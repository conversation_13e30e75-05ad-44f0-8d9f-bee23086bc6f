import {writable} from 'svelte/store';
import type {EditableTaskDto} from '$common/models/dtos/task.dto';
import {generateGuid} from '$common/core/utils';
import {TaskMode} from '$common/models/enums';

export const initialCurrentEditable: EditableTaskDto = {
    lang: 'EN',
    commentPrivate: '',
    duplicatedFromTaskId: '',
    groupId: '',
    hintsEnabled: true,
    id: '',
    isActive: true,
    isDeleted: false,
    content: '',
    page_content:{
      id:'',
      content_items:[],
      incentiveContentId:'',
      taskId:''
    },
    type: 'class',
    sentences: [],
    sentencesToChoose: [],
    sentencesToChooseCount: 0,
    date: '',
    time: '12:00',
    dateTime: '',
    isNew: true,
    commentPublic: '',
    isPublic: false,
    navigationInPublicTaskEnabled: false,
    navigationInPublicTaskOnlyAfter60: true,
    startingMode: TaskMode.translation,
    additionalTasks: {
        listen: {
            id: generateGuid(),
            mode: TaskMode.listen,
            time: 0,
            delay: 0,
            maxScore: 100,
            hintEnabled: true,
            enabled: false,
            allowAnonymous: false,
            allowRetry: false
        },
        bytime: {
            id: generateGuid(),
            mode: TaskMode.bytime,
            time: 600,
            delay: 0,
            maxScore: 100,
            hintEnabled: true,
            enabled: false,
            allowAnonymous: false,
            allowRetry: false
        },
        phantom: {
            id: generateGuid(),
            mode: TaskMode.phantom,
            time: 20,
            delay: 0,
            maxScore: 100,
            hintEnabled: true,
            enabled: false,
            allowAnonymous: false,
            allowRetry: false
        },
        audiodic: {
            id: generateGuid(),
            mode: TaskMode.audiodic,
            time: 0,
            delay: 0,
            maxScore: 100,
            hintEnabled: true,
            enabled: false,
            allowAnonymous: false,
            allowRetry: false
        },
        voice: {
            id: generateGuid(),
            mode: TaskMode.voice,
            time: 0,
            delay: 0,
            maxScore: 100,
            hintEnabled: true,
            enabled: false,
            allowAnonymous: false,
            allowRetry: false
        }
    },
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
    incentiveContent: [],
    users: [],
    hebrewFont: true,
    allowAnonymous: false
};

export const CurrentEditableState = writable({...initialCurrentEditable});

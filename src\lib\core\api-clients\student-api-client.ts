import type {ExtendedStudentDto, StudentDto} from '$common/models/dtos/student.dto';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import type {ShortStudentRequestDto} from '$common/models/dtos/student-request.dto';
import {BaseApiClient} from './base-api-client';
import type {StudentGroupsDto} from '$common/models/dtos/student-groups.dto';
import {get} from 'svelte/store';
import {StudentFilterState} from '$lib/state/student-filter-state';
import {StudentPagingState, StudentSortingState} from '$lib/state/student-paging-state';
import {StudentsRequestsPagingState} from "$lib/state/students-requests-paging-state";
import {StudentsRequestsFilterState} from "$lib/state/students-requests-filter.state";

export class StudentApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getStudents = async (fromGroup = false): Promise<TableDataDto<ExtendedStudentDto>> => {
        const currentFilterState = get(StudentFilterState);
        const currentPagingState = get(StudentPagingState);
        const currentSortingState = get(StudentSortingState);


        return await this.getDataOrThrow(
            `/api/students?onlyIsActive=${fromGroup ? false : currentFilterState.onlyIsActive}&take=${
                currentPagingState.take
            }&skip=${currentPagingState.skip}${
                currentFilterState.groupId ? `&groupId=${currentFilterState.groupId}` : ''
            }${
                currentSortingState.sortBy
                    ? `&sortBy=${currentSortingState.sortBy}&sortDir=${currentSortingState.sortDir}`
                    : ''
            }&search=${currentFilterState.search}`
        );
    };

    public getStudentRequests = async (): Promise<TableDataDto<ShortStudentRequestDto>> => {
        const currentFilterState = get(StudentsRequestsFilterState);
        const currentPagingState = get(StudentsRequestsPagingState);
        return await this.getDataOrThrow(`/api/students/requests?take=${currentPagingState.take}&skip=${currentPagingState.skip}&search=${currentFilterState.search}`);
    }

    public deleteStudentRequest = async (id: string) => {
        return await this.deleteOrThrow(`/api/students/requests?id=${id}`)
    }


    public getStudentByTZ = async (tz: string): Promise<TableDataDto<StudentDto>> =>
        await this.getDataOrThrow(`/api/students?tz=${tz}`);

    public getGroupsHistoryByStudentId = async (studentId?: string): Promise<StudentGroupsDto[]> =>
        await this.getDataOrThrow(`/api/students/groupsHistory?studentId=${studentId || ''}`);
}

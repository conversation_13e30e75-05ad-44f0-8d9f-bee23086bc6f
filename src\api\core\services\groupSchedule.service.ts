import {db} from "$api/core/service-clients/db";
import {mapper} from "$common/core/mapper";
import type {CreateUpdateGroupScheduleDto, GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
import _ from 'lodash'
import {format} from "date-fns";


export const getGroupScheduleChanges = async (groupId: string) => {

    const data = await db.groupScheduleChanges.findMany({
        where: {
            groupId
        },
        orderBy: {
            dateStart: 'asc'
        },
        include: {
            group: {
                select: {
                    timeEnd: true
                }
            }
        }
    });

    return data.map((gs) => mapper<GroupScheduleDto, unknown>(gs))
};


export const deleteGroupScheduleChanges = async (id: string) => {

    const data = await db.$transaction(async (tx) => {
        const res = await tx.groupScheduleChanges.delete({
            where: {
                id
            }
        });


        const groupChanges = await tx.groupScheduleChanges.findMany({
            where: {
                groupId: res.groupId
            },
            include: {
                group: {
                    select: {
                        name: true
                    }
                }
            }
        });

        if (groupChanges.length === 1) {
            await tx.groups.update({
                where: {
                    id: res.groupId
                },
                data: {
                    dateStart: groupChanges?.at(0)?.dateStart || new Date(),
                    name: `${format(new Date(groupChanges?.at(0)?.dateStart || new Date()), 'dd.MM.yyyy')}_${groupChanges?.at(0)?.group?.name?.split('_')?.slice(1)?.join('_')}`
                }
            })
        }

        return res;
    })


    return mapper<GroupScheduleDto, unknown>(data)
}


export const updateGroupScheduleChanges = async (dto: CreateUpdateGroupScheduleDto, updaterId: string) => {

    const data = await db.$transaction(async (tx) => {
        const res = await tx.groupScheduleChanges.update({
            where: {
                id: dto.id
            },
            data: {
                hoursPerSession: dto.hoursPerSession,
                daysSchedule: dto.daysSchedule,
                dateStart: dto.dateStart,
                updatedBy: updaterId,
            },
            select: {
                groupId: true,
                group: {
                    select: {
                        name: true
                    }
                }
            }
        });

        if (dto.updatedElementIndex === 0) {
            await tx.groups.update({
                where: {
                    id: res.groupId
                },
                data: {
                    dateStart: dto.dateStart,
                    name: `${format(new Date(dto.dateStart), 'dd.MM.yyyy')}_${res?.group?.name?.split('_')?.slice(1)?.join('_')}`
                }
            })
        }
        return res;
    })
    return mapper<GroupScheduleDto, unknown>(data)
}


export const createGroupScheduleChanges = async (dto: GroupScheduleDto, creatorId: string) => {
    const gpSchedule = _.omit({...dto}, ['group'])
    const data = await db.groupScheduleChanges.create({
        data: {
            ...gpSchedule,
            createdBy: creatorId,
            updatedBy: creatorId
        }
    })
    return mapper<GroupScheduleDto, unknown>(data)
}
import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";

export class GroupHolidaysExcApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getGroupHolidaysExceptionsByGroupId = async (groupId: string) => {
        return await this.getDataOrThrow(`/api/groupHolidaysExc?groupId=${groupId}`)

    }

    public deleteGroupHolidaysExceptions = async (groupHolidayExcId: string) => {
        return await this.deleteOrThrow(`/api/groupHolidaysExc?groupHolidayExcId=${groupHolidayExcId}`)
    }

    public postGroupHolidaysExceptions = async (dto: GroupHolidayExcDto) => {
        return await this.postDataOrThrow('/api/groupHolidaysExc', dto)
    }

}
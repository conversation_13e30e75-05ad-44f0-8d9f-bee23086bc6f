<script lang="ts">
    import {IconExternalLink} from '@tabler/icons-svelte'

    export let text;
    export let size = 'md';
    export let className = '';
    export let url = '';
    export let target = "_blank";
    export let type: 'info' | 'success' | 'error' = 'info';

    let bgStyle = type === 'info' ? 'bg-blue-100 dark:bg-gray-700' : type === 'warning' ? 'bg-warning-100 dark:bg-warning-700' : type === 'success' ? 'bg-success-200 dark:bg-success-700' : 'bg-error-100 dark:bg-error-700';
    let textStyle = type === 'info' ? 'text-blue-800 dark:text-blue-400' : type === 'warning' ? 'text-warning-800 dark:text-warning-300' : type === 'success' ? 'text-success-900 dark:text-success-300' : 'text-error-800 dark:text-error-200';
    let borderStyle = type === 'info' ? 'border-blue-400' : type === 'warning' ? 'border-warning-400' : type === 'success' ? 'border-success-400' : 'border-error-400';
</script>

<span class="infoBadge {className} w-fit p-1 {bgStyle} {textStyle} text-{size} font-medium m-0.5 px-2.5 py-0.5 rounded border {borderStyle}">
    {#if url}
	    <a href={url} target={target} class="inline-flex items-center gap-x-2 underline">
		    <IconExternalLink size="15"/>
            {text}
	    </a>
    {:else}
        {text}
    {/if}
</span>
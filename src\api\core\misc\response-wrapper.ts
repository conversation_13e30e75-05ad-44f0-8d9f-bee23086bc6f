import {json} from "@sveltejs/kit";
import appInsights from 'applicationinsights';

const isProd = process.env.NODE_ENV === 'production';

export async function wrapFunc<T>(apiFunction: (...args: any[]) => Promise<T>, ...args: any[]) {
    try {
        const data = await apiFunction();

        return json({data, success: true});
    } catch (error) {
        const user = args.at(0)?.locals?.user ?? {firstname: 'anon', lastname: 'yet'}

        if (appInsights.defaultClient) {
            appInsights.defaultClient.trackException({exception: error as Error});
        }
        console.error(error);

        return json({
            success: false,
            error: !isProd ? JSON.stringify(error) : 'api error, try one more time :)'
        });
    }
}
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {paramsToKeyValue} from "$api/core/utils";
import type {RequestEvent} from "@sveltejs/kit";
import {
    createGroupHolidayExc,
    deleteGroupHolidayExc,
    getGroupHolidaysExceptions
} from "$api/core/services/groupHolidaysExc.service";
import {mapper} from "$common/core/mapper";
import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";


export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {groupId} = paramsToKeyValue(url.searchParams);
        return await getGroupHolidaysExceptions(groupId);
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {groupHolidayExcId} = paramsToKeyValue(url.searchParams);
        return await deleteGroupHolidayExc(groupHolidayExcId);
    });


export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const groupHolidayExc = await event.request.json();
        const dto = mapper<GroupHolidayExcDto, unknown>(groupHolidayExc)
        return await createGroupHolidayExc(dto);
    });

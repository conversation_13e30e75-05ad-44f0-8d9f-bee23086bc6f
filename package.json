{"name": "hebreway-hadash", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "host": "vite --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "postinstall": "prisma generate --schema=./prisma/schema.prisma"}, "devDependencies": {"@aws-sdk/client-s3": "^3.385.0", "@skeletonlabs/skeleton": "^2.0.0", "@skeletonlabs/tw-plugin": "^0.1.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^1.22.4", "@types/bcryptjs": "^2.4.2", "@types/jquery": "^3.5.16", "@types/jsonwebtoken": "^9.0.2", "@types/node": "^20.5.7", "@types/tmp": "^0.2.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.14", "eslint": "^8.46.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-svelte": "^2.32.4", "playwright": "^1.52.0", "postcss": "^8.4.27", "postcss-load-config": "^4.0.1", "prettier": "^2.8.8", "prettier-plugin-svelte": "^2.10.1", "prisma": "^4.16.2", "sass": "^1.64.2", "svelte": "^4.1.2", "svelte-check": "^3.4.6", "svelte-preprocess": "^5.0.4", "sveltekit-superforms": "^1.5.0", "tailwindcss": "^3.3.3", "tslib": "^2.6.1", "typescript": "^5.1.6", "vite": "^4.4.8", "vite-plugin-static-copy": "^1.0.6", "zod": "^3.21.4"}, "type": "module", "dependencies": {"@ffmpeg/core": "^0.12.3", "@ffmpeg/ffmpeg": "^0.12.6", "@ffmpeg/util": "^0.12.1", "@floating-ui/dom": "^1.5.1", "@microsoft/applicationinsights-web": "^3.3.6", "@openreplay/tracker": "^14.0.4", "@openreplay/tracker-assist": "^9.0.1", "@pdfme/common": "^2.0.2", "@pdfme/generator": "^2.0.2", "@prisma/client": "^4.16.2", "@tabler/icons-svelte": "^2.30.0", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/typography": "^0.5.9", "@tinymce/tinymce-svelte": "^3.0.0", "@types/lodash": "^4.14.196", "@types/uuid": "^9.0.6", "applicationinsights": "^3.6.0", "aws-sdk": "^2.1430.0", "axios": "^1.4.0", "bcrypt": "^5.1.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.6.1", "cleave.js": "^1.6.0", "clsx": "^1.2.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "fuse.js": "^6.6.2", "jquery": "^3.7.0", "jquery-resizable-dom": "^0.35.0", "jsonwebtoken": "^9.0.1", "lodash": "^4.17.21", "microsoft-cognitiveservices-speech-sdk": "^1.43.1", "parse-nested-form-data": "^1.0.0", "redis": "^4.7.0", "redis-om": "^0.4.7", "shepherd.js": "^11.1.1", "svelte-dnd-action": "^0.9.24", "svelte-fa": "^3.0.4", "svelte-scrollto-element": "^0.7.0", "sveltekit-i18n": "^2.4.2", "svelty-picker": "^4.0.9", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.6", "theme-change": "^2.5.0", "tinymce": "^7.3.0", "trumbowyg": "^2.27.3", "uuid": "^9.0.1", "zlib": "^1.0.5"}, "packageManager": "pnpm@10.5.0+sha512.11106a5916c7406fe4b8cb8e3067974b8728f47308a4f5ac5e850304afa6f57e2847d7950dfe78877d8d36bfb401d381c4215db3a4c3547ffa63c14333a6fa51"}
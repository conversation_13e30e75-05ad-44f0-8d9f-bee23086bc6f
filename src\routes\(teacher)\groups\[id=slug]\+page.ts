import {GroupApiClient} from '$lib/core/api-clients/group-api-client';
import {StudentApiClient} from '$lib/core/api-clients/student-api-client';
import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
import {StudentFilterState} from '$lib/state/student-filter-state';
import {TaskFilterState} from '$lib/state/task-filter-state';
import {initialStudentFilter} from "$common/models/filters/student-filter.dto";
import {initialTaskFilter} from "$common/models/filters/task-filter.dto";
import type {Load} from "@sveltejs/kit";
import {initialStudentPaging, StudentPagingState} from "$lib/state/student-paging-state";
import {GroupHolidaysApiClient} from "$lib/core/api-clients/groupHolidays-api-client";
import {GeneralHolidaysApiClient} from "$lib/core/api-clients/generalHolidays-api.client";
import {GroupHolidaysExcApiClient} from "$lib/core/api-clients/groupHolidaysExc-api-client";
import {GroupScheduleApiClient} from "$lib/core/api-clients/groupSchedule-api-client";

export const ssr = false;

export const load: Load = async ({depends, params}) => {
    try {
        depends('load:groups/id');
        const {id} = params;
        StudentFilterState.set({...initialStudentFilter, groupId: id!});
        StudentPagingState.set({...initialStudentPaging, take: -1}); // get all group students
        TaskFilterState.set({...initialTaskFilter, groupId: id!});

        return {
            group: new GroupApiClient().getGroupById(id!),
            groups: new GroupApiClient().getGroups(true),
            students: new StudentApiClient().getStudents(true),
            tasks: new TaskApiClient().getTasks(),
            groupHistory: new GroupApiClient().getGroupStudents(id!),
            groupHolidays: new GroupHolidaysApiClient().getGroupHolidaysByGroupId(id!),
            generalHolidays: new GeneralHolidaysApiClient().getGeneralHolidays(),
            groupHolidaysExc: new GroupHolidaysExcApiClient().getGroupHolidaysExceptionsByGroupId(id!),
            groupScheduleChanges: new GroupScheduleApiClient().getGroupScheduleChangesByGroupId(id!),
        };
    } catch (error) {
        return error;
    }
};

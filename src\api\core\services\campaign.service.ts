import type {
    CampaignByRecipientIdDto,
    CampaignDto,
    NotificationByStudentIdDto,
    NotificationDto,
    ShortCampaignDto
} from '$common/models/dtos/notification.dto';
import {db} from '$api/core/service-clients/db';
import {generateGuid} from '$common/core/utils';
import {mapper} from '$common/core/mapper';
import type {TableDataDto} from "$common/models/dtos/table-data.dto";
import type {CampaignFilterDto} from "$common/models/filters/campaign-filter.dto";

export const createCampaign = async (campaign: CampaignDto) => {
    let notifications = {
        create: {
            id: generateGuid(),
            userId: campaign.recipientId
        }
    };

    if (campaign.type === 'group') {
        const students = await db.students.findMany({
            where: {
                currentGroup: campaign.recipientId
            }
        });
        const studentsNotification = students.map((student) => ({
            id: generateGuid(),
            userId: student.id
        }));

        notifications = {
            createMany: {
                data: studentsNotification
            }
        } as never;
    }

    const data = await db.message_campaigns.create({
        data: {
            ...campaign,
            notifications
        }
    });

    return mapper<CampaignDto, unknown>(data);
};


export const deleteCampaign = async (id: string) => {
    await db.message_campaigns.delete({
        where: {
            id
        }
    });
    return {success: true};
}

export const getAllCampaigns = async (filter: CampaignFilterDto): Promise<TableDataDto<ShortCampaignDto>> => {
    const where = composeWhereClause(filter);
    const data = await db.message_campaigns.findMany({where, take: filter.take, skip: filter.skip});
    const count = await db.message_campaigns.count({where});
    const creators = data.map((element) => element.createdBy);
    const groupsRecipientId = data
        .filter((element) => element.type === 'group')
        .map((element) => element.recipientId);
    const usersRecipientId = data
        .filter((element) => element.type === 'student')
        .map((element) => element.recipientId);

    const groups = await db.groups.findMany({
        where: {
            id: {
                in: groupsRecipientId
            },
            AND: [
                {
                    name: {
                        contains: filter.searchByRecipient
                    }
                }
            ]
        },
        select: {
            id: true,
            name: true
        }
    });

    const students = (
        await db.students.findMany({
            where: {
                id: {
                    in: usersRecipientId
                },
                AND: [
                    {
                        OR: [
                            {
                                tz: {
                                    contains: filter.searchByRecipient
                                }
                            },
                            {
                                firstname: {
                                    contains: filter.searchByRecipient
                                }
                            },
                            {
                                lastname: {
                                    contains: filter.searchByRecipient
                                }
                            }
                        ]
                    }
                ]
            },
            select: {
                id: true,
                lastname: true,
                firstname: true
            }
        })
    ).map((student) => {
        return {
            id: student.id,
            author: `${student.firstname} ${student.lastname}`
        };
    });


    const authors = (
        await db.users.findMany({
            where: {
                id: {
                    in: creators
                }
            },
            select: {
                id: true,
                firstname: true,
                lastname: true
            }
        })
    ).map((user) => {
        return {
            id: user.id,
            author: `${user.firstname} ${user.lastname}`
        };
    });

    if (filter.searchByRecipient) {
        const dtos: ShortCampaignDto[] = [];
        if (students.length > 0) {
            const result = data.filter(x =>
                students.some(student => student.id === x.recipientId)
            );
            dtos.push(...result.map((x) => {
                return mapper<ShortCampaignDto, unknown>({
                    id: x.id,
                    message: x.message,
                    createdAt: x.createdAt,
                    author: authors.find((u) => u.id === x.createdBy)?.author,
                    recipientName: students.find((user) => user.id === x.recipientId)?.author
                });
            }));
        }

        if (groups.length > 0) {
            const result = data.filter(x =>
                groups.some(group => group.id === x.recipientId)
            );
            dtos.push(...result.map((x) => {
                return mapper<ShortCampaignDto, unknown>({
                    id: x.id,
                    message: x.message,
                    createdAt: x.createdAt,
                    author: authors.find((u) => u.id === x.createdBy)?.author,
                    recipientName: groups.find((user) => user.id === x.recipientId)?.name
                });
            }));
        }

        return {data: dtos, count: dtos.length}
    } else {
        const dtos = data.map((x) => {
            return mapper<ShortCampaignDto, unknown>({
                id: x.id,
                message: x.message,
                createdAt: x.createdAt,
                author: authors.find((u) => u.id === x.createdBy)?.author,
                recipientName:
                    x.type === 'group'
                        ? groups.find((group) => group.id === x.recipientId)?.name
                        : students.find((user) => user.id === x.recipientId)?.author
            });
        });
        return {data: dtos, count}
    }
};
const composeWhereClause = (filter: CampaignFilterDto): Record<string, any> => {
    const whereClause: Record<string, any> = {};

    if (filter.type) {
        whereClause.type = filter.type;
    }

    if (filter.authorId) {
        whereClause.createdBy = filter.authorId;
    }
    return whereClause;
};

export const getAllCampaignsByRecipientId = async (id: string) => {
    const data = await db.message_campaigns.findMany({
        select: {
            createdBy: true,
            createdAt: true,
            message: true
        },
        where: {
            recipientId: id
        }
    });
    const creators = data.map((element) => element.createdBy);

    const users = (
        await db.users.findMany({
            where: {
                id: {
                    in: creators
                }
            },
            select: {
                id: true,
                firstname: true,
                lastname: true
            }
        })
    ).map((user) => {
        return {
            id: user.id,
            author: `${user.firstname} ${user.lastname}`
        };
    });

    return data.map((x) =>
        mapper<CampaignByRecipientIdDto, unknown>({
            createdAt: x.createdAt,
            message: x.message,
            author: users.find((u) => u.id === x.createdBy)?.author
        })
    );
};

export const getAllNotificationsByStudentId = async (id: string, read?: boolean) => {
    const data = await db.message_notifications.findMany({
        where: {
            userId: id,
            isRead: read
        },
        select: {
            id: true,
            campaign: {
                select: {
                    title: true,
                    type: true,
                    recipientId: true,
                    message: true,
                    createdAt: true,
                    createdBy: true
                }
            },
            campaignId: true,
            isRead: true
        }
    });

    const creators = data.map((element) => element.campaign.createdBy);
    const users = (
        await db.users.findMany({
            where: {
                id: {
                    in: creators
                }
            },
            select: {
                id: true,
                firstname: true,
                lastname: true
            }
        })
    ).map((user) => {
        return {
            id: user.id,
            author: `${user.firstname} ${user.lastname}`
        };
    });

    const studentObjectFirstNameLastName = await db.students.findUnique({
        where: {
            id
        },
        select: {
            firstname: true,
            lastname: true
        }
    });

    const groupsId = data
        .filter((element) => element.campaign.type === 'group')
        .map((element) => element.campaign.recipientId);

    const groups = await db.groups.findMany({
        where: {
            id: {
                in: groupsId
            }
        },
        select: {
            id: true,
            name: true
        }
    });

    return data.map((element) => {
        return mapper<NotificationByStudentIdDto, unknown>({
            id: element.id,
            type: element.campaign.type,
            title: element.campaign.title,
            message: element.campaign.message,
            createdAt: element.campaign.createdAt,
            isRead: data.find((notification) => element.campaignId === notification.campaignId)?.isRead,
            author: users.find((user) => user.id === element.campaign.createdBy)?.author,
            recipientName:
                element.campaign.type === 'student'
                    ? `${studentObjectFirstNameLastName?.firstname} ${studentObjectFirstNameLastName?.lastname}`
                    : groups.find((group) => element.campaign.recipientId === group.id)?.name
        });
    });
};

export const markNotificationAsReadById = async (id: string) => {
    const data = await db.message_notifications.update({
        where: {
            id
        },
        data: {
            isRead: true,
            readAt: new Date()
        }
    });

    return mapper<NotificationDto, unknown>(data);
};

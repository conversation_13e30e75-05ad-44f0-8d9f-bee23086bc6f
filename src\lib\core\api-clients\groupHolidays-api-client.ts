import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";

export class GroupHolidaysApiClient extends BaseApiClient {
    constructor() {
        super();
    }


    public getGroupHolidaysByGroupId = async (groupId: string) => {
        return await this.getDataOrThrow(`/api/groupHolidays?groupId=${groupId}`)
    }


    public postGroupHolidays = async (groupHoliday: GroupHolidayDto) => {
        return await this.postDataOrThrow('/api/groupHolidays', groupHoliday)
    }

    public deleteGroupHoliday = async (groupHolidayId: string) => {
        return await this.deleteOrThrow(`/api/groupHolidays?groupHolidayId=${groupHolidayId}`)
    }


}
<script>
    import {mapEnumToOptions} from '$lib/common/utils';
    import BaseInput from '$components/common/BaseInput.svelte';
    import BaseTimeInput from '$components/common/BaseTimeInput.svelte';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import SveltyPicker from 'svelty-picker';
    import {t} from '$lib/i18n/config';
    import {TaskFilter, TaskMode} from '$common/models/enums';
    import {createEventDispatcher} from 'svelte';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import BaseSwitch from '$components/common/BaseSwitch.svelte';
    import _ from 'lodash';

    export let groups;

    $: groupOptions = groups?.filter((g) => g.isActive)?.map((x) => {
        return {displayValue: x.name, value: x.id};
    });

    $: taskModeOptions = Object.values(TaskMode).map(mode => ({
        displayValue: $t(`tasks.taskModes.${mode}`),
        value: mode
    }));

    const dispatchUpdate = createEventDispatcher();


</script>

<div class="flex flex-col gap-4 min-w-[635px] ">
    <div class="flex gap-4">
        <div>
            <BaseSelect
                    bind:value={$CurrentEditableState.groupId}
                    on:change={() => {
					dispatchUpdate('groupChanged');
				}}
                    options={groupOptions}
                    name="currentGroup"
                    title={$t('tasks.taskGeneralInfo.group')}
                    className="h-9 p-0"
                    pulse={!$CurrentEditableState.groupId}
            />
        </div>
        {#if $CurrentEditableState.groupId && !$CurrentEditableState.isPublic}
            <div class="min-w-[110px]">
                <BaseSelect
                        bind:value={$CurrentEditableState.type}
                        on:change={() => {
                        const foundGroup = groups?.find((x) => x.id === $CurrentEditableState.groupId);
						dispatchUpdate('taskTypeChanged',foundGroup);
					}}
                        options={mapEnumToOptions(_.omit(TaskFilter,['sandbox']))}
                        name="typeTask"
                        title={$t('tasks.taskGeneralInfo.typeTask')}
                        className="h-9 p-0 "
                />
            </div>
            <div class="min-w-[100px] z-50">
                <div class="title mb-1 font-medium text-base">{$t('tasks.taskGeneralInfo.date')}</div>
                <SveltyPicker
                        name="date"
                        mode="date"
                        clearToggle={false}
                        weekStart={0}
                        bind:value={$CurrentEditableState.date}
                        inputClasses="input h-9 rounded pr-[5px]"
                />
            </div>
            <div>
                <BaseTimeInput
                        value={$CurrentEditableState.time}
                        title={$t('tasks.taskGeneralInfo.time')}
                        name="timeStart"
                        on:change={(e) => ($CurrentEditableState.time = e?.target?.value)}
                />
            </div>
        {:else if $CurrentEditableState.groupId && $CurrentEditableState.isPublic}
            <BaseSwitch bind:checked={$CurrentEditableState.allowAnonymous}
                        title={$t('tasks.taskGeneralInfo.allowAnon')}/>
            <BaseSwitch bind:checked={$CurrentEditableState.navigationInPublicTaskEnabled}
                        title={$t('tasks.taskGeneralInfo.navigationInPublicTaskEnabled')}/>
            <BaseSwitch bind:checked={$CurrentEditableState.navigationInPublicTaskOnlyAfter60}
                        title={$t('tasks.taskGeneralInfo.navigationInPublicTaskOnlyAfter60')}/>
        {:else}
            <div></div>
        {/if}
    </div>
    <div class="flex gap-4">
        <div class="min-w-[150px]">
            <BaseSelect
                    bind:value={$CurrentEditableState.startingMode}
                    options={taskModeOptions}
                    name="startingMode"
                    title={$t('tasks.taskGeneralInfo.startingMode')}
                    className="h-9 p-0"
                    on:change={() => {
					    dispatchUpdate('taskModeChanged');
				    }}

            />
        </div>
    </div>
    <BaseInput
            name="comment"
            title={$t('tasks.taskGeneralInfo.comment')}
            bind:value={$CurrentEditableState.commentPublic}
    />
</div>



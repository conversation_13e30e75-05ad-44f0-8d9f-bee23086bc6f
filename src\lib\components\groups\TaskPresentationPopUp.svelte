<script lang="ts">
	import type { SentenceInTaskDto } from '$common/models/dtos/sentence.dto';
	import BaseButton from '$components/common/BaseButton.svelte';
	import { IconChevronRight, IconChevronLeft } from '@tabler/icons-svelte';
	import { t } from '$lib/i18n/config';
	import { IconGenderFemale } from '@tabler/icons-svelte';
	import He from "$components/common/He.svelte";

	export let toggleTaskPresentationPopupVisible: () => void;
	export let taskSentences: SentenceInTaskDto[];
	export let taskLang: 'EN' | 'RU';
	export let taskFont: boolean;

	let currentSentenceIndex = 0;
	$: sentenceValue = taskSentences[currentSentenceIndex].value;
	let showSentenceValue = false;
	$: currentSentenceTranslation =
		taskSentences[currentSentenceIndex].translations.find(
			(translation) => translation.lang === taskLang
		)?.value || 'Перевод на этом языке отсутствует';
	$: dir = showSentenceValue ? 'rtl' : 'ltr';

	let onKeyDown = (e) => {
		switch (e.keyCode) {
			case 39:
				currentSentenceIndex + 1 === taskSentences.length ? '' : currentSentenceIndex++;
				break;
			case 37:
				currentSentenceIndex !== 0 ? currentSentenceIndex-- : '';
				break;
			case 32:
				showSentenceValue = !showSentenceValue;
				break;
			case 27:
				toggleTaskPresentationPopupVisible();
				break;
		}
	};
</script>

<div class="h-full w-full flex justify-center items-end p-14 variant-glass-primary">
	<div class="card shadow-sm flex flex-col w-full h-full items-center gap-5">
		{#if taskSentences[currentSentenceIndex].sex === 'f'}
			<IconGenderFemale class="absolute z-0 top-16 right-16" stroke={2} color="pink" size="140" />
		{/if}
		<div class="flex-1 flex items-center p-10">
			<h1 class="text-7xl z-50 text-center" {dir}>
				{#if showSentenceValue}
					<He bypassedFont={taskFont}>{sentenceValue}</He>
				{:else}
					{currentSentenceTranslation}
				{/if}
			</h1>
		</div>

		<div class="flex flex-col gap-10">
			<div class="flex justify-between">
				<div class="self-end">
					<BaseButton
						variant="ghost"
						className="rounded-full btn-icon-lg"
						disabled={currentSentenceIndex + 1 === taskSentences.length}
						size="lg"
						on:click={() => {
							currentSentenceIndex++;
						}}
					>
						<IconChevronRight />
					</BaseButton>
				</div>

				<p class="text-8xl font-extralight">{currentSentenceIndex + 1}</p>
				<div class="self-end">
					<BaseButton
						variant="ghost"
						className="rounded-full btn-icon-lg"
						disabled={currentSentenceIndex === 0}
						size="lg"
						on:click={() => {
							currentSentenceIndex--;
						}}
					>
						<IconChevronLeft />
					</BaseButton>
				</div>
			</div>

			<div class="mb-10 min-w-[250px]">
				<BaseButton
					className="w-full"
					variant="ghost"
					on:click={() => {
						showSentenceValue = !showSentenceValue;
					}}
				>
					{showSentenceValue
						? $t('tasks.taskPresentationPopUp.button.hideTranslation')
						: $t('tasks.taskPresentationPopUp.button.showTranslation')}
				</BaseButton>
			</div>
		</div>
	</div>
	<button
		class="btn-icon fixed top-2 right-2 z-50 font-bold shadow-xl variant-filled"
		on:click={toggleTaskPresentationPopupVisible}
		>×
	</button>
</div>

<svelte:window on:keydown|preventDefault={onKeyDown} />

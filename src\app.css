@import url('https://fonts.googleapis.com/css2?family=David+<PERSON><PERSON>&family=<PERSON>+<PERSON>uh<PERSON>+Libre:wght@300&display=swap');

@import url('https://cdnjs.cloudflare.com/ajax/libs/jodit/3.24.9/jodit.es2018.en.min.css');

@font-face {
    font-family: GveretLevinAlefAlefAlef;
    src: url('/fonts/GveretLevinAlefAlefAlef-Regular.otf') format('opentype');
}

@font-face {
    font-family: Arimo;
    font-style: normal;
    font-display: swap;
    src: url(https://fonts.gstatic.com/s/arimo/v28/P5sfzZCDf9_T_3cV7NCUECyoxNk37cxcAhrBdwcoaaQwpBQ.woff2) format('woff2');
    unicode-range: U+0590-05FF, U+200C-2010, U+20AA, U+25CC, U+FB1D-FB4F;
}

/** {*/
/* outline: 1px solid deeppink;*/
/*}*/

@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

/** {*/
/*	outline: 1px solid deeppink;*/
/*}*/


html,
body {
    @apply h-full overflow-hidden;
}

.select {
    padding-right: 3px !important;
}


:root.dark body .trumbowyg-box p,
:root.dark body .trumbowyg-box h1,
:root.dark body .trumbowyg-box h2,
:root.dark body .trumbowyg-box h3,
:root.dark body .trumbowyg-box h4,
:root.dark body .trumbowyg-box blockquote,
:root.dark body .trumbowyg-box th,
:root.dark body .trumbowyg-box td {
    filter: invert(1);
}

:root.dark body .trumbowyg-box p iframe,
:root.dark body .trumbowyg-box p img {
    filter: invert(1);
}

:root.dark body .trumbowyg-box th,
:root.dark body .trumbowyg-box td {
    border: 1px solid white;
    border: 1px solid white;
}

:root body .trumbowyg-box th,
:root body .trumbowyg-box td {
    border: 1px solid black;
    border: 1px solid black;
}


:root.dark body[data-theme='classic'] {
    background-color: rgb(var(--color-surface-900));
}

:root.dark [data-theme='classic'] #shell-header {
    background: rgb(var(--color-surface-800))
}

:root.dark [data-theme='classic'] aside {
    background: rgb(var(--color-surface-700));
    color: white;
}

:root.dark [data-theme='classic'] #aside-bar {
    background-image: none;
}

:root [data-theme='classic'] #aside-bar {
    background-image: none;
}


:root [data-theme='classic'] {
    background: #fafafa;
}

:root [data-theme='classic'] .pattern {
    background: none;
}

:root [data-theme='classic'] #page {
    background-image: none;
}

:root [data-theme='classic'] #page .card {
    background-image: none;
}

:root [data-theme='classic'] .card {
    background-image: none;
}

:root [data-theme='classic'] #page .infoBadge {
    background-color: transparent;
}

:root [data-theme='classic'] #shell-header {
    background: #00685E;
    color: white !important;
}

:root [data-theme='classic'] #shell-header .bg-surface-100-800-token {
    background-color: inherit;
}


:root [data-theme='classic'] aside {
    background: #819CA2;
    color: white;
}

:root [data-theme='classic'] aside select {
    color: black;
}


:root [data-theme='classic'] .bg-surface-200-700-token {
    background: none;
}


body {
    background-image: radial-gradient(
            at 0% 0%,
            rgba(var(--color-secondary-500) / 0.33) 0px,
            transparent 85%
    ),
    radial-gradient(at 98% 98%, rgba(var(--color-warning-100) / 0.15) 0px, transparent 90%);
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;

    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.trumbowyg-box iframe {
    width: 100%;
    min-height: 500px;
}

.trumbowyg-box .table {
    margin-bottom: var(--tbw-cell-line-height);
}

.trumbowyg-box th {
    height: calc(var(--tbw-cell-vertical-padding) * 2 + var(--tbw-cell-line-height));
    min-width: calc(var(--tbw-cell-horizontal-padding) * 2);
    padding: var(--tbw-cell-vertical-padding) var(--tbw-cell-horizontal-padding);
}

.trumbowyg-box td {
    height: calc(var(--tbw-cell-vertical-padding) * 2 + var(--tbw-cell-line-height));
    min-width: calc(var(--tbw-cell-horizontal-padding) * 2);
    padding: var(--tbw-cell-vertical-padding) var(--tbw-cell-horizontal-padding);
}

.trumbowyg-box {
    border: none !important;
}

.trumbowyg-box span{
    line-height: 38px;
}

.drawer {
    border-radius: 0px !important;
}

.aspect-square {
    aspect-ratio: 1.5 / 1 !important;
}

@tailwind components;
@tailwind utilities;

.codex-editor--narrow .ce-toolbar__actions {
    right: -65px;
}

.codex-editor__redactor {
    padding-bottom: 50px !important;
    margin-right: 0px !important;
}

.ce-block__content {
    @apply bg-surface-100-800-token rounded-container-token px-3;
}

.image-tool__caption {
    display: none;
}

.pulse-ping-animation {
    animation: pulseBtn 1s linear infinite;
    -webkit-animation: pulseBtn 1s linear infinite;
    -moz-animation: pulseBtn 1s linear infinite;
}

@keyframes pulseBtn {
    0% {
        -moz-box-shadow: 0 0 0 0 rgba(245, 5, 5, 0.9);
        box-shadow: 0 0 0 0 rgba(245, 5, 5, 0.9);
    }
    70% {
        -moz-box-shadow: 0 0 0 50px rgba(252, 71, 71, 0);
        box-shadow: 0 0 0 50px rgba(252, 71, 71, 0);
    }
    100% {
        -moz-box-shadow: 0 0 0 0 rgba(235, 100, 160, 0);
        box-shadow: 0 0 0 0 rgba(235, 100, 160, 0);
    }
}

.awesome {
    font-style: italic;
    width: 100%;
    text-align: center;
    color: #313131;
    -webkit-animation: colorchange 20s infinite alternate !important;
    -moz-animation: colorchange 20s infinite alternate !important;
    -o-animation: colorchange 20s infinite alternate !important;
    animation: colorchange 20s infinite alternate !important;
}

@-webkit-keyframes colorchange {
    0% {
        color: blue;
    }
    10% {
        color: #8e44ad;
    }
    20% {
        color: #1abc9c;
    }
    30% {
        color: #d35400;
    }
    40% {
        color: blue;
    }
    50% {
        color: #34495e;
    }
    60% {
        color: blue;
    }
    70% {
        color: #2980b9;
    }
    80% {
        color: #f1c40f;
    }
    90% {
        color: #2980b9;
    }
    100% {
        color: pink;
    }
}

.cmyk {
    color: #070788;
    text-shadow: 0.075em -0.075em rgba(255, 0, 255, 0.75), -0.075em -0.075em rgba(0, 255, 255, 0.75),
    0 0.085em rgba(255, 255, 0, 0.75);
}


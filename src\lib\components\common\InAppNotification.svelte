<script lang="ts">
    import type {InAppNotification} from '$lib/models/notification.model';
    import {IconCheck, IconExclamationCircle, IconInfoCircleFilled} from '@tabler/icons-svelte';

    export let notification: InAppNotification;

</script>

<aside class="alert variant-glass-secondary flex">
	<div class="flex justify-between w-full">
		<div class="flex">
			<div class="flex items-start">
				{#if notification.type === 'success'}
					<IconCheck class="text-success-700" size="35"/>
				{:else if notification.type ==='error'}
					<IconExclamationCircle class="text-error-700 " size="35"/>
				{:else if notification.type==='info'}
					<IconInfoCircleFilled size="35" />
				{:else}
					<IconExclamationCircle class="text-warning-700" size="35"/>
				{/if}
			</div>
		</div>
		<div class="alert-message flex self-center justify-center mx-3 w-fit">
			<h3 class="text-2xl font-bold cmyk flex" dir="auto">{notification.message}</h3>
		</div>
	</div>
</aside>

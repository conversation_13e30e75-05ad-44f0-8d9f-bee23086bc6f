<script lang="ts">
    import {intervalToDuration} from 'date-fns';
    import {onMount} from 'svelte';
    import {t} from "$lib/i18n/config";

    export let dateEnd: Date;
    let time = new Date();

    onMount(() => {
        const interval = setInterval(() => {
            time = new Date();
        }, 1000);

        return () => {
            clearInterval(interval);
        };
    });

    $: remaining = intervalToDuration({start: time, end: dateEnd});

</script>

<span dir="auto">
    <span dir="auto">{remaining.hours ?? 0}{$t('home.hours')}:{remaining.minutes ?? 0}{$t('home.minutes')}:{remaining.seconds ?? 0}{$t('home.seconds')}</span>
</span>

import {wrapFunc} from '$api/core/misc/response-wrapper';
import {deleteExclusions, getExclusions, updateExclusions} from "$api/core/services/exclusions.service";
import type {RequestEvent} from "@sveltejs/kit";
import {paramsToKeyValue} from "$api/core/utils";

export const GET = async () =>
    wrapFunc(async () => {
        return await getExclusions()
    });


export const PUT = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const exclusions = await event.request.json();

        return await updateExclusions(exclusions)
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);
        return await deleteExclusions(id)
    })

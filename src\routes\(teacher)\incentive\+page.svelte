<script lang="ts">
    import {IncentiveApiClient} from '$lib/core/api-clients/incentive-api-client.js';
    import {generateGuid} from '$common/core/utils.js';
    import IncentiveItem from '$components/incentives/IncentiveItem.svelte';
    import {invalidate} from '$app/navigation';
    import {IconCircleMinus, IconPlus} from '@tabler/icons-svelte';
    import {IncentiveState, initialIncentiveContent} from '$lib/state/incentive-state';
    import {onDestroy} from 'svelte';
    import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
    import IncentiveContentSearch from '$components/incentives/IncentiveContentSearch.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import CountSpan from '$components/common/CountSpan.svelte';
    import {pageSize} from '$lib/state/incentive-paging-state';
    import {IncentivePagingState, initialIncentivePaging} from '$lib/state/incentive-paging-state';
    import InfiniteScrollContainer from '$components/common/InfiniteScrollContainer.svelte';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {loadingWrap} from '$lib/common/utils';
    import _ from "lodash";

    export let data;



    let incentiveContent = data.incentiveContent.data || [];

    $: count = data.incentiveContent.count;
    let firstLoadFilter = true;
    const modalStore = getModalStore();

    const loadMore = async () => {
        if (count > incentiveContent.length) {
            IncentivePagingState.set({take: pageSize + incentiveContent.length, skip: 0});
            await loadingWrap(async () => {
                await invalidate('load:incentive');
            });
        }
    };

    const unsubscribeFilter = IncentiveState.subscribe(async () => {
        if (!firstLoadFilter) {
            IncentivePagingState.set({...initialIncentivePaging});
            await loadingWrap(async () => {
                await invalidate('load:incentive');
            });
        }
        firstLoadFilter = false;
    });

    const save = async (incentiveContent: IncentiveContentDto) => {
        const incentiveContentApiClient = new IncentiveApiClient();

        incentiveContent.isNew
            ? await incentiveContentApiClient.createIncentiveContent(incentiveContent)
            : await incentiveContentApiClient.updateIncentiveContent(incentiveContent);

        await loadingWrap(async () => {
            await invalidate('load:incentive');
        });
    };

    const deleteIncentiveContent = async (id: string, isNew: boolean, name: string) => {
        let modal: {
            response: (r: any) => Promise<void>;
            regionFooter: string;
            buttonTextCancel: any;
            buttonTextConfirm: any;
            type: string;
            title: any;
            body: string;
        } = {
            type: 'confirm',
            title: $t('incentiveContent.modal.title'),
            body: `${$t('incentiveContent.modal.body')} ${name}?`,
            buttonTextCancel: $t('incentiveContent.modal.buttons.cancel'),
            buttonTextConfirm: $t('incentiveContent.modal.buttons.confirm'),
            regionFooter: 'flex gap-5 ',

            response: async (r: any) => {
                if (r) {
                    if (isNew) {
                        incentiveContent = incentiveContent.filter(
                            (incentiveContent) => incentiveContent.id != id
                        );
                    } else {
                        await new IncentiveApiClient().deleteIncentiveContent(id);
                        await loadingWrap(async () => {
                            await invalidate('load:incentive');
                        });
                    }
                }
            }
        };
        modalStore.trigger(modal);
    };

    const createNewIncentiveContent = () => {
        const incentiveContentId = generateGuid();
        const pageContentId = generateGuid()
        incentiveContent = [
            {
                id: incentiveContentId,
                name: '',
                comment: '',
                value: '',
                isNew: true,
                page_content: {
                    id: pageContentId,
                    incentiveContentId,
                    content_items: []
                }
            },
            ...incentiveContent
        ];
    };


    onDestroy(() => {
        unsubscribeFilter()
        $IncentiveState = _.cloneDeep(initialIncentiveContent);

    });
</script>


<InfiniteScrollContainer loadMoreFunc={loadMore}>
    <div class=" flex flex-col gap-10 m-6">
        <h1 class="title mb-1 mt-3 font-medium text-xl">
            {$t('incentiveContent.title')}
            <CountSpan bind:count/>
        </h1>
        <div class="flex justify-between variant-glass-primary p-5">
            <div class="min-w-[400px]">
                <IncentiveContentSearch/>
            </div>
            <div class="self-end pb-1">
                <BaseButton on:click={createNewIncentiveContent}>
                    <IconPlus/>
                    {$t('incentiveContent.createButton')}
                </BaseButton>
            </div>
        </div>

        <!--        variant-glass-primary-->
        <div class=" bg-primary-200 p-14 card">
            <div class="flex flex-col gap-5">
                {#each incentiveContent as ic, i (ic.id)}
                    <div class="flex gap-5">
                        <IncentiveItem
                                id={ic.id}
                                content={ic.value}
                                pageContent={ic.page_content}
                                bind:name={ic.name}
                                bind:comment={ic.comment}
                                isNew={ic?.isNew}
                                {save}
                        />
                        <div
                                on:click={() => {
								deleteIncentiveContent(ic.id, ic.isNew, ic.name);
							}}>
                            <IconCircleMinus class="w-10 h-10 mr-2 text-red-700 cursor-pointer"/>
                        </div>
                    </div>
                {/each}
            </div>
        </div>
    </div>
</InfiniteScrollContainer>

<style>
</style>

import {z} from 'zod';

export const exclusionCreationSchema = z.object({
    id: z.string(),
    exclusions: z.string().regex(/^[\u0590-\u05FF\s\u2010\u2011\u2012\u2013\u2014\u2015'-]*[\u0590-\u05FF\u2010\u2011\u2012\u2013\u2014\u2015'-]+(?:,[\u0590-\u05FF\s\u2010\u2011\u2012\u2013\u2014\u2015'-]*[\u0590-\u05FF\u2010\u2011\u2012\u2013\u2014\u2015'-]+)*[\u0590-\u05FF\s\u2010\u2011\u2012\u2013\u2014\u2015'-]*$/, {message: 'settings.errorsCards.exclusion'})
});

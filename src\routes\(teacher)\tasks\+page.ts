import {GroupApiClient} from '$lib/core/api-clients/group-api-client';
import type {PageLoad} from './$types';
import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
import {UserApiClient} from "$lib/core/api-clients/user-api-client";

export const ssr = false;

export const load: PageLoad = async ({depends}) => {
    try {
        depends('load:tasks');
        return {
            tasks: new TaskApiClient().getTasks(),
            groups: new GroupApiClient().getGroups(true),
            users: new UserApiClient().getUsers()
        };
    } catch (error) {
        return {
            errorMessage: error
        };
    }
};

import {db} from '../service-clients/db';
import bcrypt from 'bcryptjs';
import {generateGuid} from '$common/core/utils';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import type {UserDto} from '$common/models/dtos/user.dto';
import type {FormUserDto, FormUserWithoutPasswordDto} from '$common/models/dtos/user.dto';
import {mapper} from '$common/core/mapper';
import type {UserFilterDto} from '$common/models/filters/user-filter.dto';
import _ from 'lodash';
import {Permission} from "$common/models/enums";


export const getUsers = async (filter: UserFilterDto): Promise<TableDataDto<UserDto>> => {
    const where = composeWhereClause(filter);
    const count = await db.users.count();
    const data = await db.users.findMany({
        where,
        take: filter.take === -1 ? undefined : filter.take,
        skip: filter.skip,
        select: {
            id: true,
            tz: true,
            phone: true,
            email: true,
            firstname: true,
            lastname: true,
            role: true,
            permissions: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });


    const dtos = data.map((x) => {
        const permissionsObject = x?.permissions?.reduce((acc, permission) => {
            acc[permission.permission] = true;
            return acc;
        }, {});

        return mapper<UserDto, unknown>({...x, permissions: {...permissionsObject}})
    });

    return {data: dtos, count};
};

export const updateUser = async (user: FormUserDto, updaterId: string): Promise<FormUserWithoutPasswordDto> => {
    const password = user.password ? await bcrypt.hash(user.password, 10) : undefined;
    const enumValues = Object.values(Permission);
    const permissions = user?.permissions ? (Object.keys(user?.permissions).filter(key => enumValues.includes(key as Permission)) as Permission[]) : [];

    const data = await db.$transaction(async (tx) => {
        const data = await db.users.update({
            where: {
                id: user.id
            },
            data: {..._.omit({...user, password}, 'permissions'), updatedBy: updaterId},
        });

        await db.user_permissions.deleteMany({
            where: {
                userId: user.id
            }
        })

        if (permissions.length > 0) {
            const userPermissionsData = permissions.map((p) => ({
                id: generateGuid(),
                permission: p,
                userId: data.id,
                createdBy: updaterId
            }))
            await tx.user_permissions.createMany({
                data: userPermissionsData
            })
        }
        return data;
    })


    return mapper<FormUserWithoutPasswordDto, unknown>(data);
};

export const createUser = async (user: FormUserDto, creatorId: string) => {

    const data = await db.$transaction(async (tx) => {


        const data = await tx.users.create({
            data: {
                ...{..._.omit(user, 'permissions')},
                id: generateGuid(),
                password: await bcrypt.hash(user.password, 10),
                createdBy: creatorId,
                updatedBy: creatorId
            }
        });

        if (user.permissions) {
            const objKeys = Object.keys(user.permissions);
            const enumValues = Object.values(Permission);
            const permissions = objKeys.filter(key => enumValues.includes(key as Permission)) as Permission[];
            if (permissions.length > 0) {
                const userPermissionsData = permissions.map((p) => ({
                    id: generateGuid(),
                    permission: p,
                    userId: data.id,
                    createdBy: creatorId
                }))
                await tx.user_permissions.createMany({
                    data: userPermissionsData
                })
            }
        }

        return data;
    })

    return mapper<FormUserWithoutPasswordDto, unknown>(data);
};


export const createUserPermissions = (userId: string, permissions: Permission[]) => {
    const userPermissionsData = permissions.map((p) => ({
        id: generateGuid(),
        permission: p,
        userId: userId,
        createdBy: userId
    }))
    return db.user_permissions.createMany({
        data: userPermissionsData
    })
}


export const deleteAllUserPermissions = (userId: string) => {
    return db.user_permissions.deleteMany({
        where: {
            userId
        }
    })
}

const composeWhereClause = (filter: UserFilterDto): Record<string, any> => {
    const whereClause: Record<string, any> = {};

    if (filter.search) {
        whereClause.OR = [
            {
                firstname: {
                    contains: filter.search
                }
            },
            {
                lastname: {
                    contains: filter.search
                }
            }
        ]
    }
    return whereClause;
};

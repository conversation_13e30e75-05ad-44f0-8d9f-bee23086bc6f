import type {Handle} from '@sveltejs/kit';

export const dataMiddleware: Handle = async ({ event, resolve }) => {
	event.locals.envs = {
		VITE_PHONE_IN_PDF: process.env.VITE_PHONE_IN_PDF || '',
		VITE_EMAIL_IN_PDF: process.env.VITE_EMAIL_IN_PDF || '',
		VITE_HEADER_TITLE: process.env.VITE_HEADER_TITLE || '',
		VITE_INITIAL_THEME: process.env.VITE_INITIAL_THEME || '',
		VITE_TABLES_URL: process.env.VITE_TABLES_URL || '',
		VITE_PHANTOM_TASK_SLOW_MS_TO_SYMBOL: process.env.VITE_PHANTOM_TASK_SLOW_MS_TO_SYMBOL || '',
		VITE_PHANTOM_TASK_MEDIUM_MS_TO_SYMBOL: process.env.VITE_PHANTOM_TASK_MEDIUM_MS_TO_SYMBOL || '',
		VITE_PHANTOM_TASK_FAST_MS_TO_SYMBOL: process.env.VITE_PHANTOM_TASK_FAST_MS_TO_SYMBOL || '',
		VITE_OR_KEY: process.env.VITE_OR_KEY || '',
		VITE_OR_ENDPOINT: process.env.VITE_OR_ENDPOINT || '',
		VITE_SENTENCESAUDIO_FOLDERNAME: process.env.VITE_SENTENCESAUDIO_FOLDERNAME || '',
		VITE_S3_BUCKET: process.env.VITE_S3_BUCKET || '',
		VITE_S3_REGION: process.env.VITE_S3_REGION || '',
		AZURE_SUBSCRIPTION_KEY: process.env.AZURE_SUBSCRIPTION_KEY || '',
		AZURE_REGION: process.env.AZURE_REGION || '',
	};
	
	return resolve(event);
};

import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import type {AboutContentsDto} from "$common/models/dtos/AboutContents.dto";


export class AboutContentsApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getAboutContents = async () => {
        return await this.getDataOrThrow('/api/aboutContents')
    }

    public updateAboutContent = async (data: Omit<AboutContentsDto, 'lang'>) => {
        return await this.putDataOrThrow('/api/aboutContents', {...data})
    }
}
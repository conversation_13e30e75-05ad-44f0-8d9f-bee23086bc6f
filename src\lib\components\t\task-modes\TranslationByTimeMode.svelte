<script lang="ts">
	import SentenceInTask from '$components/t/SentenceInTask.svelte';
	import type { AdditionalTaskDto, TaskResultDto } from '$common/models/dtos/task.dto';
	import BaseButton from '$components/common/BaseButton.svelte';
	import {createEventDispatcher} from 'svelte';
	import { initialDate, time } from '$lib/state/task-current-completion.state';
	import { derived } from 'svelte/store';
	import {
		checkTranslationResponse,
		formatCountdown,
		isInitialDate
	} from '$lib/common/task-helpers';
	import { t } from '$lib/i18n/config';

	const dispatcher = createEventDispatcher();

	export let model: TaskResultDto;

	export let task: AdditionalTaskDto;
	export let disabled = false;

	let isCompletionStarted = false;

	$: completions = model?.completions;
	$: revealMode = !isInitialDate(model.finishedAt, initialDate);

	$: isOngoingTask = isCompletionStarted && isInitialDate(model.finishedAt, initialDate);

	$: timer = derived(time, () => Math.round(($time - model.startedAt) / 1000));
	$: countdown =  task.time - $timer;
	$: if (countdown <= 0 && isOngoingTask) check();

	const check = () => {
		checkTranslationResponse(model, task.maxScore);
		dispatcher('submitTask');
	};

	const startTask = () => {
		if (!isCompletionStarted) {
			model.startedAt = new Date();
			isCompletionStarted = true;
		}
	}


</script>

{#if isOngoingTask}
	<div class="bg-countdown">
		{formatCountdown(countdown)}
	</div>
{/if}

{#each completions as sentence, i}
	<SentenceInTask
		index={i + 1}
		{disabled}
		{revealMode}
		hintEnabled={task.hintEnabled}
		bind:sentence
		on:inputStarted|once={startTask}
	/>
{/each}

{#if !revealMode}
	<BaseButton on:click={check} {disabled}>{$t('t.button')}</BaseButton>
{/if}

<style>
	.bg-countdown {
		font-size: 25vmin;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		display: flex;
		color: white;
		justify-content: center;
		align-items: center;
		mix-blend-mode: difference;
		z-index: -1;
		opacity: 50%;
	}
</style>

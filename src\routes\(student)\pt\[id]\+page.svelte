<script lang="ts">
    import ResultBlock from '$components/t/ResultBlock.svelte';
    import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
    import {getCertainCompletionState} from '$lib/state/task-current-completion.state';
    import _ from 'lodash';
    import {page} from '$app/stores';
    import type {CompletionTaskDto, CompletionTaskStateModel} from '$common/models/dtos/task.dto';
    import TranslationMode from '$components/t/task-modes/TranslationMode.svelte';
    import {TaskMode} from '$common/models/enums';
    import ListeningMode from '$components/t/task-modes/ListeningMode.svelte';
    import {get} from 'svelte/store';
    import {generateGuid} from '$common/core/utils';
    import TranslationByTimeMode from '$components/t/task-modes/TranslationByTimeMode.svelte';
    import PhantomDictantMode from '$components/t/task-modes/PhantomDictantMode.svelte';
    import VoiceMode from '$components/t/task-modes/VoiceMode.svelte';
    import {mergeDateTime} from '$lib/common/utils';
    import {getAveragePercentResult, getTotalAbsoluteResult, generateInitialTranslationsResult} from '$lib/common/task-helpers';
    import {t} from '$lib/i18n/config';
    import ContentEditor from "$components/content/ContentEditor.svelte";
    import {HebrewFontState} from "$lib/state/hebrew-font-state";
    import {animateScroll} from 'svelte-scrollto-element';
    import BaseInput from "$components/common/BaseInput.svelte";
    import type {PublicCompletionTaskStateModel} from "$common/models/dtos/task.dto";
    import AdditionalTasksNavigation from "$components/t/AdditionalTasksNavigation.svelte";
    import InitialAdditionalTasksNavigation from "$components/t/InitialAdditionalTasksNavigation.svelte";
    import {onMount} from "svelte";
    import {ExclusionsState} from "$lib/state/exclusions-state";
    import LexicalContent from "$components/common/LexicalContent.svelte";

    export let data;

    const customTaskMode = $page.url.searchParams.get('tm');

    let incentiveToShow;
    let top;
    let currentState = getCertainCompletionState($page.params.id, false);


    $:exclusions = data.exclusions.data;
    $: data && initAndRun(data);
    $: allowAnon = $currentState.currentMode === TaskMode.translation
        ? data.task.allowAnonymous
        : data.task.additionalTasks?.[$currentState.currentMode]?.allowAnonymous ?? true;

    $: allowRetry = $currentState.currentMode === TaskMode.translation
        ? data.task.allowRetry
        : data.task.additionalTasks?.[$currentState.currentMode]?.allowRetry ?? false;

    $: authenticatedAnon = !!($currentState as PublicCompletionTaskStateModel)?.name && ($currentState as PublicCompletionTaskStateModel)?.name?.length <= 255
        && !!($currentState as PublicCompletionTaskStateModel)?.whatsapp && ($currentState as PublicCompletionTaskStateModel)?.name?.length <= 24;

    $: allowedToStartTask = allowAnon || (!allowAnon && authenticatedAnon);

    const easeOutElastic = (x: number): number => {
        const c4 = (2 * Math.PI) / 3;

        return x === 0
            ? 0
            : x === 1
                ? 1
                : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1;
    };

    const isInitialIntermediate = (state = $currentState) => !customTaskMode && !state.currentResult && (state.task.navigationInPublicTaskEnabled && !state.task.navigationInPublicTaskOnlyAfter60);

    const initAndRun = (data) => {
        const updatedState = {
            ...$currentState,
            id: generateGuid(),
            task: data.task,
            results: []
        } as CompletionTaskStateModel;

        HebrewFontState.set(data.task.hebrewFont ?? false);

        // For initial setup, always switch to the starting mode, but preserve navigation logic
        if (!isInitialIntermediate(updatedState) || !updatedState.currentResult) {
            const targetMode = customTaskMode ? customTaskMode as TaskMode : data.task.startingMode ?? TaskMode.translation;

            // Force the mode switch by setting the mode directly and generating the result
            updatedState.currentMode = targetMode;
            updatedState.currentResult = generateInitialTranslationsResult(
                targetMode,
                updatedState.task.sentences,
                updatedState.task?.lang || 'EN'
            );
            // Only set intermediateState to false if we're truly starting fresh (no previous results)
            if (updatedState?.results?.length === 0) {
                updatedState.intermediateState = false;
            }
        }

        currentState.set(updatedState);

        incentiveToShow = _.maxBy(updatedState.task?.task_incentive_content?.filter(x => x.scoreThreshold <= updatedState.currentScore), 'scoreThreshold');
    };

    const save = async () => {
        animateScroll?.scrollTo({
            y: top.offsetTop,
            duration: 1500,
            easing: easeOutElastic,
            container: document?.getElementById('page-content')
        });

        const state = get(currentState);
        $currentState.results =
            !state?.results || state?.results?.length === 0
                ? [state.currentResult!]
                : [...state.results, state.currentResult!];

        $currentState.currentScore = getAveragePercentResult($currentState);
        $currentState.currentScoreAbsolute = getTotalAbsoluteResult($currentState);

        $currentState.intermediateState = true;


        const releaseDt = mergeDateTime($currentState.task.date, $currentState.task.time);

        const payload = {
            ..._.omit($currentState, ['task', 'currentMode', 'intermediateState', 'currentResult']),
            taskId: $page.params.id,
            taskReleaseDate: new Date(releaseDt)
        } as CompletionTaskDto;

        incentiveToShow = _.maxBy($currentState.task.task_incentive_content?.filter(x => x.scoreThreshold <= $currentState.currentScoreAbsolute), 'scoreThreshold');

        await new TaskApiClient().createTaskResult(payload);
    };

    onMount(() => {
        $ExclusionsState = [...exclusions]
    })
</script>

<div class="flex justify-center" dir="ltr">
    <div class="p-4 max-w-[760px] flex flex-col gap-5 w-full mt-10 mb-10">
        <div dir="rtl" class="text-center">
            <p dir="rtl" class="text-2xl badge font-bold ">
                {data.task.commentPublic}
            </p>
        </div>

        {#if data?.task?.content?.length > 0}
            <div class="mt-10 min-h-[30vh] w-full">
                <ContentEditor bind:content={data.task.content} readMode="true"/>
            </div>
        {:else if data?.task?.pageContent && data?.task?.pageContent.content_items.length > 0}
            <div class="pt-5 pb-10 px-0 md:px-9">
                <LexicalContent
                        isReadMode={true}
                        content={data?.task?.pageContent}
                />
            </div>
        {/if}
        <div class="w-full flex gap-4">
            <BaseInput
                    pulse={!allowAnon && !$currentState.name}
                    title={$t('t.name') + (!allowAnon ? '*' : '')}
                    bind:value={$currentState.name}
                    className="!w-1/2" dir="auto">
            </BaseInput>
            <BaseInput
                    pulse={!allowAnon && !$currentState.whatsapp}
                    title={$t('t.whatsapp') + (!allowAnon ? '*' : '')}
                    bind:value={$currentState.whatsapp}
                    className="!w-1/2"
                    dir="auto">
            </BaseInput>
        </div>

        <div class="w-full flex">
        </div>

        <div id="anchor" bind:this={top}>
            <hr>
        </div>

        {#if _.values($currentState.task.additionalTasks).some((x) => x.enabled) && $currentState.intermediateState && $currentState.task.navigationInPublicTaskEnabled}
            <AdditionalTasksNavigation useTempState={true}
                                       bypassBefore60={!$currentState.task.navigationInPublicTaskOnlyAfter60}/>
        {/if}

        {#if isInitialIntermediate() && !$currentState.currentResult}
            <InitialAdditionalTasksNavigation useTempState={true}
                                              bypassBefore60={!$currentState.task.navigationInPublicTaskOnlyAfter60}/>
        {/if}

        {#if $currentState.intermediateState}
            <ResultBlock allowRetry={allowRetry || $currentState.task.navigationInPublicTaskEnabled}
                         useTempState={true}/>
        {/if}

        {#if $currentState.currentMode === TaskMode.translation && !isInitialIntermediate()}
            <TranslationMode
                    disabled={!allowedToStartTask}
                    bind:model={$currentState.currentResult}
                    hintsEnabled={$currentState.task.hintsEnabled}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.listen && !isInitialIntermediate()}
            <ListeningMode
                    bind:model={$currentState.currentResult}
                    maxScore={$currentState.task.additionalTasks.listen.maxScore}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.bytime && !isInitialIntermediate()}
            <TranslationByTimeMode
                    disabled={!allowedToStartTask}
                    bind:model={$currentState.currentResult}
                    task={$currentState.task.additionalTasks.bytime}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.audiodic && !isInitialIntermediate()}
            <TranslationMode
                    disabled={!allowedToStartTask}
                    bind:model={$currentState.currentResult}
                    hintsEnabled={$currentState.task.additionalTasks.audiodic.hintEnabled}
                    maxScore={$currentState.task.additionalTasks.audiodic.maxScore}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.phantom && !isInitialIntermediate()}
            <PhantomDictantMode
                    disabled={!allowedToStartTask}
                    bind:model={$currentState.currentResult}
                    task={$currentState.task.additionalTasks.phantom}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.voice && !isInitialIntermediate()}
            <VoiceMode
                    disabled={!allowedToStartTask}
                    bind:model={$currentState.currentResult}
                    hintsEnabled={$currentState.task.additionalTasks.voice?.hintEnabled ?? true}
                    maxScore={$currentState.task.additionalTasks.voice?.maxScore ?? 100}
                    on:submitTask={save}
            />
        {/if}

        {#if incentiveToShow && $currentState?.intermediateState}
            <div class="mt-10 p-4">
                <p class="flex text-lg font-bold justify-center mb-5">{$t('t.inventiveTitle')}</p>
                {#if incentiveToShow?.incentive_content?.value}
                    <ContentEditor readMode={true} content={incentiveToShow.incentive_content.value}/>
                {:else if incentiveToShow?.incentive_content?.page_content}
                    <LexicalContent
                            isReadMode={true}
                            content={incentiveToShow?.incentive_content?.page_content}
                    />
                {/if}
            </div>
        {/if}
    </div>
</div>


<style>
    :global(#page-content) {
        overflow: scroll;
        height: auto;
    }
</style>

<script lang="ts">
    import {IconPlayerPlayFilled, IconPlayerPauseFilled} from '@tabler/icons-svelte';
    import {isPlay, title} from "$lib/state/audio-player.state";
    export let song;
    export let finished;
</script>

<div class="flex justify-between card rounded my-3 p-2 border-success-300-600-token border-3 items-center" dir="rtl">
    <div class="flex flex-row items-center h-fit rounded {finished ? 'bg-success-300-600-token' : ''}">
        <p class="rounded p-1 align-middle h-fit">{song.artist}.</p>
        <p class="rounded p-1">{song.title}</p>
    </div>
    <div class="flex p-2 mr-3 !bg-primary-50-900-token rounded-xl cursor-pointer btn h-fit items-center">
        <button type="button" on:click>
            {#if $isPlay && song.title === $title}
                <IconPlayerPauseFilled />
            {:else}
                <IconPlayerPlayFilled />
            {/if}
        </button>
    </div>
</div>

<style>
</style>
<script lang="ts">
    import BaseInput from "$components/common/BaseInput.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconCircleMinus, IconPlus} from "@tabler/icons-svelte";
    import {generateGuid} from "$common/core/utils";
    import {deserialize} from "$app/forms";
    import {t} from "$lib/i18n/config";
    import _ from "lodash";
    import {ExclusionsApiClient} from "$lib/core/api-clients/exclusions-api-client";
    import {loadingWrap, validateEntityBySchema} from "$lib/common/utils";
    import {exclusionCreationSchema} from "$lib/validation-schemes/exclusion";
    import ExclusionItem from "$components/settings/ExclusionItem.svelte";
    import type {ExclusionsDto} from "$common/models/dtos/Exclusions.dto";
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";
    import {invalidate} from "$app/navigation";

    export let exclusions: ExclusionsDto[];

    $: initialExclusions = _.cloneDeep(exclusions);


    let inputValue = '';
    let inputError = ''
    let updateInputError = {
        id: '',
        error: ''
    }


    async function handleSubmit(event) {
        const data = new FormData();
        let newExclusion = {
            id: generateGuid(),
            exclusions: inputValue
        }
        for (const key in newExclusion) {
            data.append(key, newExclusion[key]);
        }

        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });
        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            inputError = result.data.errors.find((error) => error.field === "exclusions")?.message
        } else {
            NotificationStore.push({
                type: NotificationType.success,
                message: t.get('settings.notifications.create')
            }, 5);
            await invalidate('load:settings')
            inputValue = ''
            inputError = ''
        }
    }


    const clearUpdateInputError = () => {
        updateInputError = {
            id: '',
            error: ''
        }
    }

    const isTheSameExclusion = (newExclusion: ExclusionsDto) => {
        return initialExclusions.find((excl => excl.id === newExclusion.id))?.exclusions === newExclusion.exclusions;
    }


    const onInput = _.debounce(
        async (exclusion: ExclusionsDto) => {
            const {result, errors} = validateEntityBySchema(exclusion, exclusionCreationSchema);
            if (!result) {
                updateInputError = {
                    id: exclusion.id,
                    error: errors.find((error) => error.field === "exclusions")?.message || ''
                }
            } else {
                if (!isTheSameExclusion(exclusion)) {
                    clearUpdateInputError();

                    let res = await loadingWrap(async () => new ExclusionsApiClient().updateExclusions(exclusion))
                    if (res) {
                        NotificationStore.push({
                            type: NotificationType.success,
                            message: t.get('settings.notifications.update')
                        }, 5);
                        await invalidate('load:settings')
                    } else {
                        NotificationStore.push({
                            type: NotificationType.error,
                            message: t.get('settings.notifications.error')
                        }, 5);
                    }
                } else {
                    clearUpdateInputError()
                }
            }
        },
        2000
    );

    const deleteExclusion = async (id: string) => {
        const res = await new ExclusionsApiClient().deleteExclusions(id);
        if (res) {
            NotificationStore.push({
                type: NotificationType.success,
                message: 'Успешно удалено'
            }, 5);
            await invalidate('load:settings')

        } else {
            NotificationStore.push({
                type: NotificationType.error,
                message: 'Произошла ошибка'
            }, 5);
        }
    }
</script>

<div class="flex flex-col gap-14">
    <div class="w-full">
        <form action={'?/createExclusion'} on:submit|preventDefault={handleSubmit} class="w-full flex gap-2">
            <div class="flex flex-col max-w-[500px] w-full">
                <BaseInput className=" w-full" bind:value={inputValue}/>
                {#if inputError}
                    <p class="text-red-600">
                        {$t(inputError)}
                    </p>
                {/if}
            </div>
            <BaseButton className="self-start" type='submit'>
                <IconPlus/>
            </BaseButton>
        </form>
    </div>
    <div class="flex flex-col gap-10 w-full">
        {#each exclusions as exclusion}
            <ExclusionItem {exclusion} {onInput}
                           {deleteExclusion}
                           updateInputError={updateInputError.id===exclusion.id?updateInputError.error:''}/>
        {/each}
    </div>
</div>
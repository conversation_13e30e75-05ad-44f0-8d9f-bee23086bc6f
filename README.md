# Документация

## 1. Описание приложения

### Hebreway - Учебное веб-приложение.

#### Описание приложения:

Hebreway представляет собой веб-приложение, специально разработанное для обучения ивритскому языку. Платформа
обеспечивает пользователям доступ к заданиям, способствующим более эффективному усвоению учебного материала, пройденного
на занятиях.

#### Вариации заданий:

Пользователи могут выполнять разнообразные типы заданий, представленных в пяти вариациях:

-Перевод

-Диктант

-Фантомный диктант

-Выполнение с ограничением по времени

-Слушание

#### Возможности для учителей:

Учителя имеют доступ к созданию заданий и могут проверять выполнение заданий студентами, используя платформу Hebreway.

#### Возможности для администратора

Администраторы имеют расширенные функциональные возможности, включая:
Создание предложений
Формирование профилей для учителей
Принятие новых студентов и назначение им определенных групп

#### Использование и функциональность

Hebreway обеспечивает удобную и эффективную среду для обучения Ивриту, предоставляя инструменты как для учителей, так и
для администраторов, чтобы улучшить процесс обучения и оценки успеваемости студентов.

## 2. Принципиальная схема приложения

<br/>
<br/>
<p align="center">
   <img src="./docs-resources/schema.png" alt="">
</p>
<br/>
<br/>

[Редактировать диаграмму](excalidraw.com/#json=68kruhXDX6onMguaU8EQ2,WqRpEifPDhkYg6hajizjSg "Редактировать")

<br/>
<br/>

1) [Sveltekit ](kit.svelte.dev "Sveltekit ")- Это фреймворк для создания веб-приложений, построенный на основе Svelte.
   Он предоставляет инструменты для разработки производительных и масштабируемых приложений, используя принципы
   компонентного подхода Svelte. SvelteKit обеспечивает удобную среду разработки, включая маршрутизацию, предварительную
   загрузку данных, серверный рендеринг и другие функции, упрощающие процесс создания современных веб-приложений.
   Frontend часть.


2) [Vercel](vercel.com "Vercel") - это облачная платформа, предназначенная для развертывания веб-приложений. Она
   предоставляет удобные инструменты для разработки, тестирования и развертывания веб-проектов. Vercel поддерживает
   различные фреймворки, такие как Next.js, React, Angular, Svelte и другие, позволяя быстро и легко развертывать
   статические сайты, одностраничные приложения (SPA) и полноценные веб-приложения. Платформа предлагает автоматическое
   масштабирование, быструю загрузку и простое управление проектами в облачной среде.На ней развернуто приложение.


3) [Netlify](netlify.com "Netlify") - это облачная платформа, специализирующаяся на разработке, развертывании и
   управлении веб-проектами. Она предоставляет разнообразные инструменты для создания, развертывания и обслуживания
   веб-сайтов и веб-приложений.


4) [Sentry](sentry.io "Sentry") - это платформа мониторинга ошибок и исследования проблем в приложениях. Она позволяет
   разработчикам отслеживать и анализировать ошибки, возникающие в их приложениях, чтобы быстро находить и исправлять
   проблемы. Sentry предоставляет детальную информацию о сбоях приложений, включая трассировку стека, информацию о
   браузере и устройстве пользователя, а также контекст, в котором произошла ошибка. Это помогает разработчикам
   оперативно реагировать на проблемы и улучшать качество своего программного обеспечения. Все ошибки, которые возникают
   в Sveltekit или Vercel записываются в Sentry.


5) [PlanetScale](planetscale.com "PlanetScale") - это распределенная база данных, разработанная для управления данными в
   масштабе облачных приложений. Она предоставляет инструменты для создания, управления и масштабирования баз данных на
   основе технологии распределенного хранения, адаптированных для облачных приложений. Основное преимущество PlanetScale
   заключается в упрощении управления данными в распределенной среде, обеспечивая высокую доступность и масштабируемость
   для приложений с большим объемом данных. Тут хранится вся информация (пользователи, задания, группы) с приложения.


6) [Momento Cache](gomomento.com "Momento Cache")- это облачный сервис, предоставляющий управляемую базу данных Redis.
   Redis - это высокопроизводительная ключ-значение база данных, используемая для хранения данных в памяти. Upstash
   предоставляет возможность создания, масштабирования и управления экземплярами Redis без необходимости заботиться о
   инфраструктуре - все это делается через облачный сервис. Это позволяет разработчикам использовать Redis для
   кэширования, обработки событий, управления сеансами пользователей и других приложений, не беспокоясь о настройке и
   поддержке инфраструктуры базы данных.


7) [ Amazon S3](aws.amazon.com " Amazon S3") (Simple Storage Service) - это услуга облачного хранилища данных,
   предоставляемая Amazon Web Services (AWS). Она предоставляет возможность хранить и извлекать любое количество данных
   в интернете. Amazon S3 позволяет хранить файлы любых размеров, от небольших файлов до огромных объемов данных, и
   обеспечивает высокую доступность, надежность и масштабируемость. Хранит все аудиофайлы с предложений, файлы которые
   прикреплены в заданиях, поощрительном контенте.

8) [Open Replay](https://openreplay.hebreway.com/) - система для записи сессий пользователей и взаимодействия в реальном времени.

## 3. Переменные окружения (.env файл)

Для корректной работы приложения необходимы следующие переменные окружения, которые при локальном запуске хранятся в
.env файле, а на сервере в конфигурации приложния

|              Ключ               |                                                          Значение по-умолчанию                                                          |                                                                                                                       Описание                                                                                                                        |
|:-------------------------------:|:---------------------------------------------------------------------------------------------------------------------------------------:|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|
|          DATABASE_URL           |                                                                sensitive                                                                |                                                                                             URL-адрес базы данных. Нужен для соединения с БД Planet Scale                                                                                             |
|     CACHE_NAME      |                                                                sensitive                                                                |                                                                                                                    Ключ к Momento                                                                                                                     |
|    MOMENTO_API_KEY     |                                                                sensitive                                                                |                                                                                            Для авторизации доступа к Momento базе данных через их REST API                                                                                            |
|          S3_ACCESS_KEY          |                                                                sensitive                                                                |                                                                        Нужен для идентификации и авторизации при доступе к данным, хранящимся в облачном хранилище Amazon S3.                                                                         |
|          S3_SECRET_KEY          |                                                                sensitive                                                                |                                                                               Это секретный ключ доступа, используемый для аутентификации запросов к сервису Amazon S3                                                                                |
|         VITE_S3_REGION          |                                                                sensitive                                                                |                                                                                                   Регион(местоположение), в котором хранятся данные                                                                                                   |
|         VITE_S3_BUCKET          |                                                                sensitive                                                                |                                                                                 Контейнер в Amazon S3, используемый для хранения файлов и данных в облачном хранилище                                                                                 |
| VITE_SENTENCESAUDIO_FOLDERNAME  |                                                                sensitive                                                                |                                                                                              Название папки, в которой хранятся файлы и данные (аудио).                                                                                               |
|       TASKFILE_FOLDERNAME       |                                                                sensitive                                                                |                                                                             Название папки, в которой харянтся файлы и данные (контент, который загружается для заданий)                                                                              |
|      AUDIOFILE_SIZE_LIMIT       |                                                                 10000000                                                                 |                                                                                                Максимальный размер для аудио файлов **в байтах**.                                                                                                 |
|  ATTACHMENT_FILE_LIMIT_SIZE_MB  |                                                                   25                                                                    |                                                                                   Максимальный размер для файлов, которые прикрепляются к заданию **в мегабайтах**.                                                                                   |
|        JWT_ACCESS_SECRET        |                                                                sensitive                                                                |                                                      Секретный ключ, используемый для подписи и верификации JWT (JSON Web Tokens) при аутентификации и авторизации пользователей в приложениях.                                                       |
| JWT_ACCESS_TOKEN_LIFETIME_MINS  |                                                                sensitive                                                                |         Определяет время жизни (срок действия) JWT Access Token **в минутах**. JWT (JSON Web Token) обычно имеет установленное время жизни, после которого он становится недействительным и требует обновления или повторной аутентификации.          |
| JWT_REFRESH_TOKEN_LIFETIME_MINS |                                                                sensitive                                                                | Определяет время жизни (срок действия) Refresh Token **в минутах** в контексте работы с JWT (JSON Web Tokens). Refresh Token используется для обновления Access Token, который, в свою очередь, предоставляет доступ к защищенным ресурсам в системе. |
|        VITE_PHONE_IN_PDF        |                                                                sensitive                                                                |                                                                      Используется в PDF, в котором хранится задание для группы, в которой состоит пользователь. (номер телефона)                                                                      |
|        VITE_EMAIL_IN_PDF        |                                                                sensitive                                                                |                                                                          Используется в PDF, в котором хранится задание для группы, в которой состоит пользователь. (имейл)                                                                           |
|        VITE_HEADER_TITLE        |                                                                Hebreway                                                                 |                                                                                                                  Заголовок в Header.                                                                                                                  |
|       VITE_INITIAL_THEME        |                                                                 classic                                                                 |                                                                                                                   Стандартная тема.                                                                                                                   |
|          VITE_OR_KEY          |  |                                                                                                                  Ключ от OpenReplay                                                                                                                   |
|          VITE_OR_ENDPOINT          | https://openreplay.hebreway.com/ingest |                                                                                                                   Адрес OpenReplay                                                                                                                    |

## 4. Доступы

|   Название   |                      URL                       | Credentials |
|:------------:|:----------------------------------------------:|:-----------:|
|    vercel    |         [Vercel](vercel.com "Vercel")          |  sensitive  |
| plannetscale |  [PlanetScale](planetscale.com "PlanetScale")  |  sensitive  |
|    sentry    |          [Sentry](sentry.io "Sentry")          |  sensitive  |
|  amazon s3   |   [ Amazon S3](aws.amazon.com " Amazon S3")    |  sensitive  |
|   momento    | [Upstash Redis](gomomento.com "Momento Cache") |  sensitive  |
|   netlify    |        [Netlify](netlify.com "Netlify")        |  sensitive  |



## 5. Статистика

Чтобы посмотреть статистику:

https://openreplay.hebreway.com/
доступ предоставлен через почту <EMAIL>

## 7. Изменение БД

После изменения файла `prisma/schema.prisma`, необходимо применить изменения в БД, для этого:
1. следует убедиться что в `.env` установлен `dev` конекшн
2. запустить команду `prisma generate` / `npx prisma generate` - это сгенерирует внутренние модели
3. запустить команду `prisma db push` / `npx prisma db push` - это применит изменения из `prisma/schema.prisma` в БД


## 7. Запуск приложения.

Для локального запуска приложения необходимо иметь файл .env в корне проекта, с указанными выше env переменными.
Команда для запуска приложения:

```bash
npm run dev
```

{"tabs": {"requests": "Запросы", "students": "Студенты"}, "requests": {"table": {"head": {"tz": "ТЗ", "fullname": "ФИО", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whatsapp": "Вот<PERSON>ап", "groupStartDate": "Дата создания группы", "learnStartDate": "Начало обучения", "lang": "Язык", "level": "Уровень", "action": "Действие", "application": "Дата подачи"}, "handleButton": "Принять", "deleteButton": "Удалить"}, "modal": {"createTitle": "Создать запрос студента", "formFields": {"titles": {"city": "Город", "groupLevel": "Уровень группы", "groupLang": "Язык группы", "groupStartDate": "Дата начала группы", "learnStartDate": "Дата начала обучения"}}, "formFieldsErrors": {"city": "Город обязателен", "groupLevel": "Уровень группы обязателен", "groupLang": "Язык группы обязателен", "groupStartDate": "Дата начала группы обязательна", "learnStartDate": "Дата начала обучения обязательна"}, "buttons": {"create": "Создать", "cancel": "Отмена"}, "previousmodalname": "Приём студента", "incompletegroup": "У этого студента есть незавершенная группа, пожалуйста установите дату окончания группы.", "studentTitle": "Данные студента из запроса", "groupTitle": "Настройки группы для нового студента", "groupSubtitle": "Настройки группы для нового студента", "levelSubtitle": "Выбранный уровень", "langSubtitle": "и язык группы", "groupStartSubtitle": "Выбранная дата начала группы", "submit": "Сохранить", "cancel": "Отменить", "group": "Группа", "dob": "Дата рождения", "groupStart": "Начало обучения", "tasksStart": "Задания с "}, "modalDelete": {"title": "Вы уверены?", "body": "Это действие удалит запрос студента", "buttonTextCancel": "Отмена", "buttonTextConfirm": "Удалить", "notification": {"success": "Запрос студента успешно удален", "cancel": "Удаление запроса студента отменено", "error": "Произошла ошибка!"}}, "notifications": {"handle": {"success": "Студент был успешно добавлен"}, "create": {"success": "Запрос студента был успешно создан"}}, "addRequestButton": "Добавить запрос"}, "students": {"filters": {"search": "Поиск", "isActive": "Только активные", "info": "Полная информация", "placeHolderSearch": "Поиск"}, "table": {"head": {"tz": "ТЗ", "fullname": "ФИО", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whatsapp": "Вот<PERSON>ап", "group": "Группа", "action": "Действие", "regDate": "Дата регистрации", "startInGroupDate": "Дата начала в группе", "comment": "Комментарий", "lastTaskResult": "Последний результат", "averageTaskResult": "Средний результат", "lastTaskDelay": "Время выполнения последнего", "averageTaskDelay": "Среднее время выполнения", "statistics": "Статистика"}, "hours": "<PERSON>а<PERSON>ы", "days": "<PERSON><PERSON>и", "editButton": "Редактировать", "copyButton": "Копировать ссылку", "duplicateButton": "Создать копию задания"}, "modal": {"previousmodalname": "Редактирование студента", "title": "Редактирование студента", "submit": "Сохранить", "cancel": "Отменить", "statistics": "Статистика", "totalHours": "Количество часов обучения", "totalDays": "Количество дней обучения", "fields": {"lastname": "Фамилия", "firstname": "Имя", "dob": "Дата рождения", "phone": "Телефон", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whatsapp": "Вот<PERSON>ап", "comment": "Комментарий", "tz": "ТЗ", "photo": "Фото", "teudatOle": "Теудат Оле", "city": "Город", "groupLevel": "Уровень группы", "groupLang": "Язык группы", "groupStartDate": "Дата начала группы", "learnStartDate": "Дата начала обучения"}, "pastDataDescription": "Если студент уже учился у нас на курсе, его прошлая информация отмечена как - *", "formFieldsErrors": {"tz": "ТЗ должен содержать от 6 до 12 символов", "firstname": "Имя должно содержать минимум 2 символа", "lastname": "Фамилия должна содержать минимум 2 символа", "email": "Имейл должен быть корректный", "phone": "Телефон должен быть корректный", "whatsapp": "Вотсап должен быть корректным", "dob": "Дата не должна быть пустая"}, "groupsHistory": {"switch": "Поменять группу", "stop": "Стоп", "table": {"totalDays": "Кол-во дней", "totalHours": "Кол-во часов", "endlearning": "Конец обучения", "starttasks": "Начало заданий", "startlearning": "Начало обучения", "group": "Группа"}}, "updateStudentGroupModal": {"edit": "Редактирование", "group": "группы", "fields": {"group": "Группа", "datestart": "Дата начала", "taskstart": "Задания  с", "dateend": "Дата конца"}}, "switchStudentGroupModal": {"breadcrumb": "Смена группы", "fromgroup": "Из группы", "fields": {"dateend": "Дата окончания", "tonewgroup": "В новую группу", "datestart": "Дата начала", "taskstart": "Задания с"}}, "stopStudentGroupModal": {"stop": "Стоп", "group": "группа", "title": "Какая дата окончания?", "fields": {"dateend": "Дата окончания"}}, "deleteStudentGroupModal": {"delete": "Удалить", "group": "группа", "title": "Вы уверены?", "buttons": {"save": "Да", "cancel": "Нет"}}, "editWarning": {"title": "Вы уверены?", "body": "Это изменение повлияет на историю группы студента", "cancel": "Отмена", "confirm": "Принять"}}, "notifications": {"edit": {"success": "Студент успешно обновлен"}, "updateStudentGroup": {"success": "Группа студента была успешно обновлена"}, "deleteStudentGroup": {"success": "Группа студента была успешно удалена"}, "stopStudentGroup": {"success": "Группа студента была успешно остановлена"}, "switchStudentGroup": {"success": "Группа студента была успешно изменена"}}}, "modalUsers": {"titles": {"titleToCreate": "Создать нового пользователя", "titleToUpdate": "Изменения данных пользователя"}}, "buttons": {"save": "Сохранить", "cancel": "Отменить"}, "messageerror": "В эти даты, студент уже учится в группе "}
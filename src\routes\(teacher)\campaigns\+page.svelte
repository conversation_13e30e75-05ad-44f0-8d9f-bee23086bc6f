<script lang="ts">
    import CountSpan from "$components/common/CountSpan.svelte";
    import {CampaignFilterState} from "$lib/state/campaign-filter-state";
    import {loadingWrap} from "$lib/common/utils";
    import {afterNavigate, beforeNavigate, invalidate} from "$app/navigation";
    import {initialCampaignFilter} from "$common/models/filters/campaign-filter.dto";
    import {t} from "$lib/i18n/config";
    import CampaignsFilter from "$components/campaigns/CampaignsFilter.svelte";
    import CampaignsTable from "$components/campaigns/CampaignsTable.svelte";
    import {CampaignPagingState, initialCampaignPaging, pageSize} from "$lib/state/campaign-paiging-state";
    import InfiniteScrollContainer from "$components/common/InfiniteScrollContainer.svelte";
    import _ from "lodash";
    import {getModalStore, type ModalSettings} from "@skeletonlabs/skeleton";
    import {CampaignApiClient} from "$lib/core/api-clients/campaign-api.client";
    import NotificationState from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";

    export let data;


    $:campaigns = data?.campaigns?.data.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    $:count = data?.campaigns?.count;
    const modalStore = getModalStore();


    const unsubscribeFilter = CampaignFilterState.subscribe(async () => {
        $CampaignPagingState = _.cloneDeep(initialCampaignPaging);
        await loadingWrap(async () => {
            await invalidate('load:campaigns');
        });
    });


    const loadMore = async () => {
        if (count > campaigns.length) {
            CampaignPagingState.set({
                skip: 0,
                take: pageSize + campaigns.length
            });
            await loadingWrap(async () => {
                await invalidate('load:campaigns');
            });
        }
    };


    afterNavigate(() => {
        $CampaignFilterState = _.cloneDeep(initialCampaignFilter)
    });

    beforeNavigate(() => {
        unsubscribeFilter();
    });

    const deleteCampaign = async (e) => {
        const modalResult = await new Promise<boolean>((resolve) => {
            const modal: ModalSettings = {
                type: 'confirm',
                title: `<h1 dir="auto">${$t('campaigns.deleteModal.title')}</h1>`,
                body: `<p dir="auto">${$t('campaigns.deleteModal.body')}</p>`,
                buttonTextConfirm: $t('campaigns.deleteModal.buttonTextConfirm'),
                buttonTextCancel: $t('campaigns.deleteModal.buttonTextCancel'),
                response: (r: boolean) => resolve(r)
            };
            modalStore.trigger(modal);
        });
        if (modalResult) {
            const result = await new CampaignApiClient().deleteCampaign(e.detail)
            if (result) {
                NotificationState.push({
                    type: NotificationType.success,
                    message: $t('campaigns.notifications.delete.success')
                });
                await invalidate('load:campaigns')
            } else {
                NotificationState.push({
                    type: NotificationType.error,
                    message: $t('campaigns.notifications.delete.error')
                });
            }
        } else {
            NotificationState.push({
                type: NotificationType.success,
                message: $t('campaigns.notifications.delete.cancel')
            })
        }
    }

</script>

<div class="h-[calc(100vh-85px)] overflow-hidden flex flex-col px-6 ">
    <div class="mt-3">
        <h1 class="title mb-1 font-medium text-xl">
            {$t('campaigns.title')}
            <CountSpan {count}/>
        </h1>
    </div>

    <CampaignsFilter users={data?.users?.data}/>

    <!--    <InfiniteScrollContainer loadMoreFunc={loadMore}>-->
    <div class="mt-3 overflow-y-auto overflow-x-auto ">
        <CampaignsTable loadMoreFunc={loadMore} on:deleteCampaign={deleteCampaign} {campaigns}/>
    </div>
    <!--    </InfiniteScrollContainer>-->
</div>

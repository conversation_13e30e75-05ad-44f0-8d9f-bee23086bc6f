import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {mapper} from "$common/core/mapper";
import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
import {
    createGeneralHoliday,
    deleteGeneralHoliday,
    getGeneralHolidays
} from "$api/core/services/generalHolidays.service";
import {paramsToKeyValue} from "$api/core/utils";


export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const generalHoliday = await event.request.json();
        const dto = mapper<GeneralHolidayDto, unknown>({...generalHoliday, date: new Date(generalHoliday.date)})
        return await createGeneralHoliday(dto)
    });


export const GET = async (event: RequestEvent) =>
    wrapFunc(async () => {
        return await getGeneralHolidays()
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);
        return await deleteGeneralHoliday(id)
    });
//S3
import {toBoolean} from "$api/core/utils";

const ACCESS: string = process.env.S3_ACCESS_KEY || '';
const SECRET: string = process.env.S3_SECRET_KEY || '';
const SENTENCESAUDIO_FOLDERNAME: string = process.env.VITE_SENTENCESAUDIO_FOLDERNAME || '';
const AUDIOFILE_SIZE_LIMIT: number = Number.parseInt(process.env.AUDIOFILE_SIZE_LIMIT || '10000000');
const ATTACHMENT_FILE_LIMIT_SIZE_MB: number = Number.parseInt(
	process.env.ATTACHMENT_FILE_LIMIT_SIZE_MB || '25'
);
const BUCKET: string = process.env.VITE_S3_BUCKET || '';
const REGION: string = process.env.VITE_S3_REGION || '';
const TASKFILE_FOLDERNAME: string = process.env.TASKFILE_FOLDERNAME || '';
const INTEGRATION_SECRET: string = process.env.INTEGRATION_SECRET || '';

//auth
const JWT_SECRET = process.env.JWT_ACCESS_SECRET || 'secret';
const JWT_ACCESS_TOKEN_LIFETIME = process.env.JWT_ACCESS_TOKEN_LIFETIME_MINS || '5';
const JWT_REFRESH_TOKEN_LIFETIME = process.env.JWT_REFRESH_TOKEN_LIFETIME_MINS || '1440';

//REDIS
const URL = process.env.REDIS_URL || '';

//CACHE
const IS_CACHE_ENABLED = toBoolean(process.env.IS_CACHE_ENABLED||false)

const HEADER = process.env.VITE_HEADER_TITLE || ''

export const config = {
	s3: {
		ACCESS,
		SECRET,
		BUCKET,
		REGION,
		SENTENCESAUDIO_FOLDERNAME,
		AUDIOFILE_SIZE_LIMIT,
		ATTACHMENT_FILE_LIMIT_SIZE_MB
	},
	auth: {
		JWT_SECRET,
		JWT_ACCESS_TOKEN_LIFETIME,
		JWT_REFRESH_TOKEN_LIFETIME
	},
	redis: {
		URL,
	},
	cache:{
		IS_CACHE_ENABLED
	},
	TASKFILE_FOLDERNAME,
	INTEGRATION_SECRET,
	
	public: {
		header: HEADER
	}
};

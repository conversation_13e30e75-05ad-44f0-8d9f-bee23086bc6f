export interface GroupScheduleDto {
    id: string,
    groupId: string,
    daysSchedule: string,
    hoursPerSession: number,
    dateStart: string | Date,
    createdBy?: string,
    updatedBy?: string,
    createdAt?: string | Date,
    updatedAt?: string | Date,
    group?: {
        timeEnd?: Date;
    }
    isNew?: true;
}


export interface CreateUpdateGroupScheduleDto extends GroupScheduleDto {
    updatedElementIndex?: number
}
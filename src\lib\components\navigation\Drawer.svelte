<script>
	import Navbar from './Navbar.svelte';
</script>

<div class="drawer drawer-end">
	<input id="drawer" type="checkbox" class="drawer-toggle" />
	<div class="drawer-content flex flex-col">
		<!-- Page content here -->
		<Navbar />
		<div class="flex">
			<div class="flex-shrink-1 flex-grow basis-auto">
				<slot />
			</div>
		</div>
	</div>
	<!--	<div class="drawer-side">-->
	<!--		<label for="drawer" class="drawer-overlay">{' '}</label>-->
	<!--		{#if $page?.data?.user.role === 'user'}-->
	<!--			<Menu isMobile={true} />-->
	<!--		{:else}-->
	<!--			<MenuStudent isMobile={true} />-->
	<!--		{/if}-->
	<!--	</div>-->
</div>

import type {HandledComplaintFilter} from "$common/models/enums";
import type {BaseFilterDto} from "$common/models/filters/base-filter.dto";


export interface ComplaintFilterDto {
    isHandled: HandledComplaintFilter | -1,
    take: number,
    skip: number,
    latestUpdater: string
}


export const initialComplaintFilter: ComplaintFilterDto = {
    isHandled: 0,
    take: 25,
    skip: 0,
    latestUpdater: ''
}
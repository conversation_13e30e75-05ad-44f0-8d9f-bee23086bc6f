<script lang="ts">
    export let mode: 'listen' | 'phantom' | 'bytime' | 'audiodic' | 'translation' | 'voice';
    import {
        IconClockHour9,
        IconEar,
        IconEyeOff,
        IconHeadphones,
        IconLanguage,

		IconMicrophone

    } from '@tabler/icons-svelte';
</script>

<div class="font-bold w-fit whitespace-nowrap rounded-[0.27rem] bg-secondary-100 px-[0.65em] pb-[0.25em] pt-[0.35em] text-center align-baseline text-[0.75em] leading-none text-secondary-800">
    {#if mode === 'listen'}
        <IconHeadphones size={22}/>
    {:else if mode === 'audiodic'}
        <IconEar size={22}/>
    {:else if mode === 'phantom'}
        <IconEyeOff size={22}/>
    {:else if mode === 'translation'}
        <IconLanguage size={22}/>
    {:else if mode === 'voice'}
        <IconMicrophone size={22}/>
    {:else}
        <IconClockHour9 size={22}/>
    {/if}
</div>


<script lang="ts">
    import {format} from "date-fns";
    import {DbFormatStringToDaysSchedule} from "$lib/common/utils.js";
    import BaseButton from "$components/common/BaseButton.svelte";
    import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
    import type {FormGroup} from "$common/models/dtos/group.dto";
    import {t} from "$lib/i18n/config";


    export let formData: FormGroup;

    let tableTitles = [
        $t(`groups.scheduleTable.tableTitles.date`),
        $t(`groups.scheduleTable.tableTitles.days`),
        $t(`groups.scheduleTable.tableTitles.hoursPerSession`),
        $t(`groups.scheduleTable.tableTitles.action`),
    ];
    export let openUpdateScheduleChangesModal: (action: 'create' | 'update', index?: number, groupSchedule?: GroupScheduleDto) => void;


    export let deleteScheduleChanges: (id: string) => void;
</script>


<div class="flex flex-col gap-5 h-full mt-5 ">
    <div class="flex justify-between items-center">
        <h1 class="title mb-1 font-medium text-xl">{$t(`groups.scheduleTable.title`)}</h1>
        <BaseButton on:click={()=>{openUpdateScheduleChangesModal('create')}}
                    className="self-start">
            +
        </BaseButton>
    </div>

    <table class="table overflow-y-auto overflow-x-auto table-hover table-compact h-fit">
        <thead on:keypress>
        <tr>
            {#each tableTitles as title}
                <th class="text-right  {title===$t(`groups.scheduleTable.tableTitles.action`)?'w-[20%]':''}">{title}</th>
            {/each}
        </tr>
        </thead>
        <tbody>
        {#each formData.groupScheduleChanges as groupSchedule,index}
            <tr>
                <td>
                    {format(new Date(groupSchedule.dateStart), 'dd.MM.yyyy')}
                </td>
                <td>
                    {DbFormatStringToDaysSchedule(groupSchedule.daysSchedule)}
                </td>
                <td>
                    {groupSchedule.hoursPerSession}
                </td>
                <td class="flex gap-2">
                    <BaseButton on:click={()=>{
                            openUpdateScheduleChangesModal('update',index,groupSchedule)
                        }}>
                        {$t(`groups.scheduleTable.buttons.edit`)}
                    </BaseButton>
                    {#if formData.groupScheduleChanges.length > 1}
                        <BaseButton className="bg-error-600 dark:!bg-error-500" on:click={()=>{
                            deleteScheduleChanges(groupSchedule.id)
                        }}>
                            Delete
                        </BaseButton>
                    {/if}
                </td>
            </tr>
        {/each}

        </tbody>
    </table>
</div>

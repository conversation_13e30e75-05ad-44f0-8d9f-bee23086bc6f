<script lang="ts">
	import { getBestResultForMode } from '$lib/common/task-helpers';
	import type { CompletionTaskDto } from '$common/models/dtos/task.dto';
	import ResultBadge from '$components/t/ResultBadge.svelte';
	import { TaskMode } from '$common/models/enums';
	import TaskModeIcon from '$components/t/TaskModeIcon.svelte';

	export let results;
	export let currentSelectedStudentId: string;
	export let calculateDoneIn: (result: CompletionTaskDto, mode?: string) => string;

	$: resultStudent = results?.find((r) => r.studentId === currentSelectedStudentId);
</script>

<td class="!bg-transparent"></td>
<td colspan="4" class="!p-0 m-0">
	<table class="table !p-0 !m-0">
		<tbody>
			{#each Object.values(TaskMode) as r}
				{@const countDoneByMode = calculateDoneIn(resultStudent, r)}
				{@const bestResult = getBestResultForMode(r, resultStudent)}
				{@const attemptsForMode = resultStudent.results?.filter((x) => x.mode === r)?.length ?? 0}
				<tr class="!card !m-0 !p-0">
					<td class="card w-1/4"
						><div class="w-full flex"><TaskModeIcon mode={r} className="ml-2" /> {r}</div></td
					>
					<td class="card w-[29%]">{countDoneByMode}</td>
					<td class="card w-[20%] justify-center">
						<ResultBadge result={bestResult} />
					</td>
					<td class="card w-6">{attemptsForMode}</td>
				</tr>
			{/each}
		</tbody>
	</table>
</td>

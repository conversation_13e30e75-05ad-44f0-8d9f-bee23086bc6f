<script lang="ts">
    import BaseSwitch from "$components/common/BaseSwitch.svelte";
    import BaseInput from "$components/common/BaseInput.svelte";
    import axios from "axios";
    import {page} from "$app/stores";
    import _ from "lodash";
    import {format} from "date-fns";
    import {invalidate} from "$app/navigation";


    export let linkDailySurvey = '';
    export let linkRareSurvey = '';

    export let rareSurveyDate: Date | null = new Date();

    $:date = rareSurveyDate ? format(rareSurveyDate, 'yyyy-MM-dd') : null;

    let isActiveDailySurvey = !!linkDailySurvey;
    let isActiveRareSurvey = !!linkRareSurvey;


    const onChangeDailySwitch = () => {
        if (!isActiveDailySurvey) {
            linkDailySurvey = ''
            onInputDaily();
        }
    }

    const onChangeRareSwitch = () => {
        if (!isActiveRareSurvey) {
            linkRareSurvey = ''
            rareSurveyDate = null;
            onInputRare()
        }
    }


    const onInputDaily = _.debounce(
        async () => {
            const res = await axios.put('/api/surveys/daily', {
                dailySurveyUrl: linkDailySurvey,
                groupId: $page?.params?.id
            })
            if (res.data) {
                await invalidate('load:groups/id')
            }
            return res;
        }, 1000
    );


    const onInputRare = _.debounce(
        async () => {
            const res = await axios.put('/api/surveys/rare', {
                rareSurveyUrl: linkRareSurvey,
                groupId: $page?.params?.id,
                dateRareSurvey: date
            });
            if (res.data) {
                await invalidate('load:groups/id')
            }
            return res;
        }, 1000
    );

    const onChangeDate = (value: string) => {
        date = format(new Date(value), 'yyyy-MM-dd');
        onInputRare()
    }


</script>

<div class="flex flex-col gap-5">
    <div class="flex gap-20" dir="rtl">
        <div class="flex flex-col gap-1 w-[10%]">
            <div class="title font-medium text-base">Daily survey</div>
            <BaseSwitch bind:checked={isActiveDailySurvey} on:change={onChangeDailySwitch}/>
        </div>
        <div class="w-[30%] {isActiveDailySurvey?'':'invisible'}">
            <BaseInput title="Link" placeHolder="Insert a link to a daily survey" bind:value={linkDailySurvey}
                       on:input={onInputDaily}/>
        </div>
    </div>
    <div class="flex gap-[73px]" dir="rtl">
        <div class="flex flex-col gap-1 w-[12%]">
            <div class="title font-medium text-base">Rare survey</div>
            <BaseSwitch bind:checked={isActiveRareSurvey} on:change={onChangeRareSwitch}/>
        </div>
        {#if isActiveRareSurvey}
            <div class="w-full flex  items-end">
                <div class="w-[35%] ml-5">
                    <BaseInput className="" title="Link" placeHolder="Insert a link to a rare survey"
                               bind:value={linkRareSurvey}
                               on:input={onInputRare}
                    />
                </div>
                <div class="w-[15%] flex flex-col gap-1">
                    <div class="title font-medium text-base">Date</div>
                    <input dir="rtl" class="input h-[43px] rounded pr-[5px] w-[70%]"
                           on:change={(e)=>onChangeDate(e.target.value)}
                           value={date}
                           type="date">
                </div>
            </div>
        {/if}
    </div>
</div>

<script lang="ts">
    import '../app.css';
    import {Constants} from '$api/core/constants';
    import InAppNotificationsContainer from '$lib/components/common/InAppNotificationsContainer.svelte';
    import {AppShell, Drawer, Modal} from '@skeletonlabs/skeleton';
    import Navbar from '$lib/components/navigation/Navbar.svelte';
    import Menu from '$lib/components/navigation/Menu.svelte';
    import MenuStudent from '$components/navigation/MenuStudent.svelte';
    import AcceptStudentModal from '$components/student-requests/AcceptStudentModal.svelte';
    import CreateStudentRequestModal from '$components/student-requests/CreateStudentRequestModal.svelte';
    import UpdateStudentModal from '$components/students/UpdateStudentModal.svelte';
    import SendNotificationModal from '$components/notifications/SendNotificationModal.svelte';
    import CreateGroupModal from '$components/groups/CreateUpdateGroupModal.svelte';
    import CreateUpdateUserModal from '$components/users/CreateUpdateUserModal.svelte';
    import TaskSentenceSearchPopUp from '$components/tasks/TaskSentenceSearchPopUp.svelte';
    import {browser} from '$app/environment';
    import {onMount} from 'svelte';
    import {loadTranslations} from '$lib/i18n/config';
    import {computePosition, autoUpdate, offset, shift, flip, arrow} from '@floating-ui/dom';
    import {storePopup} from '@skeletonlabs/skeleton';
    import UpdateStudentGroupModal from '$components/students/UpdateStudentGroupModal.svelte';
    import DeleteStudentGroupModal from '$components/students/DeleteStudentGroupModal.svelte';
    import StopStudentGroupModal from '$components/students/StopStudentGroupModal.svelte';
    import SwitchStudentGroupModal from '$components/students/SwitchStudentGroupModal.svelte';
    import StopGroupModal from '$components/groups/StopGroupModal.svelte';
    import {config} from 'svelty-picker';
    import PageTransition from '$components/common/PageTransition.svelte';
    import type {LayoutServerData} from './$types';
    import ResultDetailsPopup from '$components/groups/ResultDetailsPopup.svelte';
    import {initializeStores} from '@skeletonlabs/skeleton';
    import {AppThemeState} from '$lib/state/app-theme-state';
    import {LocaleState} from "$lib/state/locale.state";
    import TaskSentenceModalConfirm from "$components/tasks/TaskSentenceModalConfirm.svelte";
    import {HebrewFontState} from "$lib/state/hebrew-font-state";
    import ComplaintSentenceModal from "$components/t/task-modes/ComplaintSentenceModal.svelte";
    import SaveSentenceModal from "$components/sentences/SaveSentenceModal.svelte";
    import TaskGenerateModal from "$components/tasks/TaskGenerateModal.svelte";
    import UpdateScheduleChangesModal from "$components/groups/UpdateScheduleChangesModal.svelte";
    import {page} from "$app/stores";
    import Tracker from '@openreplay/tracker';
    import trackerAssist from '@openreplay/tracker-assist';

    initializeStores();

    export let data: LayoutServerData;

    config.format = 'yyyy-mm-dd';
    config.displayFormat = 'dd.mm.yyyy';
    config.weekStart = 0;

    storePopup.set({computePosition, autoUpdate, offset, shift, flip, arrow});

    onMount(async () => {
        await loadTranslations($LocaleState);
    });

    let user;

    $: {
        if (browser) {
            document.body.setAttribute('data-theme', $AppThemeState);
            if($page?.data?.user?.id){
                localStorage.setItem('userId', $page.data.user.id)
            }

            if($page?.data?.user?.tz){
                localStorage.setItem('tz', $page.data.user.tz)
            }
        }
    }

    $: if (data?.user) {
        user = data.user;
        const firstname = data?.user?.firstname;
        const lastname = data?.user?.lastname;
        if (user.role !== Constants.StudentRole) {
            $HebrewFontState = true;
        }
        let roleForOr = user.role !== Constants.StudentRole ? 'Teacher' : 'Student'

        console.log($page?.data)
        //open replay
        if (!!$page.data.envs.VITE_OR_KEY && !!$page.data.envs.VITE_OR_ENDPOINT) {
            setTimeout(async () => {
                try {
                    if (browser && ($page?.data?.user?.tz || localStorage.getItem('tz'))) {
                        if (!window.tracker) {

                            window.tracker = new Tracker({
                                projectKey: $page.data.envs.VITE_OR_KEY ,
                                ingestPoint: $page.data.envs.VITE_OR_ENDPOINT,
                                capturePerformance: true,
                                obscureInputDates: false,
                                obscureInputEmails: false,
                                obscureTextNumbers: false,
                                obscureInputNumbers: false,
                                obscureTextEmails: false,
                                defaultInputMode: 0,
                                network: {
                                    capturePayload: true,
                                    failuresOnly: false,
                                    ignoreHeaders: true
                                } as any
                            });

                            const tz = $page?.data?.user?.tz ? $page?.data?.user?.tz : localStorage.getItem('tz')

                            window.tracker.use(trackerAssist());
                            await window.tracker.start( {userID: `${roleForOr} ${firstname} ${lastname} (${tz})` ?? 'anon'} );
                        }
                    }
                } catch (e) {
                    console.error(e);
                }
            }, 500)
        }

    }

    const modalComponentRegistry = {
        acceptStudentModal: {ref: AcceptStudentModal},
        createStudentRequestModal: {ref: CreateStudentRequestModal},
        updateStudentModal: {ref: UpdateStudentModal},
        updateStudentGroupModal: {ref: UpdateStudentGroupModal},
        deleteStudentGroupModal: {ref: DeleteStudentGroupModal},
        stopStudentGroupModal: {ref: StopStudentGroupModal},
        stopGroupModal: {ref: StopGroupModal},
        switchStudentGroupModal: {ref: SwitchStudentGroupModal},
        sendNotificationStudentModal: {ref: SendNotificationModal},
        createGroupModal: {ref: CreateGroupModal},
        sendNotificationGroupModal: {ref: SendNotificationModal},
        modalUsers: {ref: CreateUpdateUserModal},
        taskSentenceSearchPopUp: {ref: TaskSentenceSearchPopUp},
        resultDetailsPopup: {ref: ResultDetailsPopup},
        taskSentenceModalConfirm: {ref: TaskSentenceModalConfirm},
        complaintSentenceModal: {ref: ComplaintSentenceModal},
        saveSentenceModal: {ref: SaveSentenceModal},
        taskGenerateModal: {ref: TaskGenerateModal},
        updateScheduleChangesModal: {ref: UpdateScheduleChangesModal}
    };
</script>

<svelte:head>
    <title>Hebreway - Ulpan Morasha</title>
</svelte:head>

<svelte:body data-theme={'hamlindigo'}/>

<Modal
        regionBackdrop="variant-glass-primary"
        components={modalComponentRegistry}
        regionBody="text-lg direction-auto"
        regionFooter="flex justify-between"
        buttonNeutral="btn variant-ghost-surface rounded-none "
        buttonPositive="btn variant-filled rounded-none"
        padding="px-8 py-5"
/>

{#if data?.user}
    <Drawer position="right" width="w-52" class="block !rounded-none" duration="0">
        {#if user && (user?.role === Constants.TeacherRole || user?.role === Constants.AdminRole)}
            <Menu/>
        {:else if user && user?.role === Constants.StudentRole}
            <MenuStudent/>
        {/if}
    </Drawer>
{/if}

<AppShell
        regionPage="relative"
        slotSidebarRight="xs:hidden {!data?.user ? 'hidden' : 'md:flex flex-row justify-between w-40'}"
>
    <svelte:fragment slot="header">
        <Navbar fullname={`${user?.firstname || ''} ${user?.lastname || ''}`}/>
    </svelte:fragment>
    <svelte:fragment slot="sidebarRight">
        {#if data?.user}
            {#if (user && user?.role === Constants.TeacherRole) || user?.role === Constants.AdminRole}
                <Menu/>
            {:else if user?.role === Constants.StudentRole}
                <MenuStudent/>
            {/if}
        {/if}
    </svelte:fragment>

    <PageTransition pathname={data?.pathname}>
        <slot/>
    </PageTransition>

    <InAppNotificationsContainer/>
</AppShell>

<style>
    :global(#page, .card) {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%239C92AC' fill-opacity='0.25' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E");
    }

    :global(#aside-bar) {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%239C92AC' fill-opacity='0.55' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E");
    }
</style>

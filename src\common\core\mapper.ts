export const mapper = <T, K>(object: K): T => {
    let obj = {} as T;

    // @ts-ignore
    Object.keys(object).map(key => {
        if (instanceOf<T>(object, key)) {
            // @ts-ignore
            obj[key] = object[key];
        }
    });

    return obj;
};

function instanceOf<T>(object: any, key: string): object is T {
    return key in object;
}

export const objectWithoutKey = (object: any, key: string) => {
    const {[key]: deletedKey, ...otherKeys} = object;
    
    return otherKeys;
}

import {get} from 'svelte/store';
import {BaseApiClient} from './base-api-client';
import {GroupFilterState} from '$lib/state/group-filter-state';
import type {GroupDto} from '$common/models/dtos/group.dto';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import {GroupPagingState, GroupSortingState} from '$lib/state/group-paging-state';
import type {GroupHistoryDto} from "$common/models/dtos/group-history.dto";
import {initialGroupFilterAll} from "$common/models/filters/group-filter.dto";

export class GroupApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public async getGroups(getAllGroups?: boolean): Promise<TableDataDto<GroupDto>> {
        const {
            name,
            hoursSchedule,
            level,
            lang,
            isActive,
            isPublic
        } = getAllGroups ? initialGroupFilterAll : get(GroupFilterState);
        const {take, skip} = getAllGroups ? initialGroupFilterAll : get(GroupPagingState);

        const currentSortingState = get(GroupSortingState);

        return await this.getDataOrThrow(
            `/api/groups?take=${take}&skip=${skip}&name=${name}&hoursSchedule=${hoursSchedule}&level=${level}&lang=${lang}&isActive=${isActive}&isPublic=${isPublic}${
                currentSortingState.sortBy
                    ? `&sortBy=${currentSortingState.sortBy}&sortDir=${currentSortingState.sortDir}`
                    : ''
            }`
        );
    }

    public getGroupById = async (id: string): Promise<GroupDto> =>
        await this.getDataOrThrow(`/api/groups/group?id=${id}`);


    public getGroupStudents = async (id: string): Promise<TableDataDto<GroupHistoryDto>> =>
        await this.getDataOrThrow(`/api/groups/students?id=${id}`);

}

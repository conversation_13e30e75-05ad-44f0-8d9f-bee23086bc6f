<script lang="ts">
    import {StudentAcceptModalState} from '$lib/state/student-accept-state';
    import {format} from "date-fns";

    export let keyName: string;
</script>

{#if $StudentAcceptModalState.studentPastData && $StudentAcceptModalState.studentPastData.hasOwnProperty(keyName)}
    {#if keyName === 'dob'}
		<span class="text-warning-500"
        >
<!--			*{new Date($StudentAcceptModalState.studentPastData[keyName]).toLocaleDateString()}-->
			*{format(new Date($StudentAcceptModalState.studentPastData[keyName]), 'dd.MM.yyyy')}
		</span
        >
    {:else}
		<span class="text-warning-500"
        >*{$StudentAcceptModalState.studentPastData[keyName] || 'This value was empty'}</span
        >
    {/if}
{/if}

import type {SentenceDto} from "$common/models/dtos/sentence.dto";

export interface ComplaintDto {
    id: string,
    comment: string,
    sentenceId: string,
    taskId: string,
    isHandled: boolean,
    createdBy: string,
    createdAt: Date,
    sentence: SentenceDto
    task_sentences:{
        sentence:SentenceDto,
        task: 'EN' | 'RU'
    }
}


export type CreateComplaintDto = Omit<ComplaintDto, 'id ' | 'createdAt' | 'isHandled' | 'sentence'>;
{"title": "משתמשים", "addUserButton": "הוסף משתמש", "placeHolderSearch": "חיפוש", "table": {"head": {"tz": "תעודת זהות", "firstname": "שם פרטי", "lastname": "שם משפחה", "email": "דוא''ל", "phone": "טלפון", "edit": "עריכה"}, "editButton": "ערוך"}, "modalUsers": {"titles": {"titleToCreate": "יצירת משתמש חדש", "titleToUpdate": "עדכון פרטי משתמש", "titlePermissions": "Разрешения"}, "submitButton": {"update": "שמור", "create": "צור"}, "formFields": {"firstname": "שם פרטי", "lastname": "שם משפחה", "phone": "טלפון", "email": "דוא''ל", "password": "סיסמה", "tz": "תעודת זהות", "role": "תפקיד", "permissions": {"editFavorites": "Изменять избранные предложения", "deleteNonFavSentences": "Удалять предложения"}}, "formFieldsErrors": {"tzMin": "מספר תעודת זהות חייב לכלול 9 ספרות", "tzMax": "ניתן להקליד עד 10 ספרות", "firstname": "שם פרטי חייב לכלול שתי אותיות לפחות", "lastname": "שם משפחה חייב לכלול שתי אותיות לפחות", "emailLength": "דו<PERSON>''ל חייב לכלול שתי אותיות לפחות", "email": "דו<PERSON>''ל חייב לכלול שתי אותיות לפחות", "phone": "מס' טלפון חייב לכלול עשר ספרות לפחות", "passwordMin": "סיסמה חייבת לכלול 5 תוים לפחות", "passwordLetter": "הסיסמה חייבת לכלול אות אחת לפחות", "passwordDigit": "הסיסמה חייבת לכלול ספרה אחת לפחות", "passwordToUpdate": "על הסיסמה להיות בעלת 5 תוים ולהכיל: אות לועזית אחת, ספרה אחת"}, "selectFields": {"role": {"teacher": "מורה", "admin": "מנהל", "disabled": "מושבת"}}, "notifications": {"create": {"success": "Пользователь успешно создан"}, "update": {"success": "Пользователь успешно обновлен"}}}}
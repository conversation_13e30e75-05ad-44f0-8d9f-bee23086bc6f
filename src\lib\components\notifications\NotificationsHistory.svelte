<script lang="ts">
	import type { CampaignDto } from '$common/models/dtos/notification.dto';
	import { format } from 'date-fns';
	import { getTimeFromDateString } from '$lib/common/utils';

	export let userNotificationsHistory: CampaignDto[];
</script>

<div class="flex flex-col gap-4 mt-4 variant-glass-primary p-2 max-h-[180px] overflow-hidden">
	<div class=" flex gap-5">
		<p class="min-w-[130px] font-bold">Дата</p>
		<span class="divider-vertical"></span>
		<p class="min-w-[100px] font-bold">Автор</p>
		<span class="divider-vertical"></span>
		<p class="flex-1 font-bold w-full text-center">Сообщение</p>
	</div>
	<div class="flex flex-col gap-4 break-all overflow-y-auto">
		{#each userNotificationsHistory as notification}
			<div class="flex gap-5">
				<p class="min-w-[150px]">
					{format(new Date(notification.createdAt), 'dd/MM/yy HH:mm')}
				</p>
				<p class="min-w-[130px]">{notification.author}</p>
				<p class="flex-1 p-1 w-full" dir="auto">
					{notification.message.length > 50
						? `${notification.message.slice(0, 50)}....`
						: notification.message}
				</p>
			</div>
		{/each}
	</div>
</div>

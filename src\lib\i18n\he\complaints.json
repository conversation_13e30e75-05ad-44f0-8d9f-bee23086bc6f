{"title": "תלונות", "editButton": "עריכה", "handledButton": "טופל", "tabs": {"complaints": "Ж<PERSON><PERSON><PERSON><PERSON>ы", "statistic": "Статистика жалоб"}, "table": {"titles": {"sentence": "מש<PERSON>ט", "task": "תרגיל", "actions": "פעולה", "complaints": "מ<PERSON>' תלונות", "description": "תיאור", "latestUpdater": "Последнее обновление"}, "student": "תלמיד"}, "modal": {"title": "דווח על הטעות", "placeholder": "כתוב פה", "cancelButton": "ביטול", "confirmButton": "שלח"}, "filter": {"handledComplaintFilter": {"none": "–", "handled": "טופל", "notHandled": "ממת<PERSON>ן לטיפול"}, "titleHandled": "טופל", "titleUpdater": "Последнее обновление"}, "notifications": {"toComplain": {"success": "ההודעה נשלחה", "error": "!משהו השתבש"}, "handled": {"success": "נש<PERSON>ר", "error": "!משהו השתבש"}}, "statistic": {"title": "Статистика жалоб", "filter": {"titleUpdate": "Последнее обновление"}, "table": {"teacherName": "Имя учителя", "numberOfComplaints": "Кол-во жалоб", "numberOfSentences": "Кол-во предложений", "numberOfSentencesHandled": "Кол-во обработанных предложений", "numberOfSentencesNotHandled": "Кол-во необработанных предложений", "sentences": "Предложения"}}}
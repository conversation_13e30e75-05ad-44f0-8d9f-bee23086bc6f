import {wrapFunc} from "$api/core/misc/response-wrapper";
import {paramsToKeyValue} from "$api/core/utils";
import {getGroupsHistoryByStudentId} from "$api/core/services/students.service";
import type {RequestEvent} from "@sveltejs/kit";


export const GET = async ({url, locals}: RequestEvent) => wrapFunc(async () => {
    const {studentId} = paramsToKeyValue(url.searchParams);
    const id = studentId || locals.user.id;
    return await getGroupsHistoryByStudentId(id);
})


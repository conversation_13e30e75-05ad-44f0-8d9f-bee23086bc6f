import {db} from '../service-clients/db';
import type {GroupFilterDto} from '$common/models/filters/group-filter.dto';
import type {GroupDto, StopGroupDto} from '$common/models/dtos/group.dto';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import {mapper} from '$common/core/mapper';
import {toBoolean} from '../utils';
import type {BaseSortingDto} from "$common/models/filters/base-filter.dto";
import type {GroupHistoryDto} from "$common/models/dtos/group-history.dto";
import {
    addHours,
    addMinutes,
    intervalToDuration,
    isAfter,
    isSameDay,
    isWithinInterval,
    set,
    subDays,
    subHours
} from "date-fns";
import {generateGuid} from "$common/core/utils";
import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
import type {DailySurveyDto} from "$common/models/dtos/daily-survey.dto";
import type {RareSurveyDto} from "$common/models/dtos/rare-survey.dto";
import {calculateGroupEndDay, isItStudyDay, isWeekend} from "$lib/common/utils";
import {SurveyStatus} from "$common/models/enums";
import type {CreateStudentSurveyDto} from "$common/models/dtos/CreateStudentSurvey.dto";


export const getGroups = async (filter: GroupFilterDto, creatorId: string, sort?: BaseSortingDto): Promise<TableDataDto<GroupDto>> => {
    const where = composeWhereClause(filter);
    const count = await db.groups.count({where});

    const data = await db.$transaction(async (tx) => {
        const groups = await tx.groups.findMany({
            where,
            take: filter.take === -1 ? undefined : filter.take,
            skip: filter.skip,
            include: {
                studentsGroups: {
                    select: {
                        student: true
                    }
                },
                _count: {
                    select: {
                        studentsGroups: {
                            where: {
                                dateEndActual: null
                            }
                        }
                    }
                },
                groupScheduleChanges: {
                    include: {
                        group: {
                            select: {
                                timeEnd: true
                            }
                        }
                    }
                },
                groupHolidaysExceptions: true,
                groupHoliday: true
            },
            orderBy: sort ? {[sort.sortBy!]: sort?.sortDir} : undefined
        });


        const groupWithoutScheduleChanges: GroupScheduleDto[] = groups.filter((g) => g.groupScheduleChanges.length === 0 && !g.isPublic).map((g) => {
            const hoursPerSession = g.hoursSchedule === 0 || g.hoursSchedule === 1 ? 5 : 4;
            const {daysSchedule, dateStart} = g;
            return {
                id: generateGuid(),
                daysSchedule,
                hoursPerSession,
                groupId: g.id,
                createdAt: dateStart,
                updatedBy: creatorId,
                createdBy: creatorId
            }
        })


        await tx.groupScheduleChanges.createMany({
            data: groupWithoutScheduleChanges
        })


        return groups;
    }, {
        maxWait: 5000,
        timeout: 10000
    })


    const dtos = data.map((x) =>
        mapper<GroupDto, unknown>({...x, studentsCount: x._count?.studentsGroups}),
    );


    return {data: dtos, count};
};

export const stopGroup = async (dto: StopGroupDto) => {
    const {id, dateEnd} = dto;
    const data = await db.$transaction(async (tx) => {
        await tx.students.updateMany({
            where: {
                currentGroup: {
                    equals: id
                }
            },
            data: {
                currentGroup: null
            }
        });
        await tx.students_groups.updateMany({
            where: {
                groupId: {
                    equals: id
                },
                dateEndActual: {
                    equals: null
                }
            },
            data: {
                dateEndActual: new Date(dateEnd)
            }
        });
        return await tx.groups.update({
            where: {
                id
            },
            data: {
                dateEnd: new Date(dateEnd),
                isActive: false
            }
        });
    });
    return mapper<GroupDto, unknown>(data);
};

const composeWhereClause = (filter: GroupFilterDto): Record<string, unknown> => {
    const whereClause: Record<string, unknown> = {};

    if (filter.lang !== -1) {
        whereClause.lang = filter.lang;
    }

    if (filter.hoursSchedule > -1) {
        whereClause.hoursSchedule = filter.hoursSchedule;
    }

    if (filter.isActive > -1) {
        whereClause.isActive = toBoolean(filter.isActive);
    }

    if (filter.isPublic > -1) {
        whereClause.isPublic = toBoolean(filter.isPublic);
    }

    if (filter.name) {
        whereClause.name = {contains: filter.name};
    }

    if (filter.level > -1) {
        whereClause.level = filter.level;
    }

    return whereClause;
};

export const createGroup = async (group: GroupDto, creatorId: string): Promise<GroupDto> => {

    const data = db.$transaction(async (tx) => {
        const data = await tx.groups.create({
            data: {
                ...group,
                createdBy: creatorId,
                updatedBy: creatorId
            }
        });

        await tx.groupScheduleChanges.create({
            data: {
                id: generateGuid(),
                groupId: data.id,
                daysSchedule: data.daysSchedule,
                hoursPerSession: data.hoursSpendBySession,
                dateStart: data.dateStart,
                createdBy: creatorId,
                updatedBy: creatorId
            }
        });

        return data;
    })

    return mapper<GroupDto, unknown>(data);
};

export const updateGroup = async (group: GroupDto): Promise<GroupDto> => {
    const data = await db.groups.update({
        where: {
            id: group.id
        },
        data: {
            ...group,
            rareSurveyDate: undefined,
            rareSurveyUrl: undefined,
            dailySurveyUrl: undefined
        }
    });

    return mapper<GroupDto, unknown>(data);
};

export const getGroupById = async (id: string): Promise<GroupDto> => {


    const data = await db.groups.findFirst({
        where: {
            id
        }
    });

    return mapper<GroupDto, unknown>({
        ...data
    });
};

export const getGroupStudents = async (id: string): Promise<TableDataDto<GroupHistoryDto>> => {
    const count = await db.students_groups.count({where: {groupId: id}})
    const data = await db.students_groups.findMany({
        where: {
            groupId: id
        },
        include: {
            student: {
                include: {
                    groupsStudents: {
                        include: {
                            group: {
                                include: {
                                    groupHoliday: true,
                                    groupHolidaysExceptions: {
                                        select: {
                                            generalHoliday: true
                                        }
                                    },
                                    groupScheduleChanges: true
                                }
                            }
                        },
                    }
                }
            }
        }
    });

    const dtos = data.map((g) => {
        const lastTaskDelayDuration = intervalToDuration({
            start: new Date(),
            end: addMinutes(new Date(), +g.student.lastTaskDelay.toString())
        });
        const averageTaskDelayDuration = intervalToDuration({
            start: new Date(),
            end: addMinutes(new Date(), +g.student.averageTaskDelay.toString())
        });
        const studentLearningHistory = g.student.groupsStudents.map((gs) => ({
            groupId: gs.group.id,
            from: gs.dateStartActual,
            to: gs.dateEndActual,
            totalHoursAmount: gs.group.totalHoursAmount,
            schedule: [...gs.group.groupScheduleChanges],
            groupHolidays: [...gs.group.groupHoliday],
            groupHolidaysExceptions: [...gs.group.groupHolidaysExceptions]
        })).flat();

        return {
            ...g,
            student: {
                ...g.student,
                lastTaskDelay: lastTaskDelayDuration,
                averageTaskDelay: averageTaskDelayDuration,
                studentLearningHistory
            }
        }
    });


    return {data: dtos as any, count}
}

export const updateGroupRareSurvey = async (dto: RareSurveyDto) => {
    return await db.groups.update({
        where: {
            id: dto.groupId
        },
        data: {
            rareSurveyDate: dto.dateRareSurvey && dto.rareSurveyUrl ? new Date(dto.dateRareSurvey) : null,
            rareSurveyUrl: dto.rareSurveyUrl ? dto.rareSurveyUrl : null
        }
    })
}

export const updateGroupDailySurvey = async (dto: DailySurveyDto) => {
    return await db.groups.update({
        where: {
            id: dto.groupId
        },
        data: {
            dailySurveyUrl: dto.dailySurveyUrl
        }
    })
}

export const createStudentSurvey = async (dto: CreateStudentSurveyDto) => {
    return await db.student_survey.create({
        data: {
            studentId: dto.studentId
        }
    })
}

export const getLatestSurveyByStudentId = async (studentId: string) => {
    return await db.student_survey.findFirst({
        where: {
            studentId: studentId
        },
        orderBy: {
            createdAt: 'desc'
        }
    })
}


export const showTheSurvey = async (studentId: string, currentGroupStudentId: string) => {
    const generalHolidays = await db.generalHolidays.findMany();
    const settings = await db.settings.findMany();

    const studentGroups = await db.students_groups.findFirst({
        where: {
            studentId,
            groupId: currentGroupStudentId ? currentGroupStudentId : undefined,
        },
        include: {
            group: {
                include: {
                    groupHoliday: true,
                    groupHolidaysExceptions: {
                        select: {
                            generalHoliday: true,
                        }
                    },
                    groupScheduleChanges: {
                        include: {
                            group: {
                                select: {
                                    timeEnd: true
                                }
                            }
                        }
                    }
                }
            }
        },
        orderBy: {
            dateStartActual: 'desc'
        },
        take: 1
    });


    if (studentGroups) {
        const groupEndDate = calculateGroupEndDay(studentGroups.group.groupScheduleChanges, studentGroups.group.totalHoursAmount, {
            globalH: generalHolidays,
            localH: studentGroups.group.groupHoliday,
            groupHExceptions: studentGroups.group.groupHolidaysExceptions
        });
        let lastStudyDay: Date | null = null;


        if (groupEndDate) {
            let tries = 0;
            if (isAfter(new Date(groupEndDate), new Date()) && isAfter(new Date(studentGroups.group.dateEnd), new Date())) {
                const foundedLastScheduleChange = studentGroups.group.groupScheduleChanges.reduce((latest: GroupScheduleDto | null, groupScheduleChange: GroupScheduleDto) => {
                    const dateStart = new Date(groupScheduleChange.dateStart);
                    if (!latest || (isAfter(dateStart, new Date(groupScheduleChange.dateStart)) && !isAfter(dateStart, new Date()))) {
                        return groupScheduleChange;
                    }
                    return latest;
                }, null);
                const daysOfWeekArray = foundedLastScheduleChange.daysSchedule.split('').map(Number);

                const currentDay = set(new Date(), {
                    hours: foundedLastScheduleChange?.group?.timeEnd.getHours(),
                    minutes: foundedLastScheduleChange?.group?.timeEnd.getMinutes(),
                    seconds: foundedLastScheduleChange?.group?.timeEnd.getSeconds(),
                });


                const generalHolidaysDate = generalHolidays?.map((e) => e.date)
                const groupHolidaysDate = studentGroups.group.groupHoliday?.map((e) => e.date);
                const groupHolidayExceptionsDate = studentGroups.group.groupHolidaysExceptions?.map((e) => e.generalHoliday.date)


                while (!lastStudyDay || tries < 3) {
                    tries++;
                    if (isItStudyDay(currentDay, daysOfWeekArray)
                        && !isWeekend(new Date(currentDay), generalHolidaysDate, groupHolidaysDate, groupHolidayExceptionsDate)
                        && isAfter(new Date(), currentDay)) {
                        lastStudyDay = set(currentDay, {
                            hours: foundedLastScheduleChange?.group?.timeEnd.getHours(),
                            minutes: foundedLastScheduleChange?.group?.timeEnd.getMinutes(),
                            seconds: foundedLastScheduleChange?.group?.timeEnd.getSeconds(),
                        })
                        if (settings && settings.length > 0) {
                            const offset = settings?.at(0)?.timeOffset;
                            if (offset) {
                                lastStudyDay = addHours(lastStudyDay, offset);

                            }
                        }
                    } else {
                        currentDay.setDate(currentDay.getDate() - 1);
                    }
                }

                const isCurrentDateInInterval = isWithinInterval(new Date(), {
                    start: lastStudyDay,
                    end: addHours(lastStudyDay, 18)
                });

                const latestSurvey = await getLatestSurveyByStudentId(studentId);


                if (latestSurvey && latestSurvey?.createdAt) {
                    const isDateFromLocalInInterval = isWithinInterval(latestSurvey?.createdAt, {
                        start: lastStudyDay,
                        end: addHours(lastStudyDay, 18)
                    })
                    if (isCurrentDateInInterval && !isDateFromLocalInInterval) {
                        if (studentGroups.group.rareSurveyDate) {
                            if (isSameDay(lastStudyDay, new Date(studentGroups.group.rareSurveyDate))) {
                                return {lastStudyDay, surveyStatus: SurveyStatus.rare}
                            } else {
                                return {lastStudyDay, surveyStatus: SurveyStatus.daily}
                            }
                        } else {
                            return {lastStudyDay, surveyStatus: SurveyStatus.daily}
                        }
                    }
                    return {lastStudyDay, surveyStatus: SurveyStatus.none}
                } else {
                    if (isCurrentDateInInterval) {
                        if (studentGroups.group.rareSurveyDate) {
                            if (isSameDay(lastStudyDay, new Date(studentGroups.group.rareSurveyDate))) {
                                return {lastStudyDay, surveyStatus: SurveyStatus.rare}
                            } else {
                                return {lastStudyDay, surveyStatus: SurveyStatus.daily}
                            }
                        } else {
                            return {lastStudyDay, surveyStatus: SurveyStatus.daily}
                        }
                    }
                    return {lastStudyDay, surveyStatus: SurveyStatus.none}
                }
            }
        }
    } else {
        return {surveyStatus: SurveyStatus.none}
    }
}

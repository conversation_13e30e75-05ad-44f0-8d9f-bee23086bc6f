<script lang="ts">
    import {t} from '$lib/i18n/config';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconPlus} from '@tabler/icons-svelte';
    import {
        initialTaskPaging,
        pageSize,
    } from '$lib/state/task-paging-state';
    import CountSpan from '$components/common/CountSpan.svelte';
    import {generateGuid} from '$common/core/utils';
    import {TaskPagingState} from '$lib/state/task-paging-state';
    import TaskFilter from '$components/tasks/TaskFilter.svelte';
    import {beforeNavigate, goto, invalidate} from '$app/navigation';
    import {TaskFilterState} from '$lib/state/task-filter-state';
    import {loadingWrap} from '$lib/common/utils';
    import GroupSpecificTasksTable from '$components/groups/GroupSpecificTasksTable.svelte';
    import {initialTaskFilter} from "$common/models/filters/task-filter.dto";
    import _ from "lodash";
    import {onDestroy, onMount} from "svelte";
    import {page} from "$app/stores";
    import {PreviousTaskPageState} from "$lib/state/previous-task-page.state";

    export let data;


    $: groups = data?.groups?.data || [];
    $: tasks = data?.tasks?.data?.map(t => {
        return {...t, isPublic: groups?.find(g => t?.groupId === g?.id)?.isPublic} // enrich task with group type
    }) || [];
    $: count = data?.tasks?.count || 0;

    let unsubscribeFilter = TaskFilterState.subscribe(async () => {
        $TaskPagingState = _.cloneDeep(initialTaskPaging)
        await loadingWrap(async () => {
            await invalidate('load:tasks');
        });
    });

    const loadMore = async () => {
        if (count > tasks.length) {
            TaskPagingState.set({
                skip: 0,
                take: pageSize + tasks.length
            });
            await loadingWrap(async () => {
                await invalidate('load:tasks');
            });
        }
    };


    onMount(() => {
        let previousPath = $page?.url?.pathname;
        if (previousPath) {
            $PreviousTaskPageState = previousPath
        }
    })


    beforeNavigate(() => {
        unsubscribeFilter();
    })

    onDestroy(() => {
        $TaskFilterState = _.cloneDeep(initialTaskFilter);
    });

</script>

<div class="h-[calc(100vh-85px)] overflow-hidden flex flex-col px-6 ">
    <div class="mt-3">
        <h1 class="title mb-1 font-medium text-xl">
            {$t('tasks.title')}
            <CountSpan {count}/>
        </h1>
    </div>

    <div class="card mt-6 p-5 w-full text-token flex justify-between items-center variant-glass-primary">
        <div class="w-9/10">
            <TaskFilter users={data?.users?.data} bind:groups/>
        </div>
        <div class="w-1/10">
            <div dir="ltr" class="w-full mt-6">
                <div>
                    <BaseButton on:click={() => goto(`/tasks/${generateGuid()}?isn=1`)} class="h-10">
                        {$t('tasks.new')}
                        <IconPlus/>
                    </BaseButton>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-3 overflow-y-auto overflow-x-auto">
        <GroupSpecificTasksTable bind:tasks bind:groups loadMoreFunc={loadMore}/>
    </div>
</div>


<script lang="ts">

    import {ContentApiClient} from "$lib/core/api-clients/content-api.cient";

    export let content: File | string;


    const uploadFile = async (file: File) => {
        const formData = new FormData();
        formData.append('file', file)
        const data = await new ContentApiClient().uploadFile(formData);
        content = data?.file?.url;
    }

    function getNameFromUrl(url) {
        let lastIndex = url.lastIndexOf('/');

        if (lastIndex !== -1) {
            return url.substring(lastIndex + 1);
        } else {
            return 'file';
        }
    }
</script>


<div>
    {#if content && typeof content === 'string'}
        <div>
            <a class="file" href={encodeURI(content ?? '')} target="_blank">
                <svg style="fill:none;width: 20px;" xmlns="http://www.w3.org/2000/svg"
                     class="inline-block icon icon-tabler icon-tabler-paperclip" width="24" height="24"
                     viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                     stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5"></path>
                </svg>
                <span class="text-lg">{getNameFromUrl(content)}</span>
            </a>
        </div>
    {:else}
        <input class="" type="file" on:change={(e) => {
			if (e.currentTarget.files[0]) {
				uploadFile(e.currentTarget.files[0]);
			}
		}}>
    {/if}

</div>
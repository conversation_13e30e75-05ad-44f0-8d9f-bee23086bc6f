<script lang="ts">
	import type { TranslationCompletion } from '$common/models/dtos/task.dto';
	import BaseButton from '$components/common/BaseButton.svelte';
	import { IconPlayerPlayFilled, IconPlayerStopFilled, IconVolume, IconVolumeOff } from '@tabler/icons-svelte';
	import { page } from '$app/stores';
	import { t } from '$lib/i18n/config';

	export let sentence: TranslationCompletion;
	let playbackRate = 1;
	let audio: HTMLAudioElement;
	const pageInfo = $page;

    let   volume = 1;
    let   currentVolume;
    let   volumeSlider;
	let   muted = false;
	let   isPlaying = false;
	
    const seekVolume = () => (audio.volume = volumeSlider.value);
    const muteVolume = () => {
        muted = !muted;
        if (muted) {
            currentVolume = volumeSlider.value;
            audio.volume = 0;
            volumeSlider.value = 0;
        } else {
            volumeSlider.value = currentVolume;
			audio.volume = currentVolume;
        }
    };

	const handleClickBtn = () => {
		if (isPlaying) {
			audio.pause();
			audio.currentTime = 0;
			isPlaying = false;
		} else {
			audio.play();
			isPlaying = true;
		}
	};

	const composeAudioToPlay = (fileName) =>
		fileName
			? `https://${pageInfo.data.envs.VITE_S3_BUCKET}.s3.${pageInfo.data.envs.VITE_S3_REGION}.amazonaws.com/${pageInfo.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${fileName}`
			: undefined;

	const x05 = () => {
		audio.playbackRate = 0.5;
		playbackRate = 0.5;
	};
	const x1 = () => {
		audio.playbackRate = 1;
		playbackRate = 1;
	};
	const x15 = () => {
		audio.playbackRate = 1.5;
		playbackRate = 1.5;
	};
</script>

<div class="w-full flex items-center gap-4">
	<audio 
        bind:this={audio} 
        class="hidden" 
        controls src={composeAudioToPlay(sentence.audioUrl)}
		on:ended={() => isPlaying = false}
        />
	<BaseButton size="sm" on:click={handleClickBtn} className="h-6 w-23" disabled={!sentence.audioUrl || sentence.audioUrl === ""} >
		{#if !isPlaying}
			<IconPlayerPlayFilled size={20} />
		{:else}
			<IconPlayerStopFilled size={20} />
		{/if}
		<span class="ml-2">
			{$t('sentences.inTask.listenButton')}
		</span>
	</BaseButton>
	        <div class="flex items-center ">
                <button class="p-1 !bg-primary-50-900-token rounded-xl cursor-pointer btn" type="button"
                        on:click={muteVolume}>
                    {#if muted}
                        <IconVolumeOff/>
                    {:else}
                        <IconVolume/>
                    {/if}
                </button>
                <input
                        type="range"
                        class="w-22 h-1"
                        min="0"
                        on:input={seekVolume}
                        bind:this={volumeSlider}
                        value={volume}
                        max="1"
                        step=".001"
                />
            </div>

	<div class="flex items-center">
		<button
			class="p-1 rounded-tl-xl rounded-bl-xl !bg-primary-50-900-token cursor-pointer btn h-fit w-fit {playbackRate ===
			0.5
				? 'outline'
				: ''} z-50"
			type="button"
			on:click={x05}
		>
			x0.5
		</button>
		<span class="divider-vertical h-5" />
		<button
			class="p-1 px-4 !bg-primary-50-900-token cursor-pointer btn h-fit w-fit {playbackRate === 1
				? 'outline'
				: ''} z-50"
			type="button"
			on:click={x1}
		>
			x1
		</button>
		<span class="divider-vertical h-5" />
		<button
			class="p-1 !bg-primary-50-900-token rounded-br-xl rounded-tr-xl cursor-pointer btn h-fit {playbackRate ===
			1.5
				? 'outline'
				: ''} z-50"
			type="button"
			on:click={x15}
		>
			x1.5
		</button>
	</div>
</div>

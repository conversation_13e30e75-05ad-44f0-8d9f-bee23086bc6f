<script lang="ts">
    import {t} from "$lib/i18n/config.js";
    import {IconCircleLetterX, IconDeviceFloppy} from "@tabler/icons-svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {getModalStore} from '@skeletonlabs/skeleton';
    import BaseSwitch from "$components/common/BaseSwitch.svelte";
    import {SentenceApiClient} from "$lib/core/api-clients/sentence-api-client";
    import {onMount} from "svelte";
    import type {GenerateTaskSentenceDto, SentenceInTaskDto} from "$common/models/dtos/sentence.dto";
    import {CurrentEditableState} from "$lib/state/task-current-editable-state";
    import {formatToPickerDate} from "$lib/common/utils.js";


    const modalStore = getModalStore();

    let initialDateFrom = new Date();
    initialDateFrom.setMonth(initialDateFrom.getMonth() - 3)

    let data: SentenceInTaskDto[];
    const currentDate = new Date();
    currentDate.setMonth(currentDate.getMonth() - 1);
    let fromDate = formatToPickerDate(currentDate);

    let toDate = formatToPickerDate(new Date());

    let count = 20;

    let foundedSentenceNumber = 0;


    $:if (count < 0) {
        count = 0;
    }

    $:disableGenerate = foundedSentenceNumber === 0;

    let onlyAudio = false;

    let onlyFav = false;

    $:selectedSentences = $CurrentEditableState.sentences.map((s) => s.id);

    const lang = $CurrentEditableState.lang;

    const groupId = $CurrentEditableState.groupId;


    const onChangeInput = async () => {
        const to = new Date(toDate)
        const from = new Date(fromDate)
        to.setHours(0, 0, 0);
        from.setHours(0, 0, 0);
        const dto: GenerateTaskSentenceDto = {
            count,
            onlyFav,
            onlyAudio,
            to,
            from,
            selectedSentences,
            lang,
            groupId
        };

        data = await new SentenceApiClient().getShuffledSentencesForGenerate(dto);
        foundedSentenceNumber = data.length;
        count = data.length ? data.length : count;
    }

    const handleClick = async () => {
        if ($CurrentEditableState.sentences.length > 0) {
            $CurrentEditableState.sentences = [...$CurrentEditableState.sentences, ...data].map((s, index) => ({
                ...s,
                index: index + 1
            })).sort((a, b) => a.index - b.index)
        } else {
            $CurrentEditableState.sentences = [...data];
        }
        modalStore.close()
    }

    onMount(async () => {
        const to = new Date(toDate)
        const from = new Date(fromDate)
        to.setHours(0, 0, 0);
        from.setHours(0, 0, 0);
        const dto: GenerateTaskSentenceDto = {
            count,
            onlyFav,
            onlyAudio,
            to,
            from,
            selectedSentences,
            lang,
            groupId
        };

        data = await new SentenceApiClient().getShuffledSentencesForGenerate(dto);
        foundedSentenceNumber = data.length;
        count = data.length || 20;
    })


</script>


<div
        class="flex flex-col gap-5 modal card p-10  sm:w-2/3 lg:w-1/2 shadow-xl space-y-4"
>

    <div class="grid grid-cols-3 gap-5">
        <label for="fromDate" class="label">
            <div class="title font-medium text-base">From</div>
            <input dir="rtl" class="input h-[43px] rounded pr-[5px]" type="date"
                   min={formatToPickerDate(initialDateFrom)} max={formatToPickerDate(new Date())} bind:value={fromDate}
                   on:change={onChangeInput}
            >
        </label>
        <label for="toDate" class="label">
            <div class="title font-medium text-base">To</div>
            <input dir="rtl" class="input h-[43px] rounded pr-[5px]" type="date"
                   min={formatToPickerDate(new Date(fromDate))} max={formatToPickerDate(new Date())}
                   bind:value={toDate}
                   on:change={onChangeInput}
            >
        </label>
        <label for="count" class="label">
            <div class="title font-medium text-base">Count of sentences</div>
            <input name="count" on:change={onChangeInput} bind:value={count} min={0} type="number"
                   class="w-full input rounded p-2 overflow-visibles indent-2 tracking-wide">
        </label>
    </div>
    <div class="grid grid-cols-3 gap-5">
        <label for="onlyAudio" class="label">
            <div class="title mb-1 font-medium text-base">Only with audio</div>
            <BaseSwitch on:change={onChangeInput} bind:checked={onlyAudio} name="onlyAudio"/>
        </label>
        <label for="onlyFav" class="label">
            <div class="title mb-1 font-medium text-base">Only favorites</div>
            <BaseSwitch on:change={onChangeInput} bind:checked={onlyFav} name="onlyFav"/>
        </label>
        <label for="sentencesCount" class="label">
            <div class="title mb-1 font-medium text-base">Number of sentences found</div>
            <span class="text-xl font-bold">{foundedSentenceNumber}</span>
        </label>
    </div>


    <div class=" flex justify-between">
        <BaseButton disabled={disableGenerate} on:click={handleClick}>
            Generate Task
            <IconDeviceFloppy/>
        </BaseButton>
        <BaseButton on:click={() => {
            modalStore.close()
        }}>
            <IconCircleLetterX/>
            {$t('students.buttons.cancel')}
        </BaseButton>
    </div>
</div>

import {wrapFunc} from "$api/core/misc/response-wrapper";
import {paramsToKeyValue} from "$api/core/utils";
import {getGroupStudents} from "$api/core/services/group.service";
import type {RequestEvent} from "@sveltejs/kit";

export const GET = async ({url}: RequestEvent) =>
    await wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);

        return await getGroupStudents(id);
    })

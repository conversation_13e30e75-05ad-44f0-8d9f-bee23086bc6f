import { z } from 'zod';

export const schemaForUpdateAndAcceptStudent = z.object({
	tz: z
		.string()
		.min(6, { message: 'students.students.modal.formFieldsErrors.tz' })
		.max(12, { message: 'students.students.modal.formFieldsErrors.tz' }),
	firstname: z.string().min(2, { message: 'students.students.modal.formFieldsErrors.firstname' }),
	lastname: z.string().min(2, { message: 'students.students.modal.formFieldsErrors.lastname' }),
	email: z.string().email('students.students.modal.formFieldsErrors.email'),
	phone: z.string().optional(),
	whatsapp: z.string().optional(),
	dob: z
		.string()
		.regex(
			/(((20[012]\d|19\d\d)|(1\d|2[0123]))-((0[0-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01])))|(((0[1-9])|([12][0-9])|(3[01]))-((0[0-9])|(1[012]))-((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\/((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01])))|(((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\/((0[0-9])|(1[012]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\.((0[0-9])|(1[012]))\.((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\.((0[0-9])|(1[012]))\.((0[1-9])|([12][0-9])|(3[01])))/,
			'students.students.modal.formFieldsErrors.dob'
		),
	comment: z.string()
});

export const schemaForCreateStudentRequest = z.object({
	tz: z
		.string()
		.min(6, { message: 'students.students.modal.formFieldsErrors.tz' })
		.max(12, { message: 'students.students.modal.formFieldsErrors.tz' }),
	firstname: z.string().min(2, { message: 'students.students.modal.formFieldsErrors.firstname' }),
	lastname: z.string().min(2, { message: 'students.students.modal.formFieldsErrors.lastname' }),
	email: z.string().email('students.students.modal.formFieldsErrors.email').optional().or(z.literal('')),
	phone: z.string().optional(),
	whatsapp: z.string().optional(),
	dob: z
		.string()
		.regex(
			/(((20[012]\d|19\d\d)|(1\d|2[0123]))-((0[0-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01])))|(((0[1-9])|([12][0-9])|(3[01]))-((0[0-9])|(1[012]))-((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\/((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01])))|(((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\/((0[0-9])|(1[012]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\.((0[0-9])|(1[012]))\.((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\.((0[0-9])|(1[012]))\.((0[1-9])|([12][0-9])|(3[01])))/,
			'students.students.modal.formFieldsErrors.dob'
		),
	city: z.string().min(1, { message: 'students.requests.modal.formFieldsErrors.city' }),
	groupLevel: z.string().min(1, { message: 'students.requests.modal.formFieldsErrors.groupLevel' }),
	groupLang: z.string().min(1, { message: 'students.requests.modal.formFieldsErrors.groupLang' }),
	groupStartDate: z
		.string()
		.regex(
			/(((20[012]\d|19\d\d)|(1\d|2[0123]))-((0[0-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01])))|(((0[1-9])|([12][0-9])|(3[01]))-((0[0-9])|(1[012]))-((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\/((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01])))|(((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\/((0[0-9])|(1[012]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\.((0[0-9])|(1[012]))\.((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\.((0[0-9])|(1[012]))\.((0[1-9])|([12][0-9])|(3[01])))/,
			'students.students.modal.formFieldsErrors.dob'
		),
	learnStartDate: z
		.string()
		.regex(
			/(((20[012]\d|19\d\d)|(1\d|2[0123]))-((0[0-9])|(1[012]))-((0[1-9])|([12][0-9])|(3[01])))|(((0[1-9])|([12][0-9])|(3[01]))-((0[0-9])|(1[012]))-((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\/((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01])))|(((0[0-9])|(1[012]))\/((0[1-9])|([12][0-9])|(3[01]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\/((0[0-9])|(1[012]))\/((20[012]\d|19\d\d)|(1\d|2[0123])))|(((0[1-9])|([12][0-9])|(3[01]))\.((0[0-9])|(1[012]))\.((20[012]\d|19\d\d)|(1\d|2[0123])))|(((20[012]\d|19\d\d)|(1\d|2[0123]))\.((0[0-9])|(1[012]))\.((0[1-9])|([12][0-9])|(3[01])))/,
			'students.students.modal.formFieldsErrors.dob'
		)
});

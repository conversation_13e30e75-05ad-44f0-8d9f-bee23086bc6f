import {db} from '$api/core/service-clients/db';
import type {IncentiveContentFilterDto} from '$common/models/filters/incentive-filter.dto';
import {mapper} from '$common/core/mapper';
import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
import type {TableDataDto} from "$common/models/dtos/table-data.dto";

export const createIncentiveContent = async (content: IncentiveContentDto) => {
    const data = await db.incentiveContent.create({
        data: {
            id: content.id,
            name: content.name,
            comment: content.comment,
            value: content.value,
            page_content: content.page_content ? {
                create: {
                    id: content.page_content?.id,
                    content_items: {
                        createMany: {
                            data: content.page_content.content_items.map((ci) => ({
                                id: ci.id,
                                content: ci.type === 'lexical' ? ci.content : JSON.stringify(ci.content),
                                type: ci.type,
                                position: ci.position
                            }))
                        }
                    }
                }
            } : undefined
        }
    });
    return mapper<IncentiveContentDto, unknown>(data);
};

export const getAllIncentiveContent = async (filter: IncentiveContentFilterDto) => {
    const where = composeWhereClause(filter);
    const count = await db.incentiveContent.count({where});
    const data = await db.incentiveContent.findMany({
        where,
        take: filter.take === -1 ? undefined : filter.take,
        skip: filter.skip,
        orderBy: {
            createdAt: 'desc'
        },
        include: {
            page_content: {
                include: {
                    content_items: {
                        orderBy: {
                            position: 'asc'
                        }
                    }
                }
            }
        }
    });


    const d = data?.map((d) => ({
        ...d,
        page_content: d.page_content ? {
            ...d.page_content,
            content_items: d.page_content.content_items.map((ci) => ({
                ...ci,
                content: ci.type === 'lexical' ? ci.content : JSON.parse(ci.content as string)
            }))
        } : null
    }))


    return mapper<TableDataDto<IncentiveContentDto>, unknown>({data: d, count});
};

export const getIncentiveContentById = async (id: string) => {
    const data = await db.incentiveContent.findUnique({
        where: {
            id
        }
    });
    return mapper<IncentiveContentDto, unknown>(data);
};

export const updateIncentiveContentById = async (id: string, content: IncentiveContentDto) => {

    const data = await db.$transaction(async (tx) => {
        if (content && content?.page_content && content?.page_content?.content_items) {
            await tx.content_item.deleteMany({
                where: {
                    page_contentId: content.page_content.id,
                }
            })
            for (const item of content.page_content?.content_items) {
                await tx.content_item.create({
                    data: {
                        id: item.id,
                        content: item.type === 'lexical' ? item.content : JSON.stringify(item.content),
                        page_contentId: content.page_content.id,
                        position: item.position,
                        type: item.type
                    },
                })
            }
        }
        return await tx.incentiveContent.update({
            where: {
                id
            },
            data: {
                ...content,
                page_content: undefined
            }
        });
    })

    return mapper<IncentiveContentDto, unknown>(data);
};

export const deleteIncentiveContentById = async (id: string) => {
    const data = await db.incentiveContent.delete({
        where: {
            id
        }
    });
    return mapper<IncentiveContentDto, unknown>(data);
};

const composeWhereClause = (filter: IncentiveContentFilterDto): Record<string, any> => {
    const whereClause: Record<string, any> = {};

    if (filter.search) {
        whereClause.name = filter.search;
    }
    return whereClause;
};

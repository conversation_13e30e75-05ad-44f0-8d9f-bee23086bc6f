<script>
	import { locale, locales, t } from '$lib/i18n/config';
	import {LocaleState} from "$lib/state/locale.state";

	export let dir = 'ltr';

</script>

<div>
	<select
		class="select select-bordered select-primary max-w-3xl w-full text-lg"
		bind:value={$locale}
		on:change={(e) => LocaleState.set(e?.target?.value ?? 'en')}
		{dir}
	>
		{#each $locales as value}
			<option {value}>{$t(`lang.${value}`)}</option>
		{/each}
	</select>
</div>

<div class="loader-container">
    <span class="loader"></span>
</div>

<style>
    .loader-container {
        position: relative;
        top: 10px;
        left: 50%;
    }

    .loader {
        width: 11px;
        height: 11px;
        border-radius: 100%;
        display: block;
        margin: 25px auto;
        position: relative;
        color: #fff;
        left: -250px;
        box-sizing: border-box;
        animation: shadowRolling 1700ms linear infinite;
    }

    @keyframes shadowRolling {
        0% {
            box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }
        12% {
            box-shadow: 250px 0 theme('colors.primary.500'), 0px 0 rgba(255, 255, 255, 0),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }
        25% {
            box-shadow: 260px 0 theme('colors.primary.500'), 250px 0 theme('colors.primary.500'),
            0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
        }
        36% {
            box-shadow: 270px 0 theme('colors.primary.500'), 260px 0 theme('colors.primary.500'),
            250px 0 theme('colors.primary.500'), 0px 0 rgba(255, 255, 255, 0);
        }
        50% {
            box-shadow: 280px 0 theme('colors.primary.500'), 270px 0 theme('colors.primary.500'),
            260px 0 theme('colors.primary.500'), 250px 0 theme('colors.primary.500');
        }
        62% {
            box-shadow: 500px 0 rgba(255, 255, 255, 0), 280px 0 theme('colors.primary.500'),
            270px 0 theme('colors.primary.500'), 260px 0 theme('colors.primary.500');
        }
        75% {
            box-shadow: 500px 0 rgba(255, 255, 255, 0), 500px 0 rgba(255, 255, 255, 0),
            280px 0 theme('colors.primary.500'), 270px 0 theme('colors.primary.500');
        }
        87% {
            box-shadow: 500px 0 rgba(255, 255, 255, 0), 500px 0 rgba(255, 255, 255, 0),
            500px 0 rgba(255, 255, 255, 0), 280px 0 theme('colors.primary.500');
        }
        100% {
            box-shadow: 500px 0 rgba(255, 255, 255, 0), 500px 0 rgba(255, 255, 255, 0),
            500px 0 rgba(255, 255, 255, 0), 500px 0 rgba(255, 255, 255, 0);
        }
    }
</style>

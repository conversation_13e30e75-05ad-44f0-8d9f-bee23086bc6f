<script lang="ts">
    import BaseInput from "$components/common/BaseInput.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {getModalStore} from "@skeletonlabs/skeleton";
    import {deserialize} from "$app/forms";
    import {ComplaintModalState, initialComplaintModalState} from "$lib/state/complaint-modal-state";
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";
    import {t} from "$lib/i18n/config";

    const modalStore = getModalStore();

    async function handleSubmit(event) {
        const data = new FormData(this);
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());

        if (result.type === 'success') {
            NotificationStore.push({
                type: NotificationType.success,
                message: $t('complaints.notifications.toComplain.success')
            });
        } else {
            NotificationStore.push({
                type: NotificationType.error,
                message: $t('complaints.notifications.toComplain.error')
            });
        }
        modalStore.close();
        $ComplaintModalState = {...initialComplaintModalState}
    }

    const closeModalForComplaint = () => {
        modalStore.close();
    }


</script>


<div dir="auto" class="rounded-xl modal card  sm:w-full md:w-2/3 lg:w-2/5 shadow-xl space-y-4 p-5">
    <form
            on:submit|preventDefault={handleSubmit}
            method="POST"
            action="?/createComplaint"
            class="flex flex-col items-center justify-between gap-10 p-2"
    >
        <input class="hidden" type="text" name="taskId" value={$ComplaintModalState.taskId}>
        <input class="hidden" type="text" name="sentenceId" value={$ComplaintModalState.sentenceId}>
        <input class="hidden" type="text" name="createdBy" value={$ComplaintModalState.createdBy}>
        <h1 class="self-start text-xl">
            {$t('complaints.modal.title')}
        </h1>
        <div class="w-full">
            <BaseInput name="comment" dir="auto" bind:value={$ComplaintModalState.comment}
                       placeHolder={$t('complaints.modal.placeholder')} maxlength="250"/>
        </div>
        <div class="flex justify-between w-full">
            <div>
                <BaseButton on:click={closeModalForComplaint}>
                    {$t('complaints.modal.cancelButton')}
                </BaseButton>
            </div>
            <div>
                <BaseButton type="submit" disabled={$ComplaintModalState.comment.length<5}>
                    {$t('complaints.modal.confirmButton')}
                </BaseButton>
            </div>
        </div>

    </form>
</div>

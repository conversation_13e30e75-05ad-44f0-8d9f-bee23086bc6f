<script lang="ts">
    import {goto} from "$app/navigation";
    import {HoursScheduleFilter, LevelFilter} from "$common/models/enums.js";
    import {
        DbFormatStringToDaysSchedule,
        calculateGroupEndDay, getCurrentGroupStudyHours, getCurrentGroupStudyDays, calculateGroupRemainingDays,
    } from "$lib/common/utils.js";
    import {IconSend} from "@tabler/icons-svelte";
    import InfoBadge from "$components/common/InfoBadge.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import type {GroupDto} from "$common/models/dtos/group.dto";
    import {createEventDispatcher, onMount} from "svelte";
    import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
    import ProgressGroup from "$components/groups/ProgressGroup.svelte";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {format} from "date-fns";


    export let row: GroupDto;
    export let generalHolidays: GeneralHolidayDto[];


    let percentage;


    let groupStudyHours;
    let groupStudyDays;
    let groupRemainingDays;
    let groupRemainingHours;


    onMount(() => {
        let endGroupLearning = calculateGroupEndDay(row.groupScheduleChanges || [], row.totalHoursAmount, {
            globalH: generalHolidays,
            localH: row.groupHoliday,
            groupHExceptions: row.groupHolidaysExceptions
        });


        if (endGroupLearning) {
            groupStudyHours = getCurrentGroupStudyHours(new Date(endGroupLearning), row.groupScheduleChanges || [], row.totalHoursAmount, {
                globalH: generalHolidays,
                localH: row.groupHoliday,
                groupHExceptions: row.groupHolidaysExceptions
            });
            groupStudyDays = getCurrentGroupStudyDays(new Date(endGroupLearning), row.groupScheduleChanges || [], row.totalHoursAmount, {
                globalH: generalHolidays,
                localH: row.groupHoliday,
                groupHExceptions: row.groupHolidaysExceptions
            });
            groupRemainingDays = calculateGroupRemainingDays(groupStudyDays, new Date(endGroupLearning), row.groupScheduleChanges || [], row.totalHoursAmount, {
                globalH: generalHolidays,
                localH: row.groupHoliday,
                groupHExceptions: row.groupHolidaysExceptions
            })
            groupRemainingHours = row.totalHoursAmount - groupStudyHours;
            const currentGroupStudyHours = getCurrentGroupStudyHours(
                new Date(endGroupLearning),
                row.groupScheduleChanges || [],
                row.totalHoursAmount,
                {
                    globalH: generalHolidays,
                    localH: row.groupHoliday,
                    groupHExceptions: row.groupHolidaysExceptions
                });
            percentage = +((currentGroupStudyHours / row.totalHoursAmount) * 100).toFixed(0);
        }
    })


    const setBorderColor = (percentage) => {


        if (percentage >= 95 && percentage <= 100) {
            return 'border-error-500-400-token'
        } else if (percentage >= 75 && percentage <= 94) {
            return 'border-warning-500-400-token'
        } else {
            return 'border-success-500-400-token'
        }
    }


    const dispatchModal = createEventDispatcher();
</script>


<tr on:click={() => goto(`/groups/${row.id}`)}
    class="text-center cursor-pointer {row.isPublic ? '!bg-surface-300-600-token' : ''}">
    <td class="relative text-start">
        <span class="mt-4 badge text-sm  text-blue-600   dark:text-blue-400 font-medium">{row.name}</span>
        {#if !row.isPublic}
            <OnlyForRole>
                <ProgressGroup {percentage} borderColor={setBorderColor(percentage)}/>
            </OnlyForRole>
        {/if}
    </td>
    <td class="">
       <span class="block mt-4">
           {format(new Date(row.dateStart), 'dd.MM.yyyy')}
       </span>
    </td>
    <td>
       <span class="block mt-4">
           {row.lang}
       </span>
    </td>
    <td>
       <span class="block mt-4">
       {LevelFilter[row.level]}


       </span>


    </td>
    <td>
       <span class="block mt-4">
           {DbFormatStringToDaysSchedule(row.groupScheduleChanges.sort((x, y) => new Date(y.createdAt) - new Date(x.createdAt))[0]?.daysSchedule)}
       </span>
    </td>
    <td>
       <span class="block mt-4">
           {HoursScheduleFilter[row.hoursSchedule]}
       </span>
    </td>
    <td>
       <span class="block mt-4">
            {#if row.whatsappUrl}
                       <InfoBadge url={row.whatsappUrl} text="whatsapp"/>
            {/if}
       </span>
    </td>
    <td>
       <span class="block mt-4">
           {row.comment}
       </span>
    </td>
    <td>
       <span class="block mt-4">
           {row.isActive}
       </span>
    </td>
    <td>
       <span class="block mt-4">
           {row?._count?.studentsGroups}
       </span>
    </td>
    {#if !row.isPublic}
        <OnlyForRole>
            <td class="flex flex-col items-center min-w-[130px]">
                <p class="font-bold">{percentage}%</p>
                <p>H: {groupStudyHours}/{groupStudyHours + groupRemainingHours} ({groupRemainingHours})</p>
                <p>D: {groupStudyDays}/{groupStudyDays + groupRemainingDays} ({groupRemainingDays})</p>
            </td>
        </OnlyForRole>
    {:else}
        <td></td>
    {/if}
    <td>
       <span class="block mt-4">
          {#if row.studentsCount > 0}
           <BaseButton
                   size="sm"
                   on:click={(e) => {
                           e.stopPropagation();
                           dispatchModal('triggerSendMessageModal', {
                              recipientId: row.id,
                              recipientName: `${row.name} `,
                              lang: row.lang
                           });
                        }}
           >
               <IconSend size={20} stroke="1.5"/>
           </BaseButton>
       {/if}
       </span>


    </td>
</tr>

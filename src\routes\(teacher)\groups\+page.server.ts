import {groupSchemaForCreatingGroup} from '$lib/validation-schemes/groups';
import {validateEntityBySchema} from '$lib/common/utils';
import {fail, type Actions, RequestEvent} from '@sveltejs/kit';
import {parseFormData} from 'parse-nested-form-data';
import {createGroup} from '$api/core/services/group.service';
import {mapper} from '$common/core/mapper';
import type {GroupDto} from '$common/models/dtos/group.dto';
import {convertStringTimeToDate} from '$lib/common/utils';

export const ssr = false;

export const actions: Actions = {
    create: async ({request,locals}: RequestEvent) => {
        const creatorId = locals.user.id;
        const group = parseFormData(await request.formData());
        const {result, errors} = validateEntityBySchema({
            ...group,
            hoursSpendBySession: group.hoursSpendBySession ? +group.hoursSpendBySession : null,
            totalHoursAmount: group.totalHoursAmount ? +group.totalHoursAmount : null
        }, groupSchemaForCreatingGroup);

        if (!result) return fail(400, {error: true, errors});

        const {
            level,
            isPublic,
            isActive,
            hoursSchedule,
            timeStart,
            timeEnd,
            dateStart,
            dateEnd,
            hoursSpendBySession,
            totalHoursAmount
        } =
            group;


        const dto = mapper<GroupDto, any>({
            ...group,
            level: +(level || 0),
            isPublic: isPublic === 'true',
            isActive: isActive === 'true',
            hoursSchedule: +(hoursSchedule || 0),
            timeStart: new Date(convertStringTimeToDate(timeStart?.toString() || '')),
            timeEnd: new Date(convertStringTimeToDate(timeEnd?.toString() || '')),
            dateStart: new Date(dateStart?.toString() || ''),
            dateEnd: new Date(dateEnd?.toString() || ''),
            hoursSpendBySession: +hoursSpendBySession!,
            totalHoursAmount: +totalHoursAmount!
        });

        return await createGroup(dto,creatorId);
    }
};

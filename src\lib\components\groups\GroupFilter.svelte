<script lang="ts">
	import BaseInput from '$components/common/BaseInput.svelte';
	import BaseSelect from '$components/common/BaseSelect.svelte';
	import {
		ActiveFilter,
		HoursScheduleFilter,
		LanguageFilter,
		LevelFilter, TypeFilter
	} from '$common/models/enums';
	import { mapEnumToOptions, mapEnumToOptionsWithTranslations } from '$lib/common/utils';
	import { t } from '$lib/i18n/config';
	import { GroupFilterState } from '$lib/state/group-filter-state';
	import { get } from 'svelte/store';
    import _ from "lodash";
	import {IconTestPipe} from '@tabler/icons-svelte'
	import BaseSwitch from "$components/common/BaseSwitch.svelte";

	let inputValue = '';


    const onInput = _.debounce(() => GroupFilterState.set({ ...get(GroupFilterState), name: inputValue }), 1000)

</script>

<div class="flex gap-10">
	<BaseInput
		name="search"
		bind:value={inputValue}
		on:input={onInput}
		title={$t('groups.filters.title.groupName')}
		className="min-w-[350px]"
	/>
	<BaseSelect
		bind:value={$GroupFilterState.hoursSchedule}
		name="hoursSchedule"
		title={$t('groups.filters.title.hoursSchedule')}
		options={mapEnumToOptions(
			HoursScheduleFilter,
			true
		)}
	/>
	<BaseSelect
		bind:value={$GroupFilterState.level}
		name="hoursSchedule"
		title={$t('groups.filters.title.level')}
		options={mapEnumToOptions(LevelFilter, true)}
	/>
	<BaseSelect
		name="lang"
		title={$t('groups.filters.title.lang')}
		options={mapEnumToOptions(LanguageFilter, true)}
		bind:value={$GroupFilterState.lang}
	/>
	<BaseSelect
		bind:value={$GroupFilterState.isActive}
		name="isActive"
		title={$t('groups.filters.title.active')}
		options={mapEnumToOptionsWithTranslations(
			ActiveFilter,
			'groups.filters.values.isActive',
			$t,
			true
		)}
	/>

	<BaseSelect
			bind:value={$GroupFilterState.isPublic}
			name="isActive"
			title={$t('groups.filters.title.type')}
			options={mapEnumToOptionsWithTranslations(
			TypeFilter,
			'groups.filters.values.type',
			$t,
			true
		)}
	/>
</div>

import {wrapFunc} from "$api/core/misc/response-wrapper";
import {paramsToKeyValue, toBoolean} from "$api/core/utils";
import type {RequestEvent} from "@sveltejs/kit";
import {getShuffledSentencesForGenerate} from "$api/core/services/sentence.service";


export const GET = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const {count, to, from, onlyFav, onlyAudio, lang, groupId} =
            paramsToKeyValue(event.url.searchParams);

        const selectedSentences = event?.url?.searchParams?.get('selectedSentences')?.split(',') || [];

        return await getShuffledSentencesForGenerate({
            count: +count,
            to: new Date(to),
            from: new Date(from),
            onlyFav: onlyFav === 'true',
            onlyAudio: onlyAudio === 'true',
            selectedSentences,
            lang,
            groupId
        })
    }, event);
FROM --platform=linux/amd64 node:20.18-alpine

RUN ln -s /usr/lib/libssl.so.3 /lib/libssl.so.3

WORKDIR /app

RUN apk add --no-cache openssl
RUN npm install -g pnpm

COPY . .

RUN pnpm install

RUN npx prisma generate

ENV NODE_OPTIONS=--max_old_space_size=4096
ENV VITE_APPINSIGHTS_INSTRUMENTATION_KEY="716bcaf8-46f3-448c-b33d-b80d0d41c953"

ENV NODE_ENV=production
RUN npm run build
EXPOSE 3000

CMD [ "node", "build" ]
<script lang="ts">
	import BaseButton from '../common/BaseButton.svelte';
	import {
		IconCloudUpload,
		IconPlayerRecordFilled,
		IconPlayerStopFilled,
		IconRepeat,
		IconPaperclip
	} from '@tabler/icons-svelte';
	import { createEventDispatcher, onMount } from 'svelte';
	import { getCurrentDateTime } from '$lib/common/utils';
	import { SentenceApiClient } from '$lib/core/api-clients/sentence-api-client';
	import type { SentenceInTaskDto } from '$common/models/dtos/sentence.dto';
	import LoadingSpinner2 from '$components/common/LoadingSpinner2.svelte';
	import {encodeMp3, loadToFfmpeg} from "$lib/common/ffmpeg-helpers";
	import {ffmpegState} from "$lib/state/ffmpeg-state";
	import {page} from "$app/stores";

	export let audioUrl: string | null;
	$: audioToPlay = composeAudioToPlay(audioUrl);
	export let id: string;
    export let disabled:boolean;
	export let sentence: SentenceInTaskDto;
	export let audioRecordedButNotYetSaved = false;
	let mediaRecorder: MediaRecorder | null = null;
	let audioBlob: string;
	let audioBlobToSave: Blob;
	let time = 0;
	let isRunning = false;
	let isUploading = false;
	let interval;
	let stream;
	let input: HTMLInputElement;

	const dispatcher = createEventDispatcher();

	onMount(async () => {
		clearInterval(interval)
	});

	const composeAudioToPlay = (fileName) =>
		fileName
			? `https://${$page.data.envs.VITE_S3_BUCKET}.s3.${$page.data.envs.VITE_S3_REGION}.amazonaws.com/${$page.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${fileName}`
			: undefined;

	const startRecording = async () => {
		try {
			stream = await navigator.mediaDevices.getUserMedia({ audio: true });
			mediaRecorder = new MediaRecorder(stream);
			mediaRecorder.addEventListener('dataavailable', async ({ data }) => {
				try {
					const audioBlobToProcess = new File([data], `${id}_${getCurrentDateTime()}.webm`, {type: `audio/webm`}) as File;
					await loadToFfmpeg(audioBlobToProcess);
					const result = await encodeMp3();

					if (result) {
						audioBlobToSave = new File([result], `${id}_${getCurrentDateTime()}.mp3`, {
							type: `audio/mp3`,
							lastModified: Date.now()
						}) as File;

						audioBlob = URL.createObjectURL(audioBlobToSave);
					} else {
						console.error('Failed to encode audio');
					}
				} catch (error) {
					console.error('Error processing audio:', error);
				}
			});
			audioUrl = '';
			audioBlob = '';
			mediaRecorder.start();
			isRunning = true;
			interval = setInterval(() => {
				time += 1;
			}, 1000);
		} catch (error) {
			console.error('Error starting recording:', error);
		}
	};

	const stopRecording = () => {
		if (mediaRecorder && isRunning) {
			mediaRecorder.stop();

			clearInterval(interval);
			time = 0;
			isRunning = false;
			audioRecordedButNotYetSaved = true;
		}
	};

	const uploadAudio = async (): Promise<string> => {
		if (audioBlobToSave) {
			const formData = new FormData();
			formData.append('file', audioBlobToSave, `${id}_${getCurrentDateTime()}.mp3`);
			audioRecordedButNotYetSaved = false;
			isUploading = true;
			return new SentenceApiClient()
				.uploadAudio(formData, sentence)
				.finally(() => (isUploading = false));
		}
	};

	const onClickPaperClip = () => {
		input.click();
	};

	const handleUploadFromFile = async (e: Event) => {
		const target = e.target as HTMLInputElement;

		if (target.files) {
			const newBlob = (await fileToBlob(target?.files[0])) as Blob;
			audioBlobToSave = new File([newBlob], `${id}_${getCurrentDateTime()}.mp3`, {
				type: newBlob.type,
				lastModified: Date.now()
			}) as File;
			audioBlob = URL.createObjectURL(audioBlobToSave);

			await uploadAudio().then((response) => {
				audioBlob = '';
				audioUrl = response;
				dispatcher('audioUploaded');
			});
		}
	};

	const handleUploadFromRecord = async () => {
		await uploadAudio().then((response) => {
			audioBlob = '';
			audioUrl = response;
			dispatcher('audioUploaded');
		});
	};

	const fileToBlob = async (file: File) =>
		new Blob([new Uint8Array(await file.arrayBuffer())], { type: `audio/mp3` });

	const formatTime = (timeInSeconds: number) => {
		const minutes = Math.floor(timeInSeconds / 60)
			.toString()
			.padStart(2, '0');
		const seconds = (timeInSeconds % 60).toString().padStart(2, '0');
		return `${minutes}:${seconds}`;
	};

	const onRecordingCanceled = () => {
		audioBlob = '';
		audioRecordedButNotYetSaved = false;
		dispatcher('audioCanceled');
	};
</script>

<div class="flex flex-col">
	<div class="flex items-center gap-3">
		{#if !isRunning && !audioBlob}
			<BaseButton {disabled} on:click={startRecording}>
				<IconPlayerRecordFilled />
			</BaseButton>
		{:else if !isRunning && audioBlob}
			<BaseButton on:click={onRecordingCanceled} disabled={isUploading}>
				<IconRepeat />
			</BaseButton>
		{:else}
			<BaseButton on:click={stopRecording}>
				<IconPlayerStopFilled />
			</BaseButton>
		{/if}

		{#if audioBlob}
			<BaseButton
				className="pulse-ping-animation"
				on:click={handleUploadFromRecord}
				disabled={isUploading}
			>
				<IconCloudUpload />
			</BaseButton>
		{:else if audioUrl}
			<form>
				<input
					bind:this={input}
					on:change={(e) => {
						handleUploadFromFile(e);
					}}
					name="inputFile"
					class="hidden"
					type="file"
					accept="audio/*"
				/>
			</form>
			<BaseButton disabled={disabled}  on:click={onClickPaperClip}>
				<IconPaperclip />
			</BaseButton>
		{:else}
			<form>
				<input
					bind:this={input}
					on:change={(e) => {
						handleUploadFromFile(e);
					}}
					name="inputFile"
					class="hidden"
					type="file"
					accept="audio/*"
				/>
			</form>

			<BaseButton {disabled} on:click={onClickPaperClip}>
				<IconPaperclip />
			</BaseButton>
		{/if}
		{#if isRunning}
			<div class="flex gap-2 text-base text-error items-center">
				{formatTime(time)}
				<div class="w-4 h-4 bg-red-600 rounded-full animate-pulse"></div>

				<p>Recording...</p>
			</div>
		{:else if isUploading}
			<LoadingSpinner2 />
		{:else}
			<audio class="w-full" controls src={audioToPlay ? audioToPlay : audioBlob}></audio>
		{/if}
	</div>
</div>

<style>
	audio {
		height: 2.3em;
	}
</style>

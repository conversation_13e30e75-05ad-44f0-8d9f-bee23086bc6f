{"title": "Groups", "new": "Create", "table": {"head": {"name": "Name", "dateStart": "Start Date", "language": "Language", "level": "Level", "days": "Schedule", "hoursSchedule": "Shift", "whatsapp": "Whatsapp Link", "comment": "Comment", "isActive": "Is Active", "studentsCount": "Students Count", "statistics": "Statistics"}}, "filters": {"title": {"groupName": "Group name", "active": "Active", "type": "Type", "lang": "Language", "level": "Level", "hoursSchedule": "Shift"}, "values": {"hoursSchedule": {"morning": "morning", "day": "day", "evening": "evening"}, "isActive": {"active": "Active", "inActive": "Inactive"}, "type": {"public": "sandbox", "ragil": "With students"}}}, "modal": {"title": {"create": "Creating group", "update": "Updating group"}, "buttons": {"submit": "Submit", "cancel": "Cancel"}, "formFields": {"titles": {"hoursSchedule": "Shift", "startTime": "Start time", "endTime": "End time", "capacity": "Capacity", "dateEnd": "End date", "dateStart": "Start date", "lang": "Language", "level": "Level", "sandbox": "sandbox", "whatsAppLink": "WhatsApp link", "comment": "Comment", "name": "Name", "active": "Active", "totalHoursAmount": "Total Hours Amount", "hoursSpendBySession": "Hours spend by session"}, "values": {"hoursSchedule": {"morning": "morning", "day": "day", "evening": "evening"}}}, "formFieldsErrors": {"daysSchedule": "Choose at least one day", "timeStart": "The start time must not be empty", "timeEnd": "The end time must not be empty", "dateStart": "The date start must not be empty", "dateEnd": "The date end must not be empty", "timeStartMoreThenTimeEnd": "The start time cannot be longer than the end time", "dateStartMoreThenDateEnd": "The start date cannot be greater than the end date", "totalHoursAmountMin": "Total hours cannot be less than 10", "totalHoursAmountMax": "Total hours cannot be more than 1000", "hoursSpendBySessionMin": "Hours spend by session cannot be less 1", "hoursSpendBySessionMax": "Hours spend by session cannot be more than 10", "totalHoursAmountInvalidType": "Total hours must be a number!", "hoursSpendBySessionInvalidType": "Hours spend by session must be a number!"}}, "modalDeleteSchedule": {"title": "Are you sure?", "body": "This action will delete the schedule", "buttonTextConfirm": "Confirm", "buttonTextCancel": "Cancel", "notifications": {"success": "Schedule successfully deleted", "cancel": "The deletion has been canceled", "error": "Try again, something went wrong..."}}, "scheduleTable": {"title": "Schedule Changes", "tableTitles": {"date": "Date", "days": "Days", "hoursPerSession": "Hours per session", "action": "Action"}, "buttons": {"edit": "Edit"}, "modal": {"title": {"create": "Create schedule", "update": "Update schedule"}, "formFields": {"date": "Date", "days": "Days", "hoursPerSession": "Hours per session"}, "buttons": {"create": "Create", "update": "Update", "cancel": "Cancel"}}, "notifications": {"create": "Schedule successfully created", "update": "Schedule successfully updated"}}, "tabsName": {"tasks": "Tasks", "students": "Students", "settings": "Settings", "history": "Group history"}, "infoGroup": {"hoursSchedule": {"morning": "Morning", "day": "Day", "evening": "Evening"}, "isActive": {"active": "Active", "inActive": "InActive"}, "isPublic": {"public": "Sandbox", "nonpublic": "Regular"}, "hours": "Hours", "days": "Days", "progress": "Progress"}, "groupSettings": {"stop": "Stop group", "modal": {"stop": "Stop group", "buttons": {"cancel": "Cancel", "save": "Save"}, "notifications": {"success": "The group has been successfully stopped"}}, "holidayTable": {"title": "Holidays", "typeLocal": "Local", "typeGlobal": "Global", "tableTitles": {"date": "Date", "comment": "Comment", "type": "Type", "action": "Action"}, "buttons": {"delete": "Delete", "save": "Save"}}}, "studentAdditionalTask": {"title": "Tasks", "table": {"name": "Name", "comment": "Comment", "done": "Done In", "result": "result", "attempts": "attempts", "actions": "actions"}, "breadcrumb": {"students": "Students"}}, "groupHistory": {"table": {"name": "Full name", "group": "Current group", "regDate": "Date of registration", "start": "Date start", "end": "Date end", "taskStart": "Date start task", "comment": "Comment", "lastTaskScore": "Last task result", "averageTaskScore": "Average task score", "lastTaskDelay": "Last task delay", "averageTaskDelay": "Average task delay", "whatsapp": "Whatsapp", "statistics": "Statistics", "action": "Action"}}, "notifications": {"create": {"success": "The group create was successful"}, "update": {"success": "The group update was successful"}}, "edit": "Edit group", "groups": "Groups", "info": "Group info", "comment": "Comment"}
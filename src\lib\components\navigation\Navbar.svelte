<script>
    import {AppBar, LightSwitch} from '@skeletonlabs/skeleton';
    import {getDrawerStore} from '@skeletonlabs/skeleton';
    import {IconMenu2} from '@tabler/icons-svelte';
    import LoadingContainer from '$components/common/LoadingContainer.svelte';
    import BaseSwitchHebrew from '$components/common/BaseSwitchHebrew.svelte';
    import {HebrewFontState} from '$lib/state/hebrew-font-state';
    import {navigating, page} from "$app/stores";
    import {Constants} from "$api/core/constants";
    import {LoadingState} from "$lib/state/loading-state";
	import { Routes } from '$common/core/routes';

    export let fullname = '';
    const drawerStore = getDrawerStore();
    $: loading = $LoadingState || Boolean($navigating);


    const drawerOpen = () => {
        drawerStore.open();
    };

    $:role = $page?.data?.user?.role;

    $:hideBurgerMenu = $page.data.pathname === Routes.StudentLogin || $page.data.pathname === Routes.Login;
</script>

<AppBar class="pattern bg-surface-50-900-token shadow-sm">
    <svelte:fragment slot="lead">
        <div dir="ltr" class="flex items-center gap-3">
            <a class="btn btn-ghost normal-case text-xl font-bold p-2"
               href="/login">{$page.data.envs.VITE_HEADER_TITLE || 'Hebreway'}</a>
            <div class="xs:hidden lg:flex flex-col gap-y-1">
                <LightSwitch width="w-[70px]" class="z-[999]" dir="ltr"/>
                {#if role === Constants.StudentRole}
                    <BaseSwitchHebrew bind:state={$HebrewFontState}/>
                {/if}
            </div>
        </div>
    </svelte:fragment>
    {#if loading}
        <div>
            <LoadingContainer/>
        </div>
    {:else if !loading && $page.data.isDev}
        <div class="w-fit mx-auto flex justify-center text-white font-bold bg-green-500 p-2">
            DEV
        </div>
    {/if}
    <svelte:fragment slot="trail">
        <div class="flex items-baseline gap-3">
            <div on:click={drawerOpen} class="{hideBurgerMenu? 'hidden':''} sm:block md:hidden h-8 w-8">
                <label class="btn-circle  align-center items-center" for="drawer">
                    <IconMenu2 size="30"/>
                </label>
            </div>
            <div class="xs:hidden lg:flex items-center" dir="auto">
                {#if fullname.length > 1}
                    <span>👋 {fullname}</span>
                {/if}
            </div>
        </div>
    </svelte:fragment>
</AppBar>

<style>
    :global(.pattern) {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 4 4'%3E%3Cpath fill='%239C92AC' fill-opacity='0.55' d='M1 3h1v1H1V3zm2-2h1v1H3V1z'%3E%3C/path%3E%3C/svg%3E");
    }
</style>

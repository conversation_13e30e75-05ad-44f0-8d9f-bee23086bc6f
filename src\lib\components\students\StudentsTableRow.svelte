<script lang="ts">
    import {modeCurrent} from "@skeletonlabs/skeleton";
    import {goto} from "$app/navigation";
    import {page} from "$app/stores";
    import {StudentFilterState} from "$lib/state/student-filter-state.js";
    import {format} from "date-fns";
    import {calculateStudentStudyHoursAndDays, durationToMinutesToHuman} from "$lib/common/utils.js";
    import {t} from "$lib/i18n/config.js";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import ResultBadge from "$components/t/ResultBadge.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconEdit, IconSend} from "@tabler/icons-svelte";
    import {createEventDispatcher, onMount} from "svelte";


    export let row;
    export let groupId;

    export let generalHolidays;

    export let fromGroup;


    const id = $page.params.id;

    const dispatchModal = createEventDispatcher();

    export let handleClickToRow: (arg: string) => void;

    let statistics = {totalHours: 0, totalDays: 0}

    let calculateStatisticFromGroup = () => {
        let scheduleInGroup = row?.studentLearningHistory.find((el) => el.groupId === id);
        return calculateStudentStudyHoursAndDays([{...scheduleInGroup}], generalHolidays);
    }


    let calculateStatistic = () => {
        return calculateStudentStudyHoursAndDays(row?.studentLearningHistory, generalHolidays);
    }

    $: statistics = row && fromGroup ? calculateStatisticFromGroup() : calculateStatistic();

</script>


<tr on:click={() => handleClickToRow(row.id)} class="cursor-pointer  ">
    <td class="max-w-[150px] min-w-[150px] pinned-cell{!$modeCurrent?'-dark':''} ">{`${row.firstname} ${row.lastname}`}</td>
    <td class="!max-w-[170px] !min-w-[170px] pinned-cell{!$modeCurrent?'-dark':''} !p-2">
        {#if row.whatsApp}
            <a href="https://web.whatsapp.com/send/?phone={row.whatsApp}&type=phone_number&app_absent=0"
               target="_blank">
                <BaseButton size="sm" className="bg-green-800 w-full">
                    <span dir="auto">{row.whatsApp}</span>
                    <IconSend size={20} stroke="1.5"/>
                </BaseButton>
            </a>
        {:else}
            <p style="direction: ltr" class="text-right">{row.phone}</p>
        {/if}
    </td>
    <td class="!max-w-[230px] !min-w-[180px] !p-0 !m-0 pinned-cell{!$modeCurrent?'-dark':''}"
        on:click={async () => await goto(`${$page.url.origin}/groups/${groupId}`)}>
        <div class="w-full flex font-medium !p-1 !m-0 text-blue-600 dark:text-blue-400 hover:underline badge text-sm btn btn-ghost">
            {row.currentGroup}
        </div>
    </td>
    {#if $StudentFilterState.showFullTable}
        <td>{row.tz}</td>
        <td>{row.email}</td>
    {/if}
    <td>{row.registrationDate ? format(new Date(row.registrationDate), 'dd.MM.yyyy') : ''}</td>
    <td>{row.currentGroupStartDate ? format(new Date(row.currentGroupStartDate), 'dd.MM.yyyy') : ''}</td>
    <td>{row.comment ? row.comment : ''}</td>
    <td>
        <ResultBadge result={row.lastTaskScore ? row.lastTaskScore : 0}/>
    </td>
    <td>
        <ResultBadge result={row.averageTaskScore ? row.averageTaskScore : 0}/>
    </td>
    <td>{durationToMinutesToHuman(row.lastTaskDelay)}</td>
    <td>{durationToMinutesToHuman(row.averageTaskDelay)}</td>
    <OnlyForRole>
        <td class="min-w-[100px]">
            <div class="flex flex-col">
                <p class="mb-1 text-center">
                   <span class="inline-block">{$t('students.students.table.hours')}
                       : <b>{statistics.totalHours}</b></span>
                </p>
                <p class="text-center">
                   <span class="inline-block">{$t('students.students.table.days')}
                       : <b>{statistics.totalDays}</b></span>
                </p>
            </div>


        </td>
    </OnlyForRole>


    <td class="flex items-center gap-2">
        <OnlyForRole>
            <BaseButton
                    size="sm"
                    on:click={(e) => {
                        e.stopPropagation();
                        dispatchModal('triggerEditModal', { action: 'update', row });
                     }}
            >
                {$t('students.students.table.editButton')}
                <IconEdit size={20} stroke="1.5"/>
            </BaseButton>
        </OnlyForRole>
        <BaseButton
                size="sm"
                on:click={(e) => {
                        e.stopPropagation();
                        dispatchModal('triggerSendMessageModal', {
                           recipientId: row.id,
                           recipientName: `${row.firstname} ${row.lastname}`,
                           currentGroup: row.currentGroup
                        });
                     }}
        >
            <IconSend size={20} stroke="1.5"/>
        </BaseButton>
    </td>
</tr>


<style lang="css">
    .pinned-cell {
        background: rgb(var(--color-surface-300));
        color: black !important;
    }


    .pinned-cell-dark {
        background: rgb(var(--color-surface-800));
        color: rgb(var(--theme-font-color-dark)) !important;
    }


    td:first-child {
        position: sticky;
        top: 0;
        right: 0;
        z-index: 10;
    }


    td:nth-child(2) {
        position: sticky;
        top: 0;
        right: 150px;
        z-index: 10;
    }


    td:nth-child(3) {
        position: sticky;
        top: 0;
        right: 320px;
        z-index: 10;
        overflow: hidden;
    }
</style>

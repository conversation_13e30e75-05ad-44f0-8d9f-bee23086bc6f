<script>
    import {IconRefresh} from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {
        getCertainCompletionState
    } from '$lib/state/task-current-completion.state';
    import {page} from '$app/stores';
    import _ from 'lodash';
    import {t} from '$lib/i18n/config';
    import {generateInitialTranslationsResult} from "$lib/common/task-helpers";
    import ResultBadge from "$components/t/ResultBadge.svelte";

    export let allowRetry = true;
    export let useTempState = false;

    $: currentState = getCertainCompletionState($page.params.id, !useTempState);

    const tryAgain = () => {
        try {
            $currentState.currentResult = generateInitialTranslationsResult(
                $currentState.currentMode,
                _.shuffle($currentState.task.sentences),
                $currentState.task?.lang || 'EN',
                true
            );
            $currentState.intermediateState = false;
        } catch (e) {
            console.log(e);
        }
    };
</script>

<div class="text-md font-bold self-center flex flex-col justify-center items-center mb-5">
    <p class="mb-1 text-lg italic">
        <ResultBadge text={$t('t.resultBlock.score')} result={$currentState?.currentResult?.scorePercent} size="2xl"/>
    </p>
    {#if allowRetry}
        <BaseButton on:click={tryAgain} className="w-full">
            {$t('t.resultBlock.tryAgain')}
            <IconRefresh/>
        </BaseButton>
    {/if}
</div>

<script lang="ts">
    import BaseButton from '$components/common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import {focusTrap} from '@skeletonlabs/skeleton';
    import {superForm} from 'sveltekit-superforms/client';
    import {onMount} from "svelte";
    import Cleave from 'cleave.js/dist/cleave';
    import {page} from "$app/stores";

	const clearStudentSpecificLocalStorageItems = () => {
		const uuidTaskRegex = new RegExp('^[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}$')
		for (let i = 0; i < localStorage.length; i++) {
			if (uuidTaskRegex.test(localStorage.key(i))) {
				localStorage.removeItem(localStorage.key(i));
			}
		}
	}

    const {form, errors, enhance} = superForm();

    $:disabled = !$form.tz || $form.dob?.length !== 10;

    onMount(() => {
        new Cleave('.input-element', {
            date: true,
            delimiter: '-',
            dateMin: '1923-01-01',
            dateMax: '2016-01-01',
            datePattern: ['d', 'm', 'Y'],
        });

		clearStudentSpecificLocalStorageItems();
    })

</script>

<div class="w-full max-h-full flex items-center justify-center h-[80vh]" dir="ltr">
	<form
			method="POST"
			class="space-y-6 lg:w-1/4 sm:w-1/2 flex flex-col gap-0"
			action="#"
			use:enhance
			use:focusTrap={true}
	>
		<div class=" h-20">
			<label for="studentTz" class="block text-sm font-medium text-gray-900 dark:text-white"
			>{$t('sign-in.formFields.tz')}</label
			>
			<input
					type="text"
					name="tz"
					id="studentTz"
					bind:value={$form.tz}
					class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white"
					placeholder="346874682"
			/>
			{#if $errors?.tz}
				<div class="text-error-500-400-token">{$t($errors.tz)}</div>
			{/if}
		</div>
		<div class="h-20 ">
			<label for="dob" class="block text-sm font-medium text-gray-900 dark:text-white"
			>{$t('sign-in.formFields.dob')}
			</label>
			<input
					bind:value={$form.dob}
					id="dob"
					name="dob"
					class="input-element appearance-none bg-gray-50 border-2 border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 sm:block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white"
					placeholder="day-month-year" type="text"/>
			{#if $errors?.dob}
				<div class="text-error-500-400-token">{$t($errors.dob)}</div>
			{/if}
			{#if $page.form?.error}
				{#if $page.form?.error?.known}
					<div class="text-error-500-400-token">{$t($page.form?.error?.message)}</div>
				{:else}
					<div class="text-error-500-400-token">{$page.form?.error}1</div>
				{/if}
			{/if}
		</div>

		<BaseButton {disabled} type="submit">{$t('sign-in.button')}</BaseButton>
	</form>
</div>


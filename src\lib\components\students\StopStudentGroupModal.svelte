<script lang="ts">
	import { GroupHistoryState } from '$lib/state/group-history-state';
	import SveltyPicker from 'svelty-picker';
	import { getModalStore } from '@skeletonlabs/skeleton';
	import BaseButton from '$components/common/BaseButton.svelte';
	import { IconCircleLetterX, IconDeviceFloppy } from '@tabler/icons-svelte';
	import BaseInput from '$components/common/BaseInput.svelte';
	import { areIntervalsOverlapping } from 'date-fns';
	import { deserialize } from '$app/forms';
	import { invalidate } from '$app/navigation';
	import { onMount } from 'svelte';
	import BreadcrumbStudentGroupModal from '$components/students/BreadcrumbStudentGroupModal.svelte';
	import { StudentEditModalState } from '$lib/state/student-edit-state';
	import { t } from '$lib/i18n/config';
	import NotificationStore from "$lib/state/notification-state";
	import {NotificationType} from "$common/models/enums";

	$: disableSave = !$GroupHistoryState.selectedGroup.dateEndActual;
	let message = '';
	let minimumValueDateStartActual: null | Date;
	let maximumValueDateEndActual: null | Date;
	const modalStore = getModalStore();

	const isDateIntersects = () => {
		const groupsHistoryWithoutSelectedGroup = $GroupHistoryState.groupsHistory.filter(
			(element) => element.groupId !== $GroupHistoryState.selectedGroup.groupId
		);

		if (!$GroupHistoryState.selectedGroup.dateEndActual) return;

		const elementOverlapping = groupsHistoryWithoutSelectedGroup.find((studentGroupRecord) => {
			if (
				areIntervalsOverlapping(
					{
						start: new Date($GroupHistoryState.selectedGroup.dateStartActual),
						end: new Date($GroupHistoryState.selectedGroup.dateEndActual)
					},
					{
						start: new Date(studentGroupRecord.dateStartActual),
						end: new Date(studentGroupRecord.dateEndActual)
					}
				)
			) {
				return studentGroupRecord;
			}
		});
		return elementOverlapping ? elementOverlapping.group.name : null;
	};

	const onChangeDate = () => {
		const result = isDateIntersects();
		if (result) {
			message = `${$t('students.messageerror')} ${result}`;
			disableSave = true;
		} else {
			message = '';
			disableSave = false;
		}
	};

	async function handleSubmit(event) {
		const data = new FormData(this);
		const response = await fetch(this.action, {
			method: 'POST',
			body: data,
			headers: {
				'x-sveltekit-action': 'true'
			}
		});

		const result = deserialize(await response.text());
		if (result.type === 'success') {
			await invalidate('load:students');
			modalStore.close();
			NotificationStore.push({
				type: NotificationType.success,
				message: t.get('students.students.notifications.stopStudentGroup.success')
			}, 5);
		}
	}

	onMount(() => {
		const el = $GroupHistoryState.groups.find(
			(group) => group.id === $GroupHistoryState.selectedGroup.groupId
		);
		const selectedGroup = $GroupHistoryState.selectedGroup;
		if (el) {
			minimumValueDateStartActual = new Date(selectedGroup.dateStartActual);
			maximumValueDateEndActual = new Date(el.dateEnd);
		}
	});
</script>

<div class="rounded-xl modal card sm:w-2/3 lg:w-[650px] shadow-xl space-y-4 p-5">
	<BreadcrumbStudentGroupModal
		currentModalName={`${$GroupHistoryState.selectedGroup.group.name} ${$t(
			'students.students.modal.stopStudentGroupModal.stop'
		)}   ${$t('students.students.modal.stopStudentGroupModal.group')} `}
	/>

	<form
		on:submit|preventDefault={handleSubmit}
		method="POST"
		action="?/stopCurrentGroup"
		class="flex flex-col items-center justify-between gap-10 p-2"
	>
		<header dir="auto" class="text-2xl font-bold">
			{$t('students.students.modal.stopStudentGroupModal.title')}
		</header>
		<label for="id" class="hidden">
			<BaseInput name="id" bind:value={$GroupHistoryState.selectedGroup.id} />
		</label>
		<div class="hidden">
			<BaseInput name="studentId" value={$StudentEditModalState.id} />
		</div>

		<div class="self-start">
			<span class="block input__title font-medium text-base mb-1">
				{$t('students.students.modal.stopStudentGroupModal.fields.dateend')}
			</span>
			<SveltyPicker
				format="yyyy-mm-dd"
				weekStart={1}
				name="dateEndActual"
				mode="date"
				required={true}
				startDate={minimumValueDateStartActual}
				initialDate={$GroupHistoryState.selectedGroup.dateEndActual}
				bind:value={$GroupHistoryState.selectedGroup.dateEndActual}
				inputClasses="relative w-full input rounded h-10 p-2"
				todayBtn={false}
				on:change={onChangeDate}
			/>
		</div>
		{#if message}
			{message}
		{/if}

		<div class="flex justify-between w-full">
			<BaseButton disabled={disableSave} type="submit">
				<IconDeviceFloppy />
				{$t('students.buttons.save')}
			</BaseButton>
			<BaseButton on:click={() => modalStore.close()}>
				<IconCircleLetterX />
				{$t('students.buttons.cancel')}
			</BaseButton>
		</div>
	</form>
</div>

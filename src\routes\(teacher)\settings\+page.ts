import type {PageLoad} from './$types';
import {ExclusionsApiClient} from "$lib/core/api-clients/exclusions-api-client";
import {AboutContentsApiClient} from "$lib/core/api-clients/aboutContents-api.client";
import {GeneralHolidaysApiClient} from "$lib/core/api-clients/generalHolidays-api.client";
import {SettingsApiClient} from "$lib/core/api-clients/settings-api.client";

export const ssr = false;

export const load: PageLoad = async ({depends}) => {
    try {
        depends('load:settings');
        return {
            exclusions: new ExclusionsApiClient().getExclusions(),
            aboutContents: new AboutContentsApiClient().getAboutContents(),
            generalHolidays: new GeneralHolidaysApiClient().getGeneralHolidays(),
            settings: new SettingsApiClient().getSettings()
        }
    } catch (error) {
        return error;
    }
};
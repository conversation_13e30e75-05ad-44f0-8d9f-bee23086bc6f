// Alternative OpenTelemetry setup that disables Application Insights OpenTelemetry
// Use this approach if the main setup still causes conflicts

const isProd = process.env.NODE_ENV === 'production';

if (isProd) {
    console.log('Initializing OpenTelemetry with Application Insights compatibility...');
    
    try {
        // Disable Application Insights automatic OpenTelemetry initialization
        process.env.APPLICATIONINSIGHTS_DISABLE_OPENTELEMETRY = 'true';
        
        // Import and initialize OpenTelemetry instrumentation BEFORE other modules
        const { NodeSDK } = require('@opentelemetry/sdk-node');
        const { Resource } = require('@opentelemetry/resources');
        const { ATTR_SERVICE_NAME, ATTR_SERVICE_VERSION } = require('@opentelemetry/semantic-conventions');
        const { AzureMonitorTraceExporter } = require('@azure/monitor-opentelemetry-exporter');
        
        // Import specific instrumentations
        const { RedisInstrumentation } = require('@opentelemetry/instrumentation-redis');
        const { Redis4Instrumentation } = require('@opentelemetry/instrumentation-redis-4');
        const { HttpInstrumentation } = require('@opentelemetry/instrumentation-http');
        
        // Create resource with service information
        const resource = new Resource({
            [ATTR_SERVICE_NAME]: 'hebreway-server',
            [ATTR_SERVICE_VERSION]: '1.0.0',
        });

        // Get connection string for Azure Monitor
        const connectionString = process.env.APPLICATIONINSIGHTS_CONNECTION_STRING || 
                                `InstrumentationKey=${process.env.APPINSIGHTS_INSTRUMENTATIONKEY || process.env.VITE_APPINSIGHTS_INSTRUMENTATION_KEY}`;

        // Initialize the SDK with Azure Monitor exporter
        const sdk = new NodeSDK({
            resource,
            instrumentations: [
                new HttpInstrumentation(),
                new RedisInstrumentation(),
                new Redis4Instrumentation(),
            ],
            traceExporter: new AzureMonitorTraceExporter({
                connectionString: connectionString,
            }),
        });

        // Start the SDK
        sdk.start();
        
        console.log('OpenTelemetry SDK initialized successfully with Azure Monitor integration');
    } catch (error) {
        console.warn('Failed to initialize OpenTelemetry SDK:', error.message);
        // Don't throw - allow the application to continue without OpenTelemetry
    }
} else {
    console.log('Skipping OpenTelemetry initialization in development mode');
}

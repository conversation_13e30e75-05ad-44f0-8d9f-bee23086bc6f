<script lang="ts">
    import BaseButton from '$components/common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import type {PageData} from './$types';
    import {GroupFilterState} from '$lib/state/group-filter-state';
    import GroupFilter from '$components/groups/GroupFilter.svelte';
    import CountSpan from '$components/common/CountSpan.svelte';
    import GroupsTable from '$components/groups/GroupsTable.svelte';
    import {IconPlus} from '@tabler/icons-svelte';
    import {SendNotificationModalState} from '$lib/state/send-notification-state';
    import {generateGuid} from '$common/core/utils';
    import {get} from 'svelte/store';
    import {
        GroupPagingState,
        GroupSortingState,
        initialGroupPaging, initialGroupSorting,
        pageSize
    } from '$lib/state/group-paging-state';
    import {afterNavigate, invalidate} from '$app/navigation';
    import {onDestroy} from 'svelte';
    import {loadingWrap} from '$lib/common/utils';
    import {initialGroupFilter} from "$common/models/filters/group-filter.dto";
    import _ from "lodash";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import OnlyNotForRole from "$components/common/OnlyNotForRole.svelte";
    import {GroupEditModalState} from "$lib/state/group-edit-state";

    export let data: PageData;
    const modalStore = getModalStore();


    let firstLoad = true;
    $: groups = data.groups.data || [];
    $: count = data?.groups.count ?? 0;
    $:generalHolidays = data?.generalHolidays.data;


    const loadMore = async () => {
        if (count > groups.length) {
            GroupPagingState.set({take: pageSize + groups.length, skip: 0});
            await loadingWrap(async () => {
                await invalidate('load:groups');
            });
        }
    };

    const openCreateGroupModal = () => {
        $GroupEditModalState.generalHolidays = [...generalHolidays];
        modalStore.trigger({type: 'component', component: 'createGroupModal'});
    };

    const openSendNotificationGroupModal = (event) => {
        SendNotificationModalState.set({
            ...get(SendNotificationModalState),
            id: generateGuid(),
            type: 'group',
            title: `${event.detail.recipientName}`,
            recipientId: event.detail.recipientId
        });
        modalStore.trigger({type: 'component', component: 'sendNotificationGroupModal'});
    };

    const unsubscribeFilterState = GroupFilterState.subscribe(async () => {
        if (!firstLoad) {
            $GroupPagingState = _.cloneDeep(initialGroupPaging)
            await loadingWrap(async () => {
                await invalidate('load:groups');
            });
        }
        firstLoad = false;
    });

    const unsubscribeSort = GroupSortingState.subscribe(async () => {
        $GroupPagingState = _.cloneDeep(initialGroupPaging)
        await loadingWrap(async () => {
            await invalidate('load:groups');
        });
    });


    afterNavigate(() => {
        $GroupFilterState = _.cloneDeep(initialGroupFilter);
        $GroupSortingState = _.cloneDeep(initialGroupSorting)
    })

    onDestroy(() => {
        unsubscribeFilterState();
        unsubscribeSort();
    });
</script>

<div class="h-[calc(100vh-85px)] overflow-hidden flex flex-col px-6 ">
    <div class="mt-3">
        <h1 class="title mb-1 font-medium text-xl">
            {$t('groups.title')}
            <CountSpan bind:count/>
        </h1>
    </div>

    <div
            class="card mt-6 p-5 w-full text-token flex justify-between items-center variant-glass-primary"
    >
        <OnlyNotForRole width="w-3/4">
            <GroupFilter/>
        </OnlyNotForRole>

        <OnlyForRole>
            <div class="mt-0">
                <div dir="ltr" class="w-full">
                    <BaseButton on:click={() => openCreateGroupModal()}>
                        <IconPlus/>
                        {$t('groups.new')}
                    </BaseButton>
                </div>
            </div>
        </OnlyForRole>
    </div>

    <div class="mt-3 overflow-y-auto overflow-x-auto ">
        <GroupsTable {generalHolidays} loadMoreFunc={loadMore}
                     {openSendNotificationGroupModal} bind:groups/>
    </div>
</div>

import type {UserRole} from '$common/models/enums';
import type {Permission} from "$common/models/enums";


export interface UserDto {
    id: string;
    tz: string;
    phone: string;
    email: string;
    firstname: string;
    lastname: string;
    isActive?: boolean | null;
    role: UserRole;
    createdAt?: Date;
    createdBy?: string;
    updatedAt?: Date;
    updatedBy?: string;
    permissions: PermissionDto[]
}


export interface UserWithoutPasswordDto {
    id: string;
    tz: string;
    firstname: string;
    lastname: string;
    email: string;
    phone: string;
    role: 'teacher' | 'admin' | 'disabled';
    permissions: PermissionDto[]
}


export interface PermissionDto {
    id: string,
    permission: Permission.editFavorites | Permission.deleteNonFavSentences,
    userId: string,
    createdAt: Date | null,
    createdBy: string | null
}

export interface FormUserDto {
    id: string;
    tz: string;
    firstname: string;
    lastname: string;
    email: string;
    phone: string;
    password: string;
    role: 'teacher' | 'admin' | 'disabled';
    permissions: {
        deleteNonFavSentences: Permission.deleteNonFavSentences | null,
        editFavorites: Permission.editFavorites | null
    }
}

export type FormUserWithoutPasswordDto = Omit<FormUserDto, 'password'>

export interface FormStudentDto extends Omit<FormUserDto, 'password'> {
    currentGroup: string;
}

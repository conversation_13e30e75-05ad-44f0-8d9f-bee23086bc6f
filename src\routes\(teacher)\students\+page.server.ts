import { parseFormData } from 'parse-nested-form-data';
import { validateEntityBySchema } from '$lib/common/utils';
import { schemaForUpdateAndAcceptStudent, schemaForCreateStudentRequest } from '$lib/validation-schemes/accept-request';
import {
	deleteStudentGroup,
	handleStudentRequest,
	setDateEndActualToCurrentGroup,
	switchStudentGroup,
	updateStudent,
	updateStudentGroup,
	createStudentRequest
} from '$api/core/services/students.service';
import type { StudentRequestDto, ShortStudentRequestDto } from '$common/models/dtos/student-request.dto';
import type { Actions } from '@sveltejs/kit';
import { fail } from '@sveltejs/kit';
import { mapper } from '$common/core/mapper';
import type { StudentDto } from '$common/models/dtos/student.dto';
import type {
	StopCurrentGroupDto,
	SwitchGroupDto,
	UpdateStudentGroupDto
} from '$common/models/dtos/student-groups.dto';
import _ from "lodash";

export const ssr = false;

export const actions: Actions = {
	handleRequest: async ({ request }: { request: Request }) => {
		const studentRequest = parseFormData(await request.formData());
		const { result, errors } = validateEntityBySchema(
			studentRequest,
			schemaForUpdateAndAcceptStudent
		);
		if (!result) return fail(400, { error: true, errors });
		const isExistingStudent = studentRequest.isExistingStudent === 'true';

		return await handleStudentRequest(
			mapper<StudentRequestDto, unknown>({
				...studentRequest,
				firstname: studentRequest?.firstname?.toString().trim(),
				lastname: studentRequest?.lastname?.toString().trim(),
				isExistingStudent
			})
		);
	},
	updateStudent: async ({ request }: { request: Request }) => {
		const studentUpdate = _.omit(parseFormData(await request.formData()), [
			'isExistingStudent',
			'dob_input',
			'teudatOleUrl',
			'photoUrl'
		]);
		const { result, errors } = validateEntityBySchema(
			studentUpdate,
			schemaForUpdateAndAcceptStudent
		);

		if (!result) return fail(400, { error: true, errors });
		return await updateStudent(mapper<StudentDto, unknown>(studentUpdate));
	},
	deleteStudentGroup: async ({ request }: { request: Request }) => {
		const { id } = parseFormData(await request.formData());
		if (!id) return fail(400, { error: true, message: 'Id not found' });
		return await deleteStudentGroup(id.toString());
	},
	updateStudentGroup: async ({ request }: { request: Request }) => {
		const studentGroupToUpdate = parseFormData(await request.formData());
		const dto = mapper<UpdateStudentGroupDto, unknown>(studentGroupToUpdate);
		return await updateStudentGroup(dto);
	},
	stopCurrentGroup: async ({ request }: { request: Request }) => {
		const stopDate = _.omit(parseFormData(await request.formData()), ['dateEndActual_input']);

		return await setDateEndActualToCurrentGroup(mapper<StopCurrentGroupDto, unknown>(stopDate));
	},
	switchStudentGroup: async ({ request }: { request: Request }) => {
		const data = parseFormData(await request.formData());
		const {
			idCurrentGroup,
			dateEndCurrentGroup,
			idSelectedGroup,
			dateStartSelectedGroup,
			taskStartSelectedGroup,
			studentId
		} = data;
		const dtoStopGroup = mapper<StopCurrentGroupDto, unknown>({
			id: idCurrentGroup,
			dateEndActual: dateEndCurrentGroup,
			studentId: studentId
		});
		const dtoSwitchGroup = mapper<SwitchGroupDto, unknown>({
			idSelectedGroup,
			studentId,
			dateStartSelectedGroup: dateStartSelectedGroup,
			taskStartSelectedGroup: taskStartSelectedGroup
		});

		const currentGroup = dtoStopGroup.id ? await setDateEndActualToCurrentGroup(dtoStopGroup) : {};
		const selectedGroup = await switchStudentGroup(dtoSwitchGroup);

		return {
			currentGroup,
			selectedGroup
		};
	},
	createStudentRequest: async ({ request }: { request: Request }) => {
		const studentRequestData = parseFormData(await request.formData());
		const { result, errors } = validateEntityBySchema(
			studentRequestData,
			schemaForCreateStudentRequest
		);
		if (!result) return fail(400, { error: true, errors });

		const studentRequest = mapper<ShortStudentRequestDto, unknown>({
			...studentRequestData,
			firstname: studentRequestData?.firstname?.toString().trim(),
			lastname: studentRequestData?.lastname?.toString().trim(),
			isHandled: false,
			createdAt: new Date(),
			teudatOleUrl: '',
			photoUrl: '',
			rawRequest: JSON.stringify(studentRequestData)
		});

		return await createStudentRequest(studentRequest);
	}
};

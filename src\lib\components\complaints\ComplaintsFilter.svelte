<script lang="ts">
    import {t} from "$lib/i18n/config";
    import {ComplaintFilterState} from "$lib/state/complaint-filter-state";
    import {mapEnumToOptionsWithTranslations} from "$lib/common/utils";
    import {HandledComplaintFilter} from "$common/models/enums";
    import BaseSelect from "$components/common/BaseSelect.svelte";

    export let updaters;




    $: latestUpdaterOptions = [
        {displayValue: 'none', value: ''},
        ...updaters.reduce((acc, c) => {
            const existItem = acc.find((el) => el?.value === c?.task_sentences?.sentence?.updatedByUser?.id);
            if (!existItem && c?.task_sentences?.sentence?.updatedByUser) {
                acc.push({
                    displayValue: `${c?.task_sentences?.sentence?.updatedByUser?.firstname} ${c?.task_sentences?.sentence?.updatedByUser?.lastname}`,
                    value: c?.task_sentences?.sentence?.updatedByUser?.id
                })
            }
            return acc
        }, [])
    ];



</script>


<div class=" card mt-6 p-5 w-full text-token flex justify-between items-center variant-glass-primary">
    <div class="flex flex-row gap-10 items-baseline">
        <div class="w-fit">
            <BaseSelect title={$t('complaints.filter.titleHandled')}
                        bind:value={$ComplaintFilterState.isHandled}
                        options={mapEnumToOptionsWithTranslations(HandledComplaintFilter, 'complaints.filter.handledComplaintFilter', $t, true)}/>
        </div>
        <div>
            <BaseSelect title={$t('complaints.filter.titleUpdater')}
                        options={latestUpdaterOptions}
                        bind:value={$ComplaintFilterState.latestUpdater}
            />
        </div>
    </div>
</div>
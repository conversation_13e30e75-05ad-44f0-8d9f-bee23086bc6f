import { TaskApiClient } from '$lib/core/api-clients/task-api-client';
import type { PageLoad } from './$types';
import {ExclusionsApiClient} from "$lib/core/api-clients/exclusions-api-client";

export const ssr = false;

export const load: PageLoad = async ({ params }) => {
	const { id } = params;
	try {
		const client = new TaskApiClient();
		return {
			task: await client.getPublicTaskById(id),
			exclusions: await new ExclusionsApiClient().getExclusions()

		}
	} catch (error) {
		return error;
	}
};

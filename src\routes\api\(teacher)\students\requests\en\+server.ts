import { fail, json, type RequestEvent } from '@sveltejs/kit';
import { createStudentRequest, extractStudentRequest } from '$api/core/services/students.service';
import {config} from '$api/core/config';

/** @type {import('../../../../../../.svelte-kit/types/src/routes').RequestHandler} */
export const POST = async ({ request }: RequestEvent) => {
	if (request.headers.get('secret') === config.INTEGRATION_SECRET) {
		try {
			const data = await request.json();
			const studentRequest = extractStudentRequest(data.answer, 'en');
			const res = await createStudentRequest({ ...studentRequest, rawRequest: JSON.stringify(studentRequest) });

			return json(res ? 'added' : 'no need');
		} catch (e) {
			console.error(e);
			return fail(500);
		}
	}

	return fail(500);
};

export const GET = async () => json({success: true})
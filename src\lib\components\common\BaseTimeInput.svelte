<script lang="ts">
	export let value: string;
	export let name: string;
	export let title: string | null;
</script>

<div class="w-full">
	{#if title}
		<div class="title mb-1 font-medium text-base without-ampm">{title}</div>
	{/if}
	<input pattern="[0-24]{2}:[0-23]{2}" bind:value {name} on:change class="w-full input rounded p-[3.5px]" type="time" />
</div>


<style>
	.without-ampm::-webkit-datetime-edit-ampm-field {
		display: none;
	}

	input[type=time]::-webkit-clear-button {
		-webkit-appearance: none;
		-moz-appearance: none;
		-o-appearance: none;
		-ms-appearance:none;
		appearance: none;
		margin: -10px;
	}
</style>

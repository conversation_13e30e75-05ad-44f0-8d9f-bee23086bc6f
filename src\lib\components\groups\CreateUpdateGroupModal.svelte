<script lang="ts">
    import {page} from '$app/stores';
    import {HoursScheduleFilter, LanguageFilter, LevelFilter, NotificationType} from '$common/models/enums';

    import {
        calculationGroupStudyingTime,
        createNameGroup,
        formatToPickerDate,
        getDateString,
        getTimeByHoursSchedule, loadingWrap,
        mapEnumToOptions, calculateGroupEndDay
    } from '$lib/common/utils';
    import {t} from '$lib/i18n/config';
    import BaseButton from '../common/BaseButton.svelte';
    import BaseInput from '../common/BaseInput.svelte';
    import BaseSelect from '../common/BaseSelect.svelte';
    import BaseSwitch from '../common/BaseSwitch.svelte';
    import SveltyPicker from 'svelty-picker';
    import DailySchedule from './DailySchedule.svelte';
    import {deserialize} from '$app/forms';
    import {getModalStore, type ModalSettings} from '@skeletonlabs/skeleton';
    import {invalidate} from '$app/navigation';
    import FormErrorMessage from '$components/common/FormErrorMessage.svelte';
    import {IconCircleLetterX, IconDeviceFloppy} from '@tabler/icons-svelte';
    import {GroupEditModalState} from '$lib/state/group-edit-state';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import {mapper} from '$common/core/mapper';
    import {daysScheduleToDbFormatString} from '$lib/common/utils';
    import {generateGuid} from '$common/core/utils';
    import NotificationStore from "$lib/state/notification-state";
    import {onMount} from 'svelte';
    import {addYears} from 'date-fns';
    import BaseTimeInput from '$components/common/BaseTimeInput.svelte';
    import _ from "lodash";
    import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
    import GroupSchedulesTable from "$components/groups/GroupSchedulesTable.svelte";
    import NotificationState from "$lib/state/notification-state";
    import {GroupScheduleApiClient} from "$lib/core/api-clients/groupSchedule-api-client";

    let nextWeek = addYears(new Date(), 1);

    const formData = $GroupEditModalState;
    const id = $page.params?.id || '';
    const modalStore = getModalStore();

    let endDate;


    let dateStart = formData.dateStart
        ? getDateString(formData.dateStart)
        : getDateString(formatToPickerDate(new Date()))

    let dateEnd = formData.dateEnd
        ? getDateString(formData.dateEnd)
        : getDateString(formatToPickerDate(nextWeek));


    $:if (formData) {
        let endGroupLearning: Date | undefined;
        if (formData.action === 'create') {
            const sh = [{
                daysSchedule: daysScheduleToDbFormatString(formData.daysSchedule),
                hoursPerSession: formData.hoursSpendBySession,
                dateStart: dateStart
            }] as GroupScheduleDto[]
            endGroupLearning = calculateGroupEndDay(sh, formData.totalHoursAmount, {globalH: formData.generalHolidays});
        } else {
            endGroupLearning = calculateGroupEndDay(formData.groupScheduleChanges || [],
                formData.totalHoursAmount,
                {
                    globalH: formData.generalHolidays,
                    localH: formData.groupHolidays,
                    groupHExceptions: formData.groupHolidaysExc
                }
            );
        }
        if (endGroupLearning) {
            endDate = formatToPickerDate(new Date(endGroupLearning))
        } else {
            endDate = ''
        }
    }


    let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;

    $: capacity = calculationGroupStudyingTime(
        formData.daysSchedule,
        formData.timeStart,
        formData.timeEnd,
        dateStart!,
        dateEnd!
    );


    $: groupName = createNameGroup(dateStart!, formData.hoursSchedule, formData.level, formData.lang);

    onMount(() => {
        additionalName = $GroupEditModalState.name
            ? $GroupEditModalState.name.replace(`${groupName}`, '')
            : '';
    });

    let additionalName = '';

    function handleDayChange(event: Event) {
        const target = event.target as HTMLInputElement;
        const value = target?.checked ? 1 : 0;
        formData.daysSchedule[target?.name] = value;
    }

    const composeGroupFullName = () => {
        return $GroupEditModalState.isPublic
            ? additionalName
            : `${groupName}${
                additionalName
                    ? additionalName.startsWith('_')
                        ? additionalName
                        : '_' + additionalName
                    : ''
            }`;
    };

    const clearIdenticalFields = () => {
        $GroupEditModalState.comment = '';
        additionalName = '';
        formData.lang = LanguageFilter.EN;
    };

    async function handleSubmit(event) {
        const dataToSent = new FormData();
        let dataToParse;

        if (!formData.isPublic) {

            dataToParse = _.omit(mapper<GroupDto, any>({
                ...formData,
                name: composeGroupFullName(),
                dateStart,
                dateEnd: endDate,
                daysSchedule: daysScheduleToDbFormatString(formData.daysSchedule),
                id: id ? id : generateGuid()
            }), ['action', 'groupScheduleChanges', 'generalHolidays', 'groupHolidays', 'groupHolidaysExc'])
        } else {
            dataToParse = _.omit(mapper<GroupDto>({
                ...formData,
                id: id ? id : generateGuid(),
                name: composeGroupFullName(),
                dateStart: getDateString(formatToPickerDate(new Date())),
                dateEnd: getDateString(formatToPickerDate(new Date())),
                daysSchedule: '11111',
                hoursSchedule: 0,
                isActive: true,
                level: 0,
                timeStart: '04:20',
                timeEnd: '16:20'
            }), ['action', 'groupScheduleChanges', 'generalHolidays', 'groupHolidays', 'groupHolidaysExc'])
        }

        for (const key in dataToParse) {
            dataToSent.append(key, dataToParse[key]);
        }

        const response = await fetch(this.action, {
            method: 'POST',
            body: dataToSent,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });


        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            modalErrors = result.data.errors;
        } else {
            modalStore.close();
            if (formData.action === 'create') {
                NotificationStore.push({
                    type: NotificationType.success,
                    message: t.get('groups.notifications.create.success')
                }, 5);
            } else {
                NotificationStore.push({
                    type: NotificationType.success,
                    message: t.get('groups.notifications.update.success')
                }, 5);
            }
            await loadingWrap(async () => {
                await invalidate('load:groups');
                await invalidate('load:groups/id');
            });
        }
    }

    const openUpdateScheduleChangesModal = (action: 'create' | 'update', index?: number, groupSchedule?: GroupScheduleDto) => {
        if (action === 'update') {
            modalStore.trigger({
                type: 'component',
                component: 'updateScheduleChangesModal',
                meta: {
                    ...groupSchedule,
                    action,
                    updatedElementIndex: index
                }
            })
        } else {
            modalStore.trigger({
                type: 'component',
                component: 'updateScheduleChangesModal',
                meta: {
                    id: generateGuid(),
                    groupId: id,
                    daysSchedule: '00000',
                    hoursPerSession: '1',
                    createdAt: new Date(),
                    action
                }
            })
        }

        $modalStore = $modalStore.reverse();
    }


    const deleteScheduleChanges = async (id: string) => {
        const modalResult = await new Promise<boolean>((resolve) => {
            const modal: ModalSettings = {
                type: 'confirm',
                title: `<h1 dir="auto">${$t('groups.modalDeleteSchedule.title')}</h1>`,
                body: `<p dir="auto">${$t('groups.modalDeleteSchedule.body')}</p>`,
                buttonTextConfirm: $t('groups.modalDeleteSchedule.buttonTextConfirm'),
                buttonTextCancel: $t('groups.modalDeleteSchedule.buttonTextCancel'),
                response: (r: boolean) => resolve(r)
            };
            modalStore.trigger(modal);
            $modalStore = $modalStore.reverse();
        });
        if (modalResult) {
            const result = await new GroupScheduleApiClient().deleteGroupScheduleChange(id);
            if (result) {
                NotificationState.push({
                    type: NotificationType.success,
                    message: $t('groups.modalDeleteSchedule.notifications.success')
                });
                await loadingWrap(async () => {
                    await invalidate('load:groups');
                    await invalidate('load:groups/id');
                });
                modalStore.close()
            } else {
                NotificationState.push({
                    type: NotificationType.error,
                    message: $t('groups.modalDeleteSchedule.notifications.error')
                });
            }
        } else {
            NotificationState.push({
                type: NotificationType.success,
                message: $t('groups.modalDeleteSchedule.notifications.cancel')
            });
        }
    }

    function testChange() {
        formData.timeStart = getTimeByHoursSchedule(formData.hoursSchedule).startInitialTime;
        formData.timeEnd = getTimeByHoursSchedule(formData.hoursSchedule).endInitialTime;
    }

</script>

<form
        action={`?/${formData.action}`}
        on:submit|preventDefault={handleSubmit}
        class="modal card p-8 w-1/2 shadow-xl space-y-4 overflow-hidden"
>
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold mb-8">{$t(`groups.modal.title.${formData.action}`)}</h1>
        <BaseSwitch
                name="&isPublic"
                title={$t('groups.modal.formFields.titles.sandbox')}
                bind:checked={formData.isPublic}
                on:change={clearIdenticalFields}
        />
    </div>
    {#if !formData.isPublic}
        <div class="flex flex-col gap-10 w-full">
            <div class="flex flex-col gap-10 w-full h-full overflow-y-auto max-h-[500px] pl-2">
                <div class="flex gap-4 flex-wrap">
                    <div>
                        <BaseSelect
                                on:change={testChange}
                                bind:value={formData.hoursSchedule}
                                name="hoursSchedule"
                                title={$t('groups.modal.formFields.titles.hoursSchedule')}
                                options={mapEnumToOptions(HoursScheduleFilter)}
                                className="h-9 p-0"
                        />
                    </div>
                    <div class="max-w-[90px]">
                        <BaseTimeInput
                                bind:value={formData.timeStart}
                                title={$t('groups.modal.formFields.titles.startTime')}
                                name="timeStart"
                        />
                        <FormErrorMessage {modalErrors} fieldName="timeStart"/>
                    </div>
                    <div class="max-w-[85px]">
                        <BaseTimeInput
                                bind:value={formData.timeEnd}
                                title={$t('groups.modal.formFields.titles.endTime')}
                                name="timeEnd"
                        />

                        <FormErrorMessage {modalErrors} fieldName="timeEnd"/>
                    </div>
                    <div class="min-w-[150px] flex-1 self-start">
                        <BaseInput
                                name="comment"
                                title={$t('groups.modal.formFields.titles.comment')}
                                bind:value={$GroupEditModalState.comment}
                                inputClasses="p-[5px]"
                        />
                    </div>
                </div>
                <div class="flex gap-4 flex-wrap items-end">
                    {#if formData.action === 'create'}
                        <div class="max-w-[150px]">
                            <div class="block input__title font-medium text-lg">
                                {$t('groups.modal.formFields.titles.dateStart')}
                            </div>
                            <SveltyPicker
                                    name="dateStart"
                                    mode="date"
                                    clearToggle={false}
                                    startDate={addYears(new Date(), -1)}
                                    endDate={dateEnd}
                                    bind:value={dateStart}
                                    inputClasses="w-full input h-9 p-2 "
                            />
                            <FormErrorMessage {modalErrors} fieldName="dateStart"/>
                        </div>
                    {/if}
                    {#if formData.action === 'create'}
                        <div class=" pt-4 w-[120px]">
                            <DailySchedule
                                    Su={formData.daysSchedule.Su}
                                    Mo={formData.daysSchedule.Mo}
                                    Tu={formData.daysSchedule.Tu}
                                    We={formData.daysSchedule.We}
                                    Th={formData.daysSchedule.Th}
                                    {handleDayChange}
                            />
                            <FormErrorMessage {modalErrors} fieldName="daysSchedule"/>
                        </div>
                    {/if}

                    <div class='{formData.action==="create"?"max-w-[70px]":"max-w-[200px]"}  ml-2'>
                        <div>
                            <div class="block input__title font-medium text-center mb-1">
                                {$t('groups.modal.formFields.titles.totalHoursAmount')}
                            </div>
                            <input bind:value={formData.totalHoursAmount}
                                   name="totalHoursAmount"
                                   class="w-full input rounded p-2 h-9 overflow-visibles indent-2 tracking-wide"
                                   type="number">
                        </div>
                        <FormErrorMessage {modalErrors} fieldName="totalHoursAmount"/>
                    </div>
                    {#if formData.action === 'create'}
                        <div class="max-w-[70px]">
                            <div>
                                <div class="block input__title font-medium text-center mb-1">
                                    {$t('groups.modal.formFields.titles.hoursSpendBySession')}

                                </div>
                                <input bind:value={formData.hoursSpendBySession}
                                       name="hoursSpendBySession"
                                       class="w-full input rounded h-9 p-2 overflow-visibles indent-2 tracking-wide"
                                       type="number">
                            </div>
                            <FormErrorMessage {modalErrors} fieldName="hoursSpendBySession"/>
                        </div>
                    {/if}

                    <div class="min-w-[150px] flex-1">
                        <BaseInput
                                name="whatsappUrl"
                                title={$t('groups.modal.formFields.titles.whatsAppLink')}
                                bind:value={formData.whatsappUrl}
                                className=" "
                                inputClasses="p-[5px]"
                        />
                    </div>
                </div>
                <div class="flex justify-between flex-wrap">
                    <div class="flex gap-4">
                        <div class=" min-w-[85px]">
                            <BaseSelect
                                    title={$t('groups.modal.formFields.titles.level')}
                                    name="level"
                                    options={mapEnumToOptions(LevelFilter)}
                                    bind:value={formData.level}
                                    className="h-9 p-0 "
                            />
                        </div>
                        <div class="min-w-[85px]">
                            <BaseSelect
                                    title={$t('groups.modal.formFields.titles.lang')}
                                    name="lang"
                                    options={mapEnumToOptions(LanguageFilter)}
                                    bind:value={formData.lang}
                                    className="h-9 p-0 "
                            />
                        </div>
                    </div>

                    <div class="flex gap-4">
                        <div class="flex-1 {formData.action === 'create' ? 'hidden' : 'flex'}  justify-center">
                            <BaseSwitch
                                    name="&isActive"
                                    title={$t('groups.modal.formFields.titles.active')}
                                    bind:checked={formData.isActive}
                            />
                        </div>
                    </div>
                </div>
                <div class="flex gap-10">
                    <div class="flex-1 flex flex-col">
                        <div class="block input__title font-medium text-lg">
                            {$t('groups.modal.formFields.titles.dateEnd')}
                        </div>
                        <SveltyPicker
                                disabled={true}
                                name="dateEnd"
                                mode="date"
                                clearToggle={false}
                                weekStart={0}
                                startDate={dateStart}
                                bind:value={endDate}
                                inputClasses=" input h-9 p-2"
                        />
                    </div>
                    <div class="flex-1 flex flex-col w-full">
                        <div class="block input__title font-medium text-base mb-1">
                            {$t('groups.modal.formFields.titles.name')}
                        </div>
                        <div class="input-group input-group-divider rounded flex !m-0 !p-0 h-[42px]">
                            {#if !$GroupEditModalState.isPublic}
                                <div class="input-group-shim">{groupName}</div>
                            {/if}
                            <input class="pr-2 " type="text" bind:value={additionalName} placeholder="name"/>
                        </div>
                    </div>
                </div>

                {#if formData.action === 'update'}
                    <GroupSchedulesTable {deleteScheduleChanges} {formData} {openUpdateScheduleChangesModal}/>
                {/if}
            </div>


            <div class="mt-4 flex justify-between">
                <BaseButton disabled={!endDate} type="submit">
                    <IconDeviceFloppy/> {$t('groups.modal.buttons.submit')}</BaseButton
                >
                <BaseButton on:click={() => modalStore.close()}>
                    <IconCircleLetterX/> {$t('groups.modal.buttons.cancel')}</BaseButton
                >
            </div>
        </div>
    {:else}
        <div class="flex flex-col gap-10 w-full">
            <div class="flex items-center gap-4">
                <div class="flex flex-col w-full">
                    <div class="block input__title font-medium text-base mb-1">
                        {$t('groups.modal.formFields.titles.name')}
                    </div>
                    <div class="input-group input-group-divider rounded flex !m-0 !p-0 h-[37px]">
                        {#if !$GroupEditModalState.isPublic}
                            <div class="input-group-shim">{groupName}</div>
                        {/if}
                        <input class="pr-2" type="text" bind:value={additionalName} placeholder="name"/>
                    </div>
                </div>
                <BaseInput
                        name="comment"
                        title={$t('groups.modal.formFields.titles.comment')}
                        bind:value={$GroupEditModalState.comment}
                        inputClasses="p-[5px]"
                />

                <div class="min-w-[85px]">
                    <BaseSelect
                            title={$t('groups.modal.formFields.titles.lang')}
                            name="lang"
                            options={mapEnumToOptions(LanguageFilter)}
                            bind:value={formData.lang}
                            className="h-9 p-0 "
                    />
                </div>
            </div>


            <div class="mt-4 flex justify-between">
                <BaseButton type="submit">
                    <IconDeviceFloppy/> {$t('groups.modal.buttons.submit')}</BaseButton
                >
                <BaseButton on:click={() => modalStore.close()}>
                    <IconCircleLetterX/> {$t('groups.modal.buttons.cancel')}</BaseButton
                >
            </div>
        </div>
    {/if}
</form>

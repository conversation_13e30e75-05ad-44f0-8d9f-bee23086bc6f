<script lang="ts">
    import {IconEyeOff} from "@tabler/icons-svelte";

    export let type: 'home' | 'class' | 'sandbox';
    // export let size = 'xs';
    export let isActive: boolean;
</script>

{#if type}
	{#if type === 'home'}
		<span
				class="flex items-center gap-1 font-bold w-fit inline-block whitespace-nowrap rounded-[0.27rem] bg-primary-100 px-[0.65em] pb-[0.25em] pt-[0.35em] text-center align-baseline text-[0.75em] leading-none text-primary-700"
		>
			{#if !isActive}
				<IconEyeOff size="16"/>
			{/if}
			{type}
		</span>
	{:else if type === 'class'}
		<span
				class="flex items-center gap-1 font-bold w-fit inline-block whitespace-nowrap rounded-[0.27rem] bg-secondary-100 px-[0.65em] pb-[0.25em] pt-[0.35em] text-center align-baseline text-[0.75em] leading-none text-secondary-800"
		>
			{#if !isActive}
				<IconEyeOff size="16"/>
			{/if}
			{type}
		</span>
	{:else if type === 'sandbox'}
		<span
				class="flex items-center gap-1 font-bold w-fit inline-block whitespace-nowrap rounded-[0.27rem] bg-warning-100 px-[0.65em] pb-[0.25em] pt-[0.35em] text-center align-baseline text-[0.75em] leading-none text-warning-800"
		>
			{#if !isActive}
				<IconEyeOff size="16"/>
			{/if}
			{type}
		</span>
	{/if}
{/if}

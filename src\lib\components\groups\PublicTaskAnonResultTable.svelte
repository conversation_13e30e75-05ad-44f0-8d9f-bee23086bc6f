<script lang="ts">
	import {
		getAnyBestResult,
	} from '$lib/common/task-helpers';
	import ResultBadge from '$components/t/ResultBadge.svelte';
	import BaseButton from "$components/common/BaseButton.svelte";
	import {getModalStore} from "@skeletonlabs/skeleton";
	import {ResultPopupDetailsState} from "$lib/state/result-popup-details.state";
	import {IconEye} from "@tabler/icons-svelte";
	import {onMount} from "svelte";
	import {max} from "date-fns";
	import _ from "lodash";

	export let task;

	const modalStore = getModalStore();

	const openDetailsPopup = (r, name, wh) => {
		ResultPopupDetailsState.set({...r, name, whatsapp: wh})
		modalStore.trigger({ type: 'component', component: 'resultDetailsPopup' });
	}

	onMount(() => {
		task.results = _.orderBy(task.results.map(x => {
			return {...x, date: max(x.results.map(y => new Date(y.finishedAt)))}
		}), 'date', 'desc');
	})

</script>

<td class="!bg-transparent"></td>
<td class="!bg-transparent"></td>
<td class="!bg-transparent"></td>
<td colspan="4" class="!p-0 !mx-10 table-compact">
	<table class="table !p-0 !m-0 !table-compact">
		<tbody>
			{#each task.results as r}
				<tr class=" !m-0 !p-0 h-fit">
					<td class="w-1/4">
						{r.name ? r.name : '––––'}
					</td>
					<td class="w-[20%] justify-center">
						{r.whatsapp  ? r.whatsapp : '––––'}
					</td>
					<td class="w-[20%]"><ResultBadge result={r?.currentScore ?? 0} /></td>

					<td class="w-[20%] justify-between text-center mx-10">
						<BaseButton className="w-1/2" size="sm" on:click={() => openDetailsPopup(getAnyBestResult(r.results), r.name, r.whatsapp)}>
							<IconEye size={20} stroke="1.5" />
						</BaseButton>
					</td>
				</tr>
			{/each}
		</tbody>
	</table>
</td>

<style>
	table td {
		vertical-align: middle;
	}
</style>

<script lang="ts">
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {IconCircleLetterX, IconDeviceFloppy} from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import SveltyPicker from 'svelty-picker';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import {onMount} from 'svelte';
    import {areIntervalsOverlapping, isWithinInterval} from 'date-fns';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import BaseInput from '$components/common/BaseInput.svelte';
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import BreadcrumbStudentGroupModal from '$components/students/BreadcrumbStudentGroupModal.svelte';
    import {StudentEditModalState} from '$lib/state/student-edit-state';
    import GroupIntersectionError from '$components/students/GroupIntersectionError.svelte';
    import {getDateString} from '$lib/common/utils';
    import {t} from '$lib/i18n/config';
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";

    let messageToCurrentGroup = '';
    let messageToSelectedGroup = '';
    let selectedGroupId: string;
    let startDateToSelectedGroup: string | null = null;
    let initialDateStartSelectedGroup: string | null = null;
    let initialDateEndSelectedGroup: string | null = null;
    let startTaskToSelectedGroup: string | null = null;
    let groupName = $GroupHistoryState?.selectedGroup?.group?.name;
    let errorMessage = $t('students.students.modal.switchStudentGroupModal.messageerror');
    const modalStore = getModalStore();

    let dateEndCurrentGroup = getDateString($GroupHistoryState.selectedGroup.dateEndActual);

    $: disable = !!(
        (!$GroupHistoryState.selectedGroup.dateEndActual && $GroupHistoryState.selectedGroup.id) ||
        messageToCurrentGroup ||
        messageToSelectedGroup
    );

    const initialDateEndCurrentGroup = getDateString(
        $GroupHistoryState.groups.find(
            (element) => element.id === $GroupHistoryState.selectedGroup.groupId
        )?.dateEnd
    );
    let initialDateStartCurrentGroup = getDateString(
        $GroupHistoryState.selectedGroup.dateStartActual
    );

    const createGroupOptions = () => {
        const groupsIntersecting = $GroupHistoryState.groupsHistory;
        const groupsWithoutGroupsFromHistory = $GroupHistoryState.groups
            .map((element) => {
                const exEl = groupsIntersecting.find((g) => g.groupId === element.id);
                if (exEl) {
                    return null;
                } else {
                    return element.isPublic ? null : element;
                }
            })
            .filter((element) => element !== null);
        console.log($GroupHistoryState.groups)
        return $GroupHistoryState.groups.filter((g) => g.isActive && !g.isPublic).map((group) => {
            return {value: group.id, displayValue: group.name};
        });
    };

    let groupOptions = createGroupOptions();

    const checkCurrentGroupForDateOverlap = () => {
        const dateStartActual = $GroupHistoryState.selectedGroup.dateStartActual;
        const dateEndActual = $GroupHistoryState.selectedGroup.dateEndActual;
        const groupsHistoryWithoutSelectedGroup = $GroupHistoryState.groupsHistory.filter(
            (element) => element.groupId !== $GroupHistoryState.selectedGroup.groupId
        );
        if (
            !dateEndActual ||
            new Date(dateStartActual).toLocaleDateString() ===
            new Date(dateEndActual).toLocaleDateString()
        ) {
            return null;
        }
        const intersectingGroup = groupsHistoryWithoutSelectedGroup.find((studentGroupRecord) => {
            if (!studentGroupRecord.dateEndActual) return null;
            return areIntervalsOverlapping(
                {
                    start: new Date(dateStartActual),
                    end: new Date(dateEndActual)
                },
                {
                    start: new Date(studentGroupRecord.dateStartActual),
                    end: new Date(studentGroupRecord.dateEndActual)
                }
            );
        });
        return intersectingGroup ? intersectingGroup.group.name : null;
    };

    const checkSelectedGroupForDateOverlap = () => {
        if (!startDateToSelectedGroup || !$GroupHistoryState.selectedGroup.dateEndActual) return null;
        const dateStartSelectedGroup = new Date(startDateToSelectedGroup);

        const intersectingOtherGroups = $GroupHistoryState.groupsHistory
            .filter((x) => x.groupId !== $GroupHistoryState.selectedGroup.groupId)
            .find((studentGroupRecord) => {
                return studentGroupRecord?.dateStartActual && studentGroupRecord?.dateEndActual
                    ? isWithinInterval(new Date(dateStartSelectedGroup), {
                        start: new Date(studentGroupRecord.dateStartActual),
                        end: new Date(studentGroupRecord.dateEndActual)
                    })
                    : false;
            });

        const intersectingCurrentGroup = isWithinInterval(new Date(dateStartSelectedGroup), {
            start: new Date($GroupHistoryState.selectedGroup.dateStartActual),
            end: new Date($GroupHistoryState.selectedGroup.dateEndActual)
        });

        return intersectingOtherGroups
            ? intersectingOtherGroups.group.name
            : intersectingCurrentGroup
                ? $GroupHistoryState.selectedGroup.group.name
                : null;
    };

    const setErrorMessage = (groupType: 'selectedGroup' | 'currentGroup', groupName: string) => {
        if (groupType === 'currentGroup') {
            messageToCurrentGroup = `${$t('students.messageerror')} ${groupName}`;
        } else {
            messageToSelectedGroup = `${$t('students.messageerror')} ${groupName}`;
        }
    };

    const clearErrorMessage = (groupType: 'selectedGroup' | 'currentGroup') => {
        if (groupType === 'currentGroup') {
            messageToCurrentGroup = ``;
        } else {
            messageToSelectedGroup = ``;
        }
    };

    const onChangeDate = () => {
        const resultCurrentGroup = checkCurrentGroupForDateOverlap();
        resultCurrentGroup
            ? setErrorMessage('currentGroup', resultCurrentGroup)
            : clearErrorMessage('currentGroup');

        let resultSelectedGroup = checkSelectedGroupForDateOverlap();

        resultSelectedGroup
            ? setErrorMessage('selectedGroup', resultSelectedGroup)
            : clearErrorMessage('selectedGroup');
    };

    const onSelectChange = () => {
        const group: GroupDto = $GroupHistoryState.groups.find((group) => group.id === selectedGroupId);

        startDateToSelectedGroup = getDateString(group?.dateStart?.toString());
        initialDateStartSelectedGroup = getDateString(group?.dateStart?.toString());
        initialDateEndSelectedGroup = getDateString(group?.dateEnd?.toString());
        startTaskToSelectedGroup = getDateString(group?.dateStart?.toString());
        onChangeDate();
    };

    onMount(() => {
        onSelectChange();
        const {dateEndActual} = $GroupHistoryState.selectedGroup;
        $GroupHistoryState.selectedGroup.dateEndActual = getDateString(dateEndActual);
    });

    async function handleSubmit(event) {
        const data = new FormData(this);
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());

        if (result.type === 'success') {
            await invalidate('load:students');
            modalStore.close();
            NotificationStore.push({
                type: NotificationType.success,
                message: t.get('students.students.notifications.switchStudentGroup.success')
            }, 5);
        }
    }
</script>

<div class="modal card p-10  sm:w-2/3 lg:w-1/2 shadow-xl space-y-4">
    <BreadcrumbStudentGroupModal
            currentModalName={$t('students.students.modal.switchStudentGroupModal.breadcrumb')}
            groupName=""
    />
    <form
            method="POST"
            on:submit|preventDefault={handleSubmit}
            action="?/switchStudentGroup"
            class="flex flex-col gap-10"
    >
        <div class="hidden">
            <BaseInput name="studentId" bind:value={$StudentEditModalState.id}/>
        </div>
        <div class="hidden">
            <BaseInput name="idCurrentGroup" bind:value={$GroupHistoryState.selectedGroup.id}/>
        </div>
        <div class="flex flex-col gap-2">
            <div class="w-full flex justify-between items-center">
                <div class="max-w-[140px]">
					<span class="block input__title font-medium text-base mb-1">
						{$t('students.students.modal.switchStudentGroupModal.fields.dateend')}
					</span>
                    <SveltyPicker
                            name="dateEndCurrentGroup"
                            mode="date"
                            startDate={initialDateStartCurrentGroup}
                            bind:value={$GroupHistoryState.selectedGroup.dateEndActual}
                            required={true}
                            inputClasses="w-full input rounded h-10 p-2"
                            todayBtn={true}
                            disabled={!groupName}
                            on:change={() => {
							onChangeDate('currentGroup');
						}}
                    />
                </div>

                <div class="block mt-8 input__title font-bold text-base mb-1">
                    {groupName ? groupName : '-'} :
                    {$t('students.students.modal.switchStudentGroupModal.fromgroup')}
                </div>
            </div>
            <div class="w-full">
                {#if messageToCurrentGroup}
                    <GroupIntersectionError intersectionError={messageToCurrentGroup}/>
                {/if}
            </div>
        </div>

        <div>
            <BaseSelect
                    title={$t('students.students.modal.switchStudentGroupModal.fields.tonewgroup')}
                    name="idSelectedGroup"
                    options={groupOptions}
                    bind:value={selectedGroupId}
                    on:change={onSelectChange}
            />
        </div>
        <div class="flex flex-col gap-2">
            <div class="flex gap-10">
                <div class="max-w-[140px]">
					<span class="block input__title font-medium text-base mb-1">
						{$t('students.students.modal.switchStudentGroupModal.fields.datestart')}
					</span>
                    <SveltyPicker
                            name="dateStartSelectedGroup"
                            startDate={initialDateStartSelectedGroup}
                            endDate={initialDateEndSelectedGroup}
                            bind:value={startDateToSelectedGroup}
                            on:change={() => {
							onChangeDate('selectedGroup');
						}}
                            required={true}
                            mode="date"
                            inputClasses="w-full input rounded h-10 p-2"
                            todayBtn={false}
                    />
                </div>
                <div class="max-w-[140px]">
					<span class="block input__title font-medium text-base mb-1">
						{$t('students.students.modal.switchStudentGroupModal.fields.taskstart')}
					</span>
                    <SveltyPicker
                            name="taskStartSelectedGroup"
                            bind:value={startTaskToSelectedGroup}
                            mode="date"
                            inputClasses="w-full input rounded h-10 p-2"
                            todayBtn={false}
                    />
                </div>
            </div>
            {#if messageToSelectedGroup}
                <GroupIntersectionError intersectionError={messageToSelectedGroup}/>
            {/if}
        </div>
        <div class=" flex justify-between">
            <BaseButton disabled={disable} type="submit">
                <IconDeviceFloppy/>
                {$t('students.buttons.save')}
            </BaseButton>
            <BaseButton on:click={() => modalStore.close()}>
                <IconCircleLetterX/>
                {$t('students.buttons.cancel')}
            </BaseButton>
        </div>
    </form>
</div>

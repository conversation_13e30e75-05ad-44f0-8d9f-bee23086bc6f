import { wrapFunc } from '$api/core/misc/response-wrapper';
import { getStudentByTZ, getStudents } from '$api/core/services/students.service';
import { paramsToKeyValue } from '$api/core/utils';
import type { StudentFilterDto } from '$common/models/filters/student-filter.dto';
import { mapper } from '$common/core/mapper';
import type { BaseSortingDto } from '$common/models/filters/base-filter.dto';
import type {RequestEvent} from "@sveltejs/kit";

export const GET = async ({ url }:RequestEvent) =>
	wrapFunc(async () => {
		const { take, skip, groupId, tz, sortBy, sortDir, onlyIsActive,search } = paramsToKeyValue(
			url.searchParams
		);
		if (tz) {
			return await getStudentByTZ(tz);
		}

		const sort = sortBy
			? mapper<BaseSortingDto, unknown>({
					sortBy: sortBy,
					sortDir: sortDir
			  })
			: undefined;

		return await getStudents(
			mapper<StudentFilterDto, unknown>({
				take: +take,
				skip: +skip,
				groupId,
				onlyIsActive: onlyIsActive === 'true',
				search
			}),
			sort
		);
	});

<script lang="ts">
    import Sentence from '$components/sentences/Sentence.svelte';
    import {t} from '$lib/i18n/config';
    import {SentenceFilterState} from '$lib/state/sentence-filter-state';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconPlus} from '@tabler/icons-svelte';
    import {
        initialSentencePaging,
        pageSize,
        SentencePagingState
    } from '$lib/state/sentence-paging-state';
    import SentenceFilter from '$components/sentences/SentenceFilter.svelte';
    import CountSpan from '$components/common/CountSpan.svelte';
    import {generateGuid} from '$common/core/utils';
    import InfiniteScrollContainer from '$components/common/InfiniteScrollContainer.svelte';
    import {onDestroy, onMount} from 'svelte';
    import {invalidate} from '$app/navigation';
    import {loadingWrap} from '$lib/common/utils';
    import {page} from "$app/stores";
    import {loadFfmpeg} from "$lib/state/ffmpeg-state";
    import {initialSentenceFilter} from "$common/models/filters/sentence-filter.dto";
    import _ from "lodash";

    export let data;


    onMount(async () => {
        try {
            await loadFfmpeg();
        } catch (error) {
            console.error('Failed to load ffmpeg on sentences page:', error);
        }
    });

    $: sentences = data?.sentences?.data || [];
    $:users = data?.users?.data || [];
    $:count = data?.sentences?.count;


    const loadMore = async () => {
        if (count > sentences.length) {
            SentencePagingState.set({take: pageSize + sentences.length, skip: 0});
            await loadingWrap(async () => {
                await invalidate('load:sentences');
            });
        }
    };

    onDestroy(() => {
        unsubscribeFilter();
        $SentenceFilterState = _.cloneDeep(initialSentenceFilter)
    });

    const unsubscribeFilter = SentenceFilterState.subscribe(async () => {
        $SentencePagingState = _.cloneDeep(initialSentencePaging)
        await loadingWrap(async () => {
            await invalidate('load:sentences');
        });
    });

    const createNewSentence = () => {
        sentences = [
            {
                id: generateGuid(),
                value: '',
                translations: [
                    {
                        id: generateGuid(),
                        lang: 'EN',
                        value: ''
                    },
                    {
                        id: generateGuid(),
                        lang: 'RU',
                        value: ''
                    }
                ],
                sex: 'm',
                level: 0,
                isNew: true,
                isFavorite: false,
                audioRecordedButNotYetSaved: false,
                displayAsText: true,
                createdAt: new Date(),
                updatedAt: new Date(),
                createdByUser: {
                    firstname: $page?.data?.user?.firstname,
                    lastname: $page?.data?.user?.lastname
                },
                updatedByUser: {
                    firstname: $page?.data?.user?.firstname,
                    lastname: $page?.data?.user?.lastname
                }

            },
            ...sentences
        ];
    };
</script>

<InfiniteScrollContainer loadMoreFunc={loadMore}>
    <div class="flex flex-col m-6">
        <div class="mt-3">
            <h1 class="title mb-1 font-medium text-xl">
                {$t('sentences.title')}
                <CountSpan {count}/>
            </h1>
        </div>

        <div
                class=" card mt-6 p-5 w-full text-token flex  items-end variant-glass-primary"
        >
            <div class=" flex-1">
                <SentenceFilter {users}/>
            </div>
            <div class="w-1/10 ">
                <div dir="ltr" class="w-full ">
                    <div>
                        <BaseButton on:click={createNewSentence}>
                            {$t('sentences.new')}
                            <IconPlus/>
                        </BaseButton>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-10">
            {#each sentences as sentence, index (sentence.id)}
                <div class="mt-4">
                    <Sentence {sentence} id={sentence.id} selectedLang={$SentenceFilterState.lang}/>
                </div>
            {/each}
        </div>
    </div>
</InfiniteScrollContainer>

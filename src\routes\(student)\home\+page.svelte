<script lang="ts">
    import {t} from '$lib/i18n/config';
    import TaskItem from '$components/students-home/TaskItem.svelte';
    import NotificationItem from '$components/students-home/NotificationItem.svelte';
    import {CampaignApiClient} from '$lib/core/api-clients/campaign-api.client';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import BaseInput from '$components/common/BaseInput.svelte';
    import {invalidate} from '$app/navigation';
    import {isItStudyDay, loadingWrap} from "$lib/common/utils";
    import {TaskFilterState} from "$lib/state/task-filter-state";
    import {onMount} from "svelte";
    import {page} from "$app/stores";
    import Fuse from 'fuse.js'
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconX} from "@tabler/icons-svelte";
    import {locale} from '$lib/i18n/config';
    import {LocaleState} from "$lib/state/locale.state";
    import {addHours} from 'date-fns';
    import {SurveyStatus} from "$common/models/enums";
    import axios from "axios";
    import Timer from "$components/common/Timer.svelte";


    export let data;


    let searchValue = '';
    let initialized = false;


    $:groupOptions = data?.studentGroups?.length > 0 && data?.studentGroups?.reduce((acc, obj) => {
        const isUnique = !acc.some(item => item?.group?.name === obj.group?.name);
        if (isUnique) {
            acc.push(obj);
        }
        return acc;
    }, [])?.map((x) => {
        return {displayValue: x?.group?.name, value: x?.group?.id};
    });


    $:disableSelect = groupOptions?.length <= 1;
    $: initialTasks = data?.tasks?.data && data?.tasks?.data.filter((t) => t?.groupId === $TaskFilterState.groupId || t?.groupId === $page.data?.user?.currentGroup || t?.groupId === groupOptions?.[0]?.value) || []
    $: tsks = initialTasks;
    $: currentGroup = data?.studentGroups?.find((sg) => sg?.groupId === $TaskFilterState.groupId)?.group;
    $:dailySurveyUrl = currentGroup?.dailySurveyUrl;
    $:rareSurveyUrl = currentGroup?.rareSurveyUrl;
    let isShowSurvey = false;




    const markAsReadNotification = async (event) => {
        await loadingWrap(() => new CampaignApiClient().markNotificationAsReadById(event.detail.id).finally(async () => await invalidate('load:home')))
    };

    const search = () => {
        if (searchValue) {
            const fuseOptions = {
                shouldSort: false,
                keys: [
                    "commentPublic"
                ]
            };
            const fuse = new Fuse(tsks, fuseOptions);
            const searchResult = fuse.search(searchValue);
            tsks = searchResult.map((sr) => ({...sr?.item}))
        } else {
            tsks = initialTasks;
        }
    };

    const clearSearch = () => {
        searchValue = '';
        tsks = initialTasks;
    }


    onMount(() => {
        TaskFilterState.subscribe(async () => {
            if (initialized) {
                await loadingWrap(async () => {
                    await invalidate('load:home')
                })
            }
        });
        TaskFilterState.update(x => ({...x, groupId: $TaskFilterState?.groupId || $page?.data?.user?.currentGroup || groupOptions?.[0]?.value}))
        initialized = true;
        const studentGroups = data.studentGroups?.find((gr) => data?.user?.currentGroup === gr?.groupId);
        const group = studentGroups?.group;
        if (group) {
            LocaleState.set(group?.lang?.toLowerCase() ?? 'en')
            $locale = group?.lang?.toLowerCase() ?? 'en';
            console.log('group?.lang?.toLowerCase()', group?.lang?.toLowerCase());
        }
        window.addEventListener('message', async (e) => {
            if (typeof (e?.data) === 'string' && e?.data?.includes('Tally.FormSubmitted')) {
                const response = await axios.post('/api/survey', {
                    studentId: $page?.data?.user?.id
                });
                if (response?.data?.success && response.status === 200) {
                    setTimeout(() => {
                        isShowSurvey = false;
                    }, 3000);
                    await invalidate('load:home')
                }

            }
        });
        // clearOldLocalStorage();
    });
    // {data?.isSurveyEligible?.lastStudyDay ? format(new Date(data?.isSurveyEligible?.lastStudyDay), 'dd.MM.yyyy kk:mm') : ''}

    $:endOfSurvey = data?.isSurveyEligible?.lastStudyDay && addHours(new Date(data?.isSurveyEligible?.lastStudyDay), 18);

</script>


<div class="flex justify-center" dir="ltr">
    <div class="p-4 max-w-[760px] flex flex-col gap-5 w-full mb-5">
        {#if data?.notifications?.length > 0}
            <h1 dir="auto" class="font-bold text-xl mt-3 ">{$t('notifications.title')}</h1>
            {#each data?.notifications as notification}
                <NotificationItem {notification} on:markAsRead={markAsReadNotification}/>
            {/each}
        {/if}
        {#if data?.isSurveyEligible?.surveyStatus && data?.isSurveyEligible?.surveyStatus !== SurveyStatus.none && (dailySurveyUrl || rareSurveyUrl)}
            <div dir="auto" class="py-4 px-4 rounded-xl flex flex-col gap-4 card variant-glass-primary">
                <div class="flex flex-col gap-10">
                    <h1 dir="auto"
                        class="ml-6 text-4xl font-bold">{$t('home.surveyTitle')}</h1>
                    <div class="flex flex-col gap-4">
                        <h1 dir="auto"
                            class="ml-6 text-2xl font-bold">
                            {$t('home.subTitle')}
                            <Timer dateEnd={endOfSurvey}/>
                        </h1>
                        <h1 dir="auto"
                            class="ml-6 text-xl font-bold">
                            {$t('home.regards')}
                            {#if $page.data.envs.VITE_HEADER_TITLE === 'Artzenu' }
                                {$t('home.directorArzenu')}
                            {:else}
                                {$t('home.director')}
                            {/if}
                        </h1>
                    </div>

                    <!--                    <BaseButton on:click={()=>{isShowSurvey=!isShowSurvey}}>-->
                    <!--                        {#if isShowSurvey}-->
                    <!--                            {$t('home.hideSurvey')}-->
                    <!--                        {:else}-->
                    <!--                            {$t('home.surveyButtonShow')}-->
                    <!--                        {/if}-->
                    <!--                    </BaseButton>-->
                </div>
                {#if data?.isSurveyEligible?.surveyStatus !== SurveyStatus.none && (dailySurveyUrl || rareSurveyUrl)}
                    <div class="h-full min-h-[600px]">
                        <iframe data-tally-src={`${data?.isSurveyEligible?.surveyStatus === SurveyStatus.rare? rareSurveyUrl:dailySurveyUrl}?student_name=${$page?.data?.user?.firstname+' '+ $page?.data?.user?.lastname}&group_name=${currentGroup?.name}&tz=${$page?.data?.user?.tz}&date=${data?.isSurveyEligible?.lastStudyDay}&whatsapp=${$page?.data?.user?.whatsApp}&transparentBackground=1&dynamicHeight=1`}
                                loading="lazy" width="100%" height="100%" frameborder="0" marginheight="0"
                                marginwidth="0"
                                title="Что и как мы учили сегодня?"></iframe>
                        <script>
                            var d = document, w = "https://tally.so/widgets/embed.js", v = function () {
                                "undefined" != typeof Tally ? Tally.loadEmbeds() : d.querySelectorAll("iframe[data-tally-src]:not([src])").forEach((function (e) {
                                    e.src = e.dataset.tallySrc
                                }))
                            };
                            if ("undefined" != typeof Tally) v(); else if (d.querySelector('script[src="' + w + '"]') == null) {
                                var s = d.createElement("script");
                                s.src = w, s.onload = v, s.onerror = v, d.body.appendChild(s);
                            }
                        </script>
                    </div>
                {/if}

            </div>
        {/if}
        {#if isShowSurvey}
        {/if}

        <h1 class="font-bold text-xl mt-5">{$t(`studentTasks.title`)}</h1>
        <div class="flex xs:flex-col xs:gap-4 md:flex-row md:md:justify-between ">
            <div class="flex w-full gap-2">
                <div class="md:w-[45%] xs:w-full flex gap-1">
                    <BaseInput bind:value={searchValue} on:input={search}
                               title={$t('studentTasks.taskFilter.search')} dir="ltr"/>
                </div>
                {#if searchValue}
                    <BaseButton on:click={clearSearch} className="self-center mt-6 p-2">
                        <IconX size="20"/>
                    </BaseButton>
                {/if}
            </div>
            <div dir="ltr" class=" xs:w-full md:w-[40%]">
                <BaseSelect
                        disabled={disableSelect}
                        bind:value={$TaskFilterState.groupId}
                        on:change={async ()=>{
                            await invalidate('load:home')
                        }}
                        options={groupOptions}
                        title={$t('studentTasks.taskFilter.group')}/>
            </div>
        </div>

        {#if tsks?.length > 0 }
            {#each tsks as task}
                <a href="/t/{task?.id}">
                    <TaskItem {task}/>
                </a>
            {/each}
        {:else}
            <div class="p-4 card mt-5">
                <p class="text-lg italic font-semibold">{$t('home.noTasks')}</p>
            </div>
        {/if}
    </div>
</div>

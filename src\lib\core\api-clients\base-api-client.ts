import axios from 'axios';
import NotificationStore from '$lib/state/notification-state';
import {NotificationType} from '$common/models/enums';
import {t} from '$lib/i18n/config';

export class BaseApiClient {
    protected getDataOrThrow = async (url: string) => this.handleResponse(() => axios.get(url));

    protected postDataOrThrow = async <T>(url: string, data: T, returnSuccess = false) =>
        this.handleResponse(() => axios.post(url, data), returnSuccess);

    protected putDataOrThrow = async <T>(url: string, data: T, returnSuccess = false) =>
        this.handleResponse(() => axios.put(url, data), returnSuccess);

    protected deleteOrThrow = async (url: string, returnSuccess = false) =>
        this.handleResponse(() => axios.delete(url), returnSuccess);

    private handleResponse = async <T extends { data: any }>(
        apiFunction: () => Promise<T>,
        returnSuccess = false
    ) => {
        try {
            const {data, success, error} = (await apiFunction()).data;

            if (!success) {
                NotificationStore.push({
                    type: NotificationType.error,
                    message: t.get('common.apiErrorMessage')
                }, 5);

                return returnSuccess
                    ? false
                    : {
                        success: false,
                        error: error
                    };
            }

            return returnSuccess ? success : data;

        } catch (error) {
            NotificationStore.push(
                {
                    type: NotificationType.error,
                    message: t.get('common.apiErrorMessage')
                }, 5);

            return returnSuccess
                ? false
                : {
                    success: false,
                    error: error
                };
        }
    };
}

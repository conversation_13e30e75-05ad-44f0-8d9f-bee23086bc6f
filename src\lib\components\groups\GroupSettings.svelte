<script lang="ts">
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher} from 'svelte';
    import {t} from '$lib/i18n/config';
    import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";
    import {
        DbFormatStringToDaysSchedule,
        DbFormatStringToDaysScheduleObject,
    } from "$lib/common/utils";
    import {page} from "$app/stores";
    import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
    import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";
    import GroupHolidaysTable from "$components/groups/GroupHolidaysTable.svelte";
    import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
    import {format} from "date-fns";
    import type {GroupDto} from "$common/models/dtos/group.dto";
    import {getModalStore} from "@skeletonlabs/skeleton";


    const modalStore = getModalStore()


    const dispatchModal = createEventDispatcher();

    export let groupHolidays: GroupHolidayDto[] = [];

    export let generalHolidays: GeneralHolidayDto[] = [];

    export let groupHolidaysExc: GroupHolidayExcDto[] = [];

    export let groupScheduleChanges: GroupScheduleDto[] = [];

    export let group: GroupDto;


    let groupId = $page?.params?.id;


</script>

<div class="overflow-auto">
    <div>
        <BaseButton
                on:click={() => {
			dispatchModal('triggerStopGroupModal');
		}}>{$t('groups.groupSettings.stop')}</BaseButton
        >
    </div>
    <GroupHolidaysTable {groupId} {groupHolidays} {generalHolidays} {groupHolidaysExc}/>
</div>



generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextIndex", "fullTextSearch"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model student_results {
  id                   String    @id @db.Char(36)
  taskId               String    @db.Char(36)
  studentId            String?   @db.Char(36)
  results              Json
  currentScore         Decimal   @db.Decimal(9, 2)
  taskReleaseDate      DateTime  @default(now())
  currentScoreAbsolute Decimal   @default(0.00) @db.Decimal(9, 2)
  name                 String?   @db.VarChar(255)
  whatsapp             String?   @db.VarChar(24)
  resultsDeflate       String?   @db.Text
  task                 task      @relation(fields: [taskId], references: [id])
  student              students? @relation(fields: [studentId], references: [id])

  @@index([studentId])
  @@index([taskId])
}

model message_campaigns {
  id            String                  @id @db.Char(36)
  type          campaign_type
  recipientId   String                  @db.Char(36)
  title         String                  @db.Var<PERSON>har(255)
  message       String                  @db.Text
  createdAt     DateTime?               @default(now()) @db.Timestamp(0)
  createdBy     String                  @db.Char(36)
  notifications message_notifications[]
}

model message_notifications {
  id         String            @id @db.Char(36)
  campaignId String            @db.Char(36)
  userId     String            @db.Char(36)
  isRead     Boolean?          @default(false)
  readAt     DateTime?         @db.Timestamp(0)
  campaign   message_campaigns @relation(fields: [campaignId], references: [id], onDelete: Cascade)

  @@index([campaignId])
}

model groups {
  id                      String                    @id @db.Char(36)
  level                   Int
  lang                    String                    @db.VarChar(255)
  daysSchedule            String                    @db.VarChar(255)
  hoursSchedule           Int
  timeStart               DateTime                  @db.Time(0)
  timeEnd                 DateTime                  @db.Time(0)
  whatsappUrl             String?                   @db.VarChar(255)
  comment                 String?                   @db.Text
  isActive                Boolean?                  @default(true)
  isPublic                Boolean?                  @default(true)
  createdAt               DateTime?                 @default(now()) @db.Timestamp(0)
  createdBy               String?                   @db.Char(36)
  updatedAt               DateTime?                 @default(now()) @db.Timestamp(0)
  updatedBy               String?                   @db.Char(36)
  name                    String?                   @db.VarChar(255)
  dateEnd                 DateTime                  @default(now()) @db.Timestamp(0)
  dateStart               DateTime                  @default(now()) @db.Timestamp(0)
  hoursSpendBySession     Int                       @default(1)
  totalHoursAmount        Int                       @default(10)
  dailySurveyUrl          String?                   @db.Char(36)
  rareSurveyDate          DateTime?                 @db.Timestamp(0)
  rareSurveyUrl           String?                   @db.Char(36)
  studentsGroups          students_groups[]
  groupScheduleChanges    groupScheduleChanges[]
  groupHoliday            groupHolidays[]
  groupHolidaysExceptions groupHolidaysExceptions[]

  @@fulltext([name])
}

model generalHolidays {
  id                      String                    @id @db.Char(36)
  date                    DateTime                  @db.Timestamp(0)
  comment                 String                    @db.VarChar(255)
  groupHolidaysExceptions groupHolidaysExceptions[]
}

model groupScheduleChanges {
  id              String    @id @db.Char(36)
  groupId         String
  daysSchedule    String    @db.VarChar(255)
  hoursPerSession Int
  createdAt       DateTime? @default(now()) @db.Timestamp(0)
  createdBy       String?   @db.Char(36)
  updatedAt       DateTime? @default(now()) @db.Timestamp(0)
  updatedBy       String?   @db.Char(36)
  dateStart       DateTime? @default(now()) @db.Timestamp(0)
  group           groups    @relation(fields: [groupId], references: [id])

  @@index([groupId])
}

model groupHolidays {
  id      String   @id @db.Char(36)
  groupId String
  date    DateTime @db.Timestamp(0)
  comment String   @db.VarChar(255)
  group   groups   @relation(fields: [groupId], references: [id])

  @@index([groupId])
}

model groupHolidaysExceptions {
  id               String          @id @db.Char(36)
  groupId          String
  generalHolidayId String
  group            groups          @relation(fields: [groupId], references: [id])
  generalHoliday   generalHolidays @relation(fields: [generalHolidayId], references: [id])

  @@index([groupId])
  @@index([generalHolidayId])
}

model aboutContent {
  id    String  @id @db.Char(36)
  lang  locales
  value Json
}

model langs {
  id   String @id @db.Char(36)
  name String @db.VarChar(255)
}

model sentences {
  id              String           @id @db.Char(36)
  level           Int
  value           String           @db.Text
  audioUrl        String?          @db.VarChar(255)
  sex             sentences_sex
  complaintsCount Int?             @default(0)
  createdAt       DateTime?        @default(now()) @db.Timestamp(0)
  createdBy       String?          @db.Char(36)
  updatedAt       DateTime?        @default(now()) @db.Timestamp(0)
  updatedBy       String?          @db.Char(36)
  isFavorite      Boolean          @default(false)
  createdByUser   users?           @relation("createdby", fields: [createdBy], references: [id])
  updatedByUser   users?           @relation("updatedby", fields: [updatedBy], references: [id])
  translations    translations[]
  task_sentences  task_sentences[]

  @@index([createdBy])
  @@index([updatedBy])
  @@fulltext([value])
}

model student_requests {
  id             String    @id @db.Char(36)
  tz             String    @db.VarChar(12)
  email          String?   @db.VarChar(255)
  firstname      String?   @db.VarChar(255)
  lastname       String?   @db.VarChar(255)
  dob            DateTime  @db.Date
  phone          String?   @db.VarChar(255)
  whatsapp       String?   @db.VarChar(255)
  document1url   String?   @db.VarChar(255)
  createdAt      DateTime? @default(now()) @db.Timestamp(0)
  groupLang      String?   @db.VarChar(255)
  groupLevel     String?   @db.VarChar(255)
  groupStartDate DateTime? @db.Date
  learnStartDate DateTime? @db.Date
  isHandled      Boolean   @default(false)
  city           String?   @db.VarChar(36)
  photoUrl       String?   @db.Text
  rawRequest     Json?
  teudatOleUrl   String?   @db.Text
}

model students {
  id                    String            @id @db.Char(36)
  tz                    String            @unique(map: "tz") @db.VarChar(255)
  email                 String            @db.VarChar(255)
  firstname             String            @db.VarChar(255)
  lastname              String            @db.VarChar(255)
  middlename            String?           @db.VarChar(255)
  dob                   DateTime          @db.Date
  phone                 String            @db.VarChar(255)
  whatsapp              String?           @db.VarChar(255)
  currentGroup          String?           @db.Char(36)
  comment               String?           @db.Text
  teacherComment        String?           @db.Text
  document1url          String?           @db.Text
  document2url          String?           @db.Text
  createdAt             DateTime?         @default(now()) @db.Timestamp(0)
  createdBy             String?           @db.Char(36)
  averageTaskDelay      BigInt            @default(0) @db.UnsignedBigInt
  averageTaskScore      Decimal           @default(0.00) @db.Decimal(9, 2)
  currentGroupStartDate DateTime?         @default(now())
  lastTaskDelay         BigInt            @default(0) @db.UnsignedBigInt
  lastTaskScore         Decimal           @default(0.00) @db.Decimal(9, 2)
  registrationDate      DateTime?         @default(now())
  groupsStudents        students_groups[]
  results               student_results[]
  student_survey        student_survey[]
}

model student_survey {
  id        String    @id @default(uuid()) @db.Char(36)
  createdAt DateTime? @default(now()) @db.Timestamp(0)
  studentId String    @db.Char(36)
  student   students  @relation(fields: [studentId], references: [id])

  @@index([studentId])
}

model students_groups {
  id              String    @id @db.Char(36)
  studentId       String    @db.Char(36)
  groupId         String    @db.Char(36)
  dateStartActual DateTime? @db.Date
  dateEndActual   DateTime? @db.Date
  dateStartTasks  DateTime? @db.Date
  dateEndTasks    DateTime? @db.Date
  createdAt       DateTime? @default(now()) @db.Timestamp(0)
  createdBy       String?   @db.Char(36)
  updatedAt       DateTime? @default(now()) @db.Timestamp(0)
  updatedBy       String?   @db.Char(36)
  student         students  @relation(fields: [studentId], references: [id])
  group           groups    @relation(fields: [groupId], references: [id])

  @@index([studentId])
  @@index([groupId])
}

model task {
  id                                String                   @id @db.Char(36)
  groupId                           String                   @db.Char(36)
  type                              task_type
  date                              DateTime                 @db.Date
  time                              DateTime                 @db.Time(0)
  commentPublic                     String?                  @db.Text
  commentPrivate                    String?                  @db.Text
  isActive                          Boolean?                 @default(true)
  hintsEnabled                      Boolean?                 @default(true)
  isDeleted                         Boolean?                 @default(false)
  duplicatedFromTaskId              String?                  @db.Char(36)
  createdAt                         DateTime?                @default(now()) @db.Timestamp(0)
  createdBy                         String?                  @db.Char(36)
  updatedAt                         DateTime?                @default(now()) @db.Timestamp(0)
  updatedBy                         String?                  @db.Char(36)
  lang                              String                   @default("RU") @db.VarChar(255)
  content                           Json?
  dateTime                          DateTime                 @default(now()) @db.Timestamp(0)
  hebrewFont                        Boolean                  @default(false)
  allowAnonymous                    Boolean                  @default(true)
  navigationInPublicTaskEnabled     Boolean?                 @default(false)
  navigationInPublicTaskOnlyAfter60 Boolean?                 @default(false)
  startingMode                      task_mode                @default(translation)
  task_sentences                    task_sentences[]
  additionalTasks                   task_additional[]
  task_incentive_content            task_incentive_content[]
  results                           student_results[]
  createdByUser                     users?                   @relation("createdby", fields: [createdBy], references: [id])
  updatedByUser                     users?                   @relation("updatedby", fields: [updatedBy], references: [id])
  pageContent                       page_content?

  @@index([createdBy])
  @@index([updatedBy])
}

model page_content {
  id                 String            @id @default(uuid()) @db.Char(36)
  taskId             String?           @unique
  incentiveContentId String?           @unique
  content_items      content_item[]
  task               task?             @relation(fields: [taskId], references: [id])
  incentive_content  incentiveContent? @relation(fields: [incentiveContentId], references: [id])
}

model content_item {
  id             String        @id @default(uuid()) @db.Char(36)
  type           content_type
  content        Json
  position       Int
  page_contentId String?       @db.Char(36)
  page_content   page_content? @relation(fields: [page_contentId], references: [id])

  @@index([page_contentId])
}

model incentiveContent {
  id                     String                   @id @db.Char(36)
  name                   String                   @db.VarChar(255)
  comment                String?                  @db.Text
  createdAt              DateTime?                @default(now()) @db.Timestamp(0)
  createdBy              String?                  @db.Char(36)
  updatedAt              DateTime?                @default(now()) @db.Timestamp(0)
  updatedBy              String?                  @db.Char(36)
  value                  Json
  task_incentive_content task_incentive_content[]
  page_content           page_content?
}

model task_additional {
  id             String     @id @db.Char(36)
  time           Int
  delay          Int
  maxScore       Int
  hintEnabled    Boolean?   @default(true)
  uniqueHash     String     @db.VarChar(255)
  createdAt      DateTime?  @default(now()) @db.Timestamp(0)
  createdBy      String?    @db.Char(36)
  updatedAt      DateTime?  @default(now()) @db.Timestamp(0)
  updatedBy      String?    @db.Char(36)
  taskId         String     @db.Char(36)
  enabled        Boolean    @default(false)
  mode           task_mode
  allowAnonymous Boolean    @default(true)
  allowRetry     Boolean    @default(true)
  speedMode      speed_mode @default(slow)
  task           task       @relation(fields: [taskId], references: [id])

  @@index([taskId])
}

model task_incentive_content {
  taskId             String           @db.Char(36)
  incentiveContentId String           @db.Char(36)
  isActive           Boolean?         @default(true)
  scoreThreshold     Int
  createdAt          DateTime?        @default(now()) @db.Timestamp(0)
  createdBy          String?          @db.Char(36)
  task               task             @relation(fields: [taskId], references: [id])
  incentive_content  incentiveContent @relation(fields: [incentiveContentId], references: [id])

  @@id([taskId, incentiveContentId])
  @@index([taskId])
  @@index([incentiveContentId])
}

model task_sentences {
  taskId          String                         @db.Char(36)
  sentenceId      String                         @db.Char(36)
  defaultTaskView task_sentences_defaultTaskView
  indexN          Int
  createdAt       DateTime?                      @default(now()) @db.Timestamp(0)
  createdBy       String?                        @db.Char(36)
  task            task                           @relation(fields: [taskId], references: [id], onDelete: Cascade)
  sentence        sentences                      @relation(fields: [sentenceId], references: [id], onDelete: Cascade)
  complaints      complaints[]

  @@id([taskId, sentenceId])
  @@index([taskId])
  @@index([sentenceId])
}

model translations {
  id         String    @id @db.Char(36)
  sentenceId String    @db.Char(36)
  value      String    @db.Text
  lang       String    @db.VarChar(255)
  createdAt  DateTime? @default(now()) @db.Timestamp(0)
  createdBy  String?   @db.Char(36)
  updatedAt  DateTime? @default(now()) @db.Timestamp(0)
  updatedBy  String?   @db.Char(36)
  sentence   sentences @relation(fields: [sentenceId], references: [id])

  @@index([sentenceId])
}

model complaints {
  id             String         @id @db.Char(36)
  comment        String         @db.VarChar(255)
  sentenceId     String         @db.Char(36)
  taskId         String         @db.Char(36)
  isHandled      Boolean        @default(false)
  createdAt      DateTime?      @default(now()) @db.Timestamp(0)
  createdBy      String         @db.Char(36)
  task_sentences task_sentences @relation(fields: [taskId, sentenceId], references: [taskId, sentenceId], onDelete: Cascade)

  @@index([taskId, sentenceId])
}

model exclusions {
  id         String    @id @db.Char(36)
  exclusions String    @db.VarChar(255)
  createdAt  DateTime? @default(now()) @db.Timestamp(0)
  createdBy  String?   @db.Char(36)
  updatedAt  DateTime? @default(now()) @db.Timestamp(0)
  updatedBy  String?   @db.Char(36)
}

model users {
  id               String             @id @db.Char(36)
  tz               String             @db.VarChar(255)
  phone            String             @db.VarChar(255)
  email            String             @unique(map: "email") @db.VarChar(255)
  password         String             @db.VarChar(255)
  firstname        String             @db.VarChar(255)
  lastname         String             @db.VarChar(255)
  isActive         Boolean?           @default(true)
  createdAt        DateTime?          @default(now()) @db.Timestamp(0)
  createdBy        String?            @db.Char(36)
  updatedAt        DateTime?          @default(now()) @db.Timestamp(0)
  updatedBy        String?            @db.Char(36)
  role             user_role          @default(teacher)
  permissions      user_permissions[]
  sentencesCreated sentences[]        @relation("createdby")
  sentencesUpdated sentences[]        @relation("updatedby")
  taskCreated      task[]             @relation("createdby")
  taskUpdated      task[]             @relation("updatedby")
}

model user_permissions {
  id         String     @id @db.Char(36)
  permission permission
  userId     String
  createdAt  DateTime?  @default(now()) @db.Timestamp(0)
  createdBy  String?    @db.Char(36)
  user       users      @relation(fields: [userId], references: [id])

  @@index([userId])
}

model settings {
  id         String @id @db.Char(36)
  timeOffset Int    @default(0)
}

model task_payload_for_debug {
  id          String    @id @db.Char(36)
  taskId      String    @default("0") @db.Char(36)
  payload     Json
  createdAt   DateTime? @default(now()) @db.Timestamp(0)
  createdBy   String?   @db.Char(36)
}

enum content_type {
  lexical
  file
  audio
}

enum permission {
  editFavorites
  deleteNonFavSentences
}

enum user_role {
  teacher
  admin
  disabled
}

enum task_mode {
  translation
  listen
  bytime
  phantom
  audiodic
  voice
}

enum campaign_type {
  group
  student
}

enum task_type {
  home
  class
  sandbox
}

enum task_sentences_defaultTaskView {
  text
  audio
}

enum sentences_sex {
  m
  f
}

enum locales {
  en
  ru
  he
}

enum speed_mode {
  slow
  medium
  fast
}

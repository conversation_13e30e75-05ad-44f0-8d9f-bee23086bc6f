import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {paramsToKeyValue} from "$api/core/utils";
import {
    createGroupScheduleChanges,
    deleteGroupScheduleChanges,
    getGroupScheduleChanges
} from "$api/core/services/groupSchedule.service";
import {mapper} from "$common/core/mapper";
import {createGroupHoliday} from "$api/core/services/groupHolidays.service";
import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";


export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {groupId} = paramsToKeyValue(url.searchParams);
        return await getGroupScheduleChanges(groupId)
    });


export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const groupSchedule = await event.request.json();
        const userId = event?.locals?.user?.id;
        const dto = mapper<GroupScheduleDto, unknown>({
            ...groupSchedule,
            hoursPerSession: +groupSchedule.hoursPerSession
        })
        return await createGroupScheduleChanges(dto, userId);
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);
        return await deleteGroupScheduleChanges(id)
    });

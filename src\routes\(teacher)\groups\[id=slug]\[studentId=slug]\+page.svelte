<script lang="ts">
    import {page} from '$app/stores';
    import {onMount} from 'svelte';
    import {StudentFilterState} from '$lib/state/student-filter-state';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import {goto, invalidate} from '$app/navigation';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconSend} from '@tabler/icons-svelte';
    import {SendNotificationModalState} from '$lib/state/send-notification-state';
    import {get} from 'svelte/store';
    import {generateGuid} from '$common/core/utils';
    import {t} from '$lib/i18n/config';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {format, intervalToDuration, min} from 'date-fns';
    import BaseInput from '$components/common/BaseInput.svelte';
    import CountSpan from '$components/common/CountSpan.svelte';
    import type {CompletionTaskDto} from '$common/models/dtos/task.dto';
    import {durationToMinutesToHuman, loadingWrap} from '$lib/common/utils';
    import StudentTaskResultsRow from '$components/groups/StudentTaskResultsRow.svelte';
    import StudentAdditionalTaskResultTable from '$components/groups/StudentAdditionalTasksResultTable.svelte';
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";
    import {pageSize, TaskPagingState} from "$lib/state/task-paging-state";

    export let data;

    let currentSelectedStudentId = $page.params.studentId;
    let comment = '';
    const modalStore = getModalStore();
    $: studentName = `${currentStudent?.firstname} ${currentStudent?.lastname}`;

    $: students = data.students.data;
    $: tasks = data.tasks.data;
    $: count = data?.tasks?.count || 0;
    $: accordeonState = tasks.length > 0 && tasks.map((x) => {
        return {[x.id]: false};
    });
    $: taskCount = tasks.filter((t) => t.commentPublic.includes(comment)).length;
    $: currentStudent = students.find((s) => s.id === currentSelectedStudentId);

    $: options = data?.groupHistory?.data?.map((s) => {
        return {displayValue: `${s.student.firstname} ${s.student.lastname}`, value: s.student.id};
    });
    $: tableTitles = [
        {name: $t('groups.studentAdditionalTask.table.name')},
        {name: $t('groups.studentAdditionalTask.table.comment')},
        {name: $t('groups.studentAdditionalTask.table.done')},
        {name: $t('groups.studentAdditionalTask.table.result')},
        {name: $t('groups.studentAdditionalTask.table.attempts')},
        {
            name: $t('groups.studentAdditionalTask.table.actions'),
            class: 'w-[230px]'
        }
    ];


    const loadMore = async () => {
        if (count > tasks.length) {
            TaskPagingState.set({
                skip: 0,
                take: pageSize + tasks.length
            });
            await loadingWrap(async () => {
                await invalidate('load:groups/studentId');
            });
        }
    };

    const openSendNotificationStudentModal = (
        type: 'student' | 'task',
        date?: string,
        taskType?: string
    ) => {
        const groupLang = data.group.lang === 'RU' ? 'ru' : 'en';

        if (type === 'student') {
            SendNotificationModalState.set({
                ...get(SendNotificationModalState),
                id: generateGuid(),
                type: 'student',
                title: `${studentName}`,
                recipientId: currentSelectedStudentId
            });
        } else {
            const nameTask =
                date &&
                `${
                    taskType === 'class'
                        ? $t(`t.classTaskWithTranslate.${groupLang}`)
                        : $t(`t.homeTaskWithTranslate.${groupLang}`)
                } ${format(new Date(date), 'P')}`;
            SendNotificationModalState.set({
                ...get(SendNotificationModalState),
                id: generateGuid(),
                type: 'student',
                title: `${studentName} ${$t(
                    `campaigns.modal.assignment.${groupLang}`
                )}  ${nameTask?.toLowerCase()}`,
                recipientId: currentSelectedStudentId
            });
        }
        modalStore.trigger({type: 'component', component: 'sendNotificationStudentModal'});
    };

    const handleChangeCurrentStudent = async () => {
        const link = `${$page.url.origin}/groups/${$page.params.id}/${currentSelectedStudentId}`;
        await goto(link);
    };

    const calculateDoneIn = (result: CompletionTaskDto, mode = 'translation') => {
        if (result && result.results?.filter((x) => x.mode === mode)?.length > 0) {
            const minimumLastTaskTranslationStartDate = min(
                result.results.filter((x) => x.mode === mode).map((x) => new Date(x.startedAt))
            );
            const lastTaskCompletionDelay = intervalToDuration({
                start: new Date(minimumLastTaskTranslationStartDate),
                end: new Date(result.taskReleaseDate)
            });
            durationToMinutesToHuman(lastTaskCompletionDelay);

            return durationToMinutesToHuman(lastTaskCompletionDelay);
        } else {
            return '';
        }
    };

    onMount(() => {
        $StudentFilterState.groupId = $page.params.id;
    });
</script>

<div class="h-[calc(100vh-120px)] overflow-hidden  px-6 flex flex-col gap-5 pt-6">
    <div class="flex justify-between">
        <ol class="breadcrumb">
            <li class="crumb">
                <a class="anchor" href="/groups/{$page.params.id}"
                ><h1 class="title mb-1 font-medium text-xl">{data.group.name}</h1></a
                >
            </li>
            <li class="crumb-separator !m-1" aria-hidden="true">
                <h1 class="title mb-1 font-medium text-xl">&#706;</h1>
            </li>
            <li class="crumb !ml-0">
                <a class="anchor" href="/groups/{$page.params.id}"
                ><h1 class="title mb-1 font-medium text-xl">
                    {$t('groups.studentAdditionalTask.breadcrumb.students')}
                </h1></a
                >
            </li>
            <li class="crumb-separator !m-1" aria-hidden="true">
                <h1 class="title mb-1 font-medium text-xl">&#706;</h1>
            </li>
            <li class="crumb">
                <BaseSelect
                        className="font-bold text-xl w-fit p-1"
                        name="currentSelectedStudent"
                        bind:value={currentSelectedStudentId}
                        {options}
                        on:change={handleChangeCurrentStudent}
                />
            </li>
        </ol>
        <div class="flex justify-end items-end gap-2">
            <BaseButton on:click={() => openSendNotificationStudentModal('student')} size="lg">
                <IconSend size={20} stroke="1.5"/>
            </BaseButton>
            {#if currentStudent?.whatsApp}
                <a
                        target="_blank"
                        href="https://web.whatsapp.com/send/?phone={currentStudent.whatsApp}&text=%D0%97%D0%B4%D1%80%D0%B0%D0%B2%D1%81%D1%82%D0%B2%D1%83%D0%B9%D1%82%D0%B5%2C+%D0%B8%D0%BD%D1%82%D0%B5%D1%80%D0%B5%D1%81%D1%83%D0%B5%D1%82+%D0%B2%D0%B0%D1%88%D0%B5+%D0%BE%D0%B1%D1%8A%D1%8F%D0%B2%D0%BB%D0%B5%D0%BD%D0%B8%D0%B5+%D0%BE+%D0%BF%D1%80%D0%BE%D0%B4%D0%B0%D0%B6%D0%B5+%D0%BC%D0%B0%D1%88%D0%B8%D0%BD%D1%8B.&type=phone_number&app_absent=0"
                >
                    <BaseButton
                            size="lg"
                            className="bg-green-800 dark:!bg-green-800 dark:!text-white"
                            on:click={(e) => {
						e.stopPropagation();
					}}
                    >
                        <IconSend size={20} stroke="1.5"/>
                    </BaseButton>
                </a>
            {/if}
        </div>
    </div>
    <hr/>
    <div class="flex justify-between items-center">
        <h1 class="title mb-1 font-medium text-xl">
            {$t('groups.studentAdditionalTask.title')}
            <CountSpan count={count}/>
        </h1>
        <div class="hidden">
            <BaseInput title="Search by comment" bind:value={comment}/>
        </div>
    </div>
    <div class=" flex flex-col overflow-y-auto overflow-x-auto ">

        <table class="table table-hover table-compact h-fit  overflow-auto">
            <thead on:keypress>
            <tr>
                {#each tableTitles as title}
                    <th class="text-right {title?.class}">{title.name}</th>
                {/each}
            </tr>
            </thead>
            <tbody class="flex-1 overflow-auto">
            <InfiniteTableScrollContainer loadMoreFunc={loadMore}>
                {#each tasks.filter((t) => t.commentPublic.includes(comment)) as row, rowIndex (row.id)}
                    {@const studentResults = row.results.find(
                        (x) => x.studentId === currentSelectedStudentId
                    )}
                    <StudentTaskResultsRow
                            {row}
                            type={row.type}
                            date={row.date}
                            results={studentResults}
                            {currentStudent}
                            commentPublic={row.commentPublic}
                            on:openSendNotificationStudentModal={() =>
							openSendNotificationStudentModal('task', row.date, row.type)}
                            on:changeAccordeonState={() => (accordeonState[row.id] = !accordeonState[row.id])}
                            {calculateDoneIn}
                    />
                    {#if accordeonState[row.id]}
                        <StudentAdditionalTaskResultTable
                                results={row.results}
                                {currentSelectedStudentId}
                                {calculateDoneIn}
                        />
                    {/if}
                {/each}
            </InfiniteTableScrollContainer>
            </tbody>
        </table>
    </div>
</div>

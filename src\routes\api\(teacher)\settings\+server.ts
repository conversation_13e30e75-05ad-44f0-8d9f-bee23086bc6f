import {wrapFunc} from "$api/core/misc/response-wrapper";
import {getSettings, putOffset} from "$api/core/services/settings.service";
import type {RequestEvent} from "@sveltejs/kit";


export const GET = async () =>
    wrapFunc(async () => {
        return await getSettings();
    });


export const PUT = async (e: RequestEvent) =>
    wrapFunc(async () => {
        const timeOffset = await e.request.json();
        return await putOffset(timeOffset);
    });
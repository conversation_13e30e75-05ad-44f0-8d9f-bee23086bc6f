import {Constants} from "$api/core/constants";

export const Routes = {
    Main: '/',
    StudentMain: '/home',
    Login: '/login',
    StudentLogin: '/signin',
    Sentences: '/sentences',
    Users: '/users',
    T: '/t/',
    PublicT: '/pt/',
    Tasks: '/tasks/'
}

const OnlyTeacherRoutes = ['/campaigns', '/groups', '/incentive', '/students', '/tasks', '/complaints']
const OnlyAdminRoutes = ['/users', '/sentences', '/settings'];
export const UnprotectedRoutes = [
    '/login',
    '/signin',
    '/api/students/requests/en',
    '/api/students/requests/ru',
    '/pt',
    '/api/s/public-tasks',
    '/api/s/results',
    '/api/exclusions'
];

export const ForbiddenByRoleRoutes = {
    [Constants.TeacherRole]: [...OnlyAdminRoutes],
    [Constants.StudentRole]: [...OnlyTeacherRoutes, ...OnlyAdminRoutes],
    [Constants.DisabledRole]: [...OnlyTeacherRoutes, ...OnlyAdminRoutes]
}

export const AlreadyLoggedInForbiddenRoutes = ['/login', '/signin'];

export const HomePageByRole = {
    [Constants.AdminRole]: Routes.Main,
    [Constants.TeacherRole]: Routes.Main,
    [Constants.StudentRole]: Routes.StudentMain
}
`SQL script to run`

```sql
ALTER TABLE `groups` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `incentiveContent` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `langs` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `message_campaigns` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `message_notifications` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `sentences` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `student_requests` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
<PERSON><PERSON>R TABLE `student_results` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `students` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `students_groups` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `task` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `task_additional` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `task_incentive_content` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `task_sentences` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `translations` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
ALTER TABLE `users` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_520_ci;
```

to find incostistancy in complaints
select c.* from complaints c left join task_sentences ts on c.sentenceId = ts.sentenceId and c.taskId = ts.taskId left join sentences s on s.id = ts.sentenceId where ts.sentenceId is null;

to find incostistancy in sentences
select t.* from task t left join task_sentences ts on t.Id = ts.taskId left join sentences s on s.id = ts.sentenceId where s.Id is null

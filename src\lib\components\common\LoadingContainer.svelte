<script>
    import LoadingSpinner2 from "$lib/components/common/LoadingSpinner2.svelte";
    import {LoadingState} from '$lib/state/loading-state';
    import {navigating} from '$app/stores';
    import {browser} from '$app/environment';


    $: loading = $LoadingState || Boolean($navigating);
    $: if (browser) {
        document.body.className = loading ? 'cursor-loading' : '';
    }
</script>

<svelte:body class={loading ? 'cursor-loading' : ''}/>

{#if loading}
    <LoadingSpinner2/>
{/if}

<style>
    :global(.cursor-loading) {
        cursor: wait;
    }
</style>

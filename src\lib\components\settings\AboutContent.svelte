<script lang="ts">
    import type {AboutContentsDto} from "$common/models/dtos/AboutContents.dto";
    import AboutContentItem from "$components/settings/AboutContentItem.svelte";
    import {AboutContentsApiClient} from "$lib/core/api-clients/aboutContents-api.client";
    import {invalidate} from "$app/navigation";
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";
    import {t} from "$lib/i18n/config";


    export let aboutContents: AboutContentsDto[];

    const save = async (data: Omit<AboutContentsDto, 'lang'>) => {
        const res = await new AboutContentsApiClient().updateAboutContent(data);
        if (res) {
            await invalidate('load:settings');
            NotificationStore.push({
                type: NotificationType.success,
                message: t.get('settings.notificationsAbout.update')
            }, 5);
        } else {
            console.log('error')
            NotificationStore.push({
                type: NotificationType.error,
                message: t.get('settings.notificationsAbout.error')
            }, 5);
        }
    }

</script>

<div class="flex flex-col gap-5 variant-glass-primary p-14 card">
    {#each aboutContents as content}
        <AboutContentItem id={content.id} content={content.value} lang={content.lang} save={save}/>
    {/each}
</div>



import {GroupApiClient} from '$lib/core/api-clients/group-api-client';
import {StudentApiClient} from '$lib/core/api-clients/student-api-client';
import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
import {StudentFilterState} from '$lib/state/student-filter-state';
import {TaskFilterState} from '$lib/state/task-filter-state';
import type {
    PageLoad
} from '../../../../../../.svelte-kit/types/src/routes/(teacher)/groups/[id=slug]/[studentId=slug]/$types';
import {initialStudentFilter} from "$common/models/filters/student-filter.dto";
import {initialTaskFilter} from "$common/models/filters/task-filter.dto";
import {initialStudentPaging, StudentPagingState} from "$lib/state/student-paging-state";

export const ssr = false;

export const load: PageLoad = async ({params, depends}) => {
    try {
        depends('load:groups/studentId');
        const {id, studentId} = params;
        StudentFilterState.set({...initialStudentFilter, groupId: id!});
        TaskFilterState.set({...initialTaskFilter, groupId: id!});
        StudentPagingState.set({...initialStudentPaging, take: -1})

        return {
            group: new GroupApiClient().getGroupById(id!),
            students: new StudentApiClient().getStudents(true),
            tasks: new TaskApiClient().getTasks(),
            groupHistory: new GroupApiClient().getGroupStudents(id!),
        };
    } catch (error) {
        return error;
    }
};

import {fail, redirect, type RequestEvent} from '@sveltejs/kit';
import {z} from 'zod';
import {loginForStudent} from '$api/core/services/auth.service';
import {superValidate} from 'sveltekit-superforms/server';
import {Routes} from '$common/core/routes';

export const ssr = false;

const schemaSignIn = z.object({
    tz: z
        .string()
        .min(6, {
            message: 'sign-in.formFieldsErrors.tzMin'
        })
        .max(12, {message: 'sign-in.formFieldsErrors.tzMax'}),
    dob: z.string().length(10, {
        message: 'sign-in.formFieldsErrors.dob'
    })
});

export const actions = {
    default: async (event: RequestEvent) => {
        const form = await superValidate(event, schemaSignIn);
        if (!form.valid) return fail(400, {form});

        const password = form.data.dob;
        const result = await loginForStudent(form.data.tz, password!, event);

        if (result.error) return fail(401, {error: result.error, form});

        throw redirect(302, Routes.StudentMain);
    }
};

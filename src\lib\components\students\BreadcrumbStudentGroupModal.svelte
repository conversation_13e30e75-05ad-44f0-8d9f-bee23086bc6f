<script lang="ts">
	import { getModalStore } from '@skeletonlabs/skeleton';
	import { GroupHistoryState } from '$lib/state/group-history-state';

	export let currentModalName: string;
	export let groupName = '';

	const modalStore = getModalStore();
</script>

{#if $GroupHistoryState.previousModalName}
	<ol class="flex items-center">
		<li class="crumb">
			<a class="text-lg anchor cursor-pointer !ml-3" on:click={() => modalStore.close()}
				>{$GroupHistoryState.previousModalName}</a
			>
		</li>
		<li class="crumb-separator font-medium text-xl !ml-3" aria-hidden>{">"}</li>
		<li class="text-lg font-bold">
			{currentModalName} <span dir="rtl">{groupName}</span>
		</li>
	</ol>
{/if}

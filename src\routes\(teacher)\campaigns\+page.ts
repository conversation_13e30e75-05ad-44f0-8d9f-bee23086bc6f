import {CampaignApiClient} from '$lib/core/api-clients/campaign-api.client';
import {UserApiClient} from "$lib/core/api-clients/user-api-client";
import type {Load} from "@sveltejs/kit";

export const ssr = false;
export const load:Load = async ({depends}) => {
    depends('load:campaigns');

    try {
        return{
           campaigns: new CampaignApiClient().getAllCampaigns(),
           users: new UserApiClient().getUsers()
        }
    } catch (error) {
        return error;
    }
};

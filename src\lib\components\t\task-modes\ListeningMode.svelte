<script lang="ts">
	import type { TaskResultDto } from '$common/models/dtos/task.dto';
	import {
		IconCircleCheckFilled
	} from '@tabler/icons-svelte';
	import BaseButton from '$components/common/BaseButton.svelte';
	import {createEventDispatcher, onMount} from 'svelte';
	import { initialDate } from '$lib/state/task-current-completion.state';
	import { checkListeningResponse, isInitialDate } from '$lib/common/task-helpers';
	import { t } from '$lib/i18n/config';
	import AudioPlayer from "$components/t/AudioPlayer.svelte";
	import _ from "lodash";
	import { page } from '$app/stores';

	const dispatcher = createEventDispatcher();

	export let model: TaskResultDto;
	export let maxScore;

	$: completions = model?.completions || [];
	$: revealMode = !isInitialDate(model.finishedAt, initialDate);

	$: tracks = completions.map((x, i) => {
		return {
			artist: i+1,
			title: x.correctValue,
			filename: `https://${$page.data.envs.VITE_S3_BUCKET}.s3.${$page.data.envs.VITE_S3_REGION}.amazonaws.com/${$page.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${x.audioUrl}`,
		}
	})

	let finishings = [];

	onMount(() => {
		finishings = Array(completions.length).fill(false);
	});

	const check = () => {
		if (!revealMode) {
			finishings.forEach((f, i) => {
				model.completions[i].isCorrect = f;
			});

			checkListeningResponse(model, maxScore);
			dispatcher('submitTask');
		}
	};

</script>

<AudioPlayer bind:finishings {tracks}
			 on:allDone={check}
			 on:listened|once={() => (model.startedAt = new Date())}
/>

{#if !revealMode}
	<div class="flex w-full mt-7">
		<BaseButton on:click={check} className="w-full">
			{$t('t.button')}
			<IconCircleCheckFilled />
		</BaseButton>
	</div>
{/if}

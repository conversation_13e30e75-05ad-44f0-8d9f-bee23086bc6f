<script lang="ts">
    import {calculateStudentStudyHoursAndDays, durationToMinutesToHuman} from '$lib/common/utils';
    import {createEventDispatcher} from 'svelte';
    import BaseButton from '../common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import {IconEdit, IconSend} from '@tabler/icons-svelte';
    import type {ExtendedStudentDto} from '$common/models/dtos/student.dto';
    import {format} from 'date-fns';
    import {StudentFilterState} from '$lib/state/student-filter-state';
    import SortingTableHeader from '$components/common/SortingTableHeader.svelte';
    import {StudentSortingState} from '$lib/state/student-paging-state';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import ResultBadge from '$components/t/ResultBadge.svelte';
    import {goto} from '$app/navigation';
    import {page} from '$app/stores';
    import {modeCurrent} from '@skeletonlabs/skeleton';
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";
    import StudentsTableRow from "$components/students/StudentsTableRow.svelte";

    export let students: ExtendedStudentDto[] = [];


    export let loadMoreFunc;

    export let groups: GroupDto[] = [];


    export let fromGroup = false;

    export let generalHolidays;

    const id = $page.params.id;


    const dispatchModal = createEventDispatcher();

    $: tableHeaders = $StudentFilterState.showFullTable
        ? [
            {
                title: 'students.students.table.head.fullname',
                sortBy: 'firstname',
                class: `!max-w-[150px] !min-w-[150px] pinned-header${!$modeCurrent ? '-dark' : ''}  `,
                pinned: true
            },
            {
                title: 'students.students.table.head.whatsapp',
                class: `!max-w-[170px] !min-w-[170px] pinned-header${!$modeCurrent ? '-dark' : ''}`,
                pinned: true

            },
            {
                title: 'students.students.table.head.group',
                class: `!max-w-[200px] !min-w-[200px] pinned-header${!$modeCurrent ? '-dark' : ''}`,
                pinned: true

            },
            {title: 'students.students.table.head.tz', sortBy: 'tz'},
            {title: 'students.students.table.head.email', sortBy: 'email'},

            {title: 'students.students.table.head.regDate', sortBy: 'registrationDate'},
            {
                title: 'students.students.table.head.startInGroupDate',
                sortBy: 'currentGroupStartDate'
            },
            {title: 'students.students.table.head.comment', sortBy: 'comment'},
            {title: 'students.students.table.head.lastTaskResult', sortBy: 'lastTaskScore'},
            {title: 'students.students.table.head.averageTaskResult', sortBy: 'averageTaskScore'},
            {title: 'students.students.table.head.lastTaskDelay', sortBy: 'lastTaskDelay'},
            {title: 'students.students.table.head.averageTaskDelay', sortBy: 'averageTaskDelay'},
            {
                title: 'students.students.table.head.statistics'
            },
            {title: 'students.students.table.head.action'}
        ]
        : [
            {
                title: 'students.students.table.head.fullname',
                sortBy: 'firstname',
                class: `!max-w-[150px] !min-w-[150px] pinned-header${!$modeCurrent ? '-dark' : ''} `,
                pinned: true

            },
            {
                title: 'students.students.table.head.whatsapp',
                class: `!max-w-[170px] !min-w-[170px] pinned-header${!$modeCurrent ? '-dark' : ''} `,
                pinned: true

            },
            {
                title: 'students.students.table.head.group',
                class: `pinned-header${!$modeCurrent ? '-dark' : ''}`,
                pinned: true
            },

            {title: 'students.students.table.head.regDate', sortBy: 'registrationDate'},
            {
                title: 'students.students.table.head.startInGroupDate',
                sortBy: 'currentGroupStartDate',
            },
            {title: 'students.students.table.head.comment', sortBy: 'comment'},
            {title: 'students.students.table.head.lastTaskResult', sortBy: 'lastTaskScore'},
            {title: 'students.students.table.head.averageTaskResult', sortBy: 'averageTaskScore'},
            {title: 'students.students.table.head.lastTaskDelay', sortBy: 'lastTaskDelay'},
            {
                title: 'students.students.table.head.averageTaskDelay',
                sortBy: 'averageTaskDelay'
            },
            {
                title: 'students.students.table.head.statistics'
            },
            {title: 'students.students.table.head.action'}
        ];


    const handleClickToRow = async (id: string) => {
        if (fromGroup) {
            const link = `${$page.url.origin}${$page.url.pathname}/${id}`;
            await goto(link);
        }
    };
</script>

<div class="table-container dark:text-black mt-5">
    <table class=" table !overflow-x-auto !overflow-y-auto table-hover table-compact ">
        <thead on:keypress>
        <tr>
            {#each tableHeaders as header}
                {#if header.title === 'students.students.table.head.statistics'}
                    <OnlyForRole>
                        <th class="text-right {!$modeCurrent ? 'wright-color-dark': 'wright-color'} accent-primary-300  !shadow-none !border-none !ring-0 !rounded-none {header.class? header.class : ''}">
                            <SortingTableHeader
                                    {header}
                                    state={StudentSortingState}
                            />
                        </th>
                    </OnlyForRole>
                {:else}
                    <th class="text-right {!$modeCurrent ? 'wright-color-dark': 'wright-color'} accent-primary-300  !shadow-none !border-none !ring-0 !rounded-none {header.class? header.class : ''}">
                        <SortingTableHeader
                                {header}
                                state={StudentSortingState}
                        />
                    </th>
                {/if}

            {/each}
        </tr>
        </thead>
        <tbody class="dark:text-white">
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each students as row, rowIndex}
                {@const groupId = groups.find((group) => group.name === row.currentGroup)?.id}
                <StudentsTableRow
                        {fromGroup}
                        on:triggerSendMessageModal={(e)=>dispatchModal('triggerSendMessageModal',{...e.detail})}
                        on:triggerEditModal={(e)=>dispatchModal('triggerEditModal',{...e.detail})} groupId={groupId}
                        {generalHolidays}
                        row={row} handleClickToRow={handleClickToRow}/>
            {/each}

        </InfiniteTableScrollContainer>
        </tbody>
    </table>
</div>

<style lang="css">
    .pinned-header {
        background: rgb(var(--color-surface-200));
        color: black !important;
    }


    .pinned-header-dark {
        background: rgb(var(--color-surface-700));
        color: rgb(var(--theme-font-color-dark)) !important;
    }


    table tr th:first-child {
        position: sticky;
        top: 0;
        right: 0;
        z-index: 10;
    }


    table tr th:nth-child(2) {
        position: sticky;
        top: 0;
        right: 150px;
        z-index: 10;
    }


    table tr th:nth-child(3) {
        position: sticky;
        top: 0;
        right: 320px;
        z-index: 10;
        overflow: hidden;
    }


    table tr th:nth-child(-n + 3) {
        z-index: 11;
        padding: 5px;
        margin: 0;
    }


    .wright-color {
        color: rgb(var(--theme-font-color-base)) !important;
    }


    .wright-color-dark {
        color: rgb(var(--theme-font-color-dark)) !important;
    }
</style>


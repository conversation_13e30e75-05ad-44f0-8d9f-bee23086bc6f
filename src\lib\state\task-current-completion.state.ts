import type {
    CompletionTaskStateModel, PublicCompletionTaskStateModel,
    TaskDto
} from "$common/models/dtos/task.dto";
import {TaskMode} from "$common/models/enums";
import {localStorageStore} from '@skeletonlabs/skeleton';
import {readable, type Writable, writable} from "svelte/store";


const initialCurrentCompletion: CompletionTaskStateModel = {
    id: '',
    currentMode: TaskMode.translation,
    task: {} as TaskDto,
    results: [],
    currentResult: null,
    currentScore: 0,
    currentScoreAbsolute: 0,
    intermediateState: false
}

const publicInitialCurrentCompletion: PublicCompletionTaskStateModel = {
    ...initialCurrentCompletion,
    name: null,
    whatsapp: null
}


export const initialDate = new Date(1970);

export const time = readable(new Date(), function start(set) {
    setInterval(() => {
        set(new Date());
    }, 1000);
});

export const getCertainCompletionState = (taskId: string, persistent = true) : Writable<CompletionTaskStateModel | PublicCompletionTaskStateModel> => persistent
    ? localStorageStore<CompletionTaskStateModel>(taskId, initialCurrentCompletion, {storage: "local"})
    : publicSingleCompletionState;

const publicSingleCompletionState = writable<PublicCompletionTaskStateModel>({...publicInitialCurrentCompletion});
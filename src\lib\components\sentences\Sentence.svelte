<script lang="ts">
    import BaseButton from '../common/BaseButton.svelte';
    import BaseInput from '../common/BaseInput.svelte';
    import BaseSelect from '../common/BaseSelect.svelte';
    import AudioControls from './AudioControls.svelte';
    import {LevelFilter, LanguageFilter} from '$common/models/enums';
    import {loadingWrap, mapEnumToOptions} from '$lib/common/utils';
    import {afterUpdate, createEventDispatcher, onMount} from 'svelte';
    import {
        IconLetterT,
        IconDeviceFloppy,
        IconWoman,
        IconMan,
        IconHeadphones,
        IconArrowBackUp
    } from '@tabler/icons-svelte';
    import NotificationStore from '../../state/notification-state';
    import {generateGuid} from '$common/core/utils';
    import type {
        SentenceDto,
        SentenceInTaskDto,
        TranslationDto
    } from '$common/models/dtos/sentence.dto';
    import {NotificationType} from '$common/models/enums';
    import {SentenceApiClient} from '$lib/core/api-clients/sentence-api-client';
    import {beforeNavigate, goto, invalidate} from '$app/navigation';
    import {getModalStore, type ModalSettings} from '@skeletonlabs/skeleton';
    import {t} from '$lib/i18n/config';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import Shepherd from 'shepherd.js';
    import '../../../../node_modules/shepherd.js/dist/css/shepherd.css';
    import _ from 'lodash';
    import He from '$components/common/He.svelte';
    import {page} from "$app/stores";
    import AdminButtons from "$components/sentences/AdminButtons.svelte";
    import SentenceDescription from "$components/sentences/SentenceDescription.svelte";
    import {Constants} from "$api/core/constants";
    import {LockedDragAndDropState} from "$lib/state/lock-dnd-state";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";


    export let sentence: SentenceInTaskDto;

    export let selectedLang: string;
    let prevSelectedLang: string;
    const modalStore = getModalStore();
    const role = $page?.data?.user?.role


    export let isTaskMode = false;

    export let isComplaintsMode = false;

    export let displayAsText = true;
    export let checkToDisableSaveButtonInTask: () => void;

    export let id: string;
    const dispatch = createEventDispatcher();
    let currentTranslationValue: string | null;
    let initialState: SentenceInTaskDto;
    let canBeSaved = false;
    let canBeRollBack = false;
    let once = true;
    let routeToProceedBypassedNavigation;
    let uniqClass = `shepherd${id}`;
    let tourStarted = false;

    $: disableSentence = sentence.isFavorite && role !== 'admin' && !isTaskMode && !isComplaintsMode;

    $:console.log(isComplaintsMode)


    $: notSavedConfirmationModal = {
        type: 'confirm',
        title: $t('sentences.modalSentences.title'),
        body: $t('sentences.modalSentences.body'),
        buttonTextCancel: $t('sentences.modalSentences.buttonTextCancel'),
        buttonTextConfirm: $t('sentences.modalSentences.buttonTextConfirm'),
        regionFooter: 'flex gap-5 border-2 border-red-900',

        response: (r: boolean) => {
            if (r) {
                canBeSaved = false;
                goto(routeToProceedBypassedNavigation);
            }
        }
    };

    $: if (canBeSaved) {
        window.onbeforeunload = !window.onbeforeunload
            ? () => {
                return 'Are u sure?';
            }
            : null;
    } else {
        window.onbeforeunload = null;
    }

    $: if (sentence.audioRecordedButNotYetSaved) {
        detectChanges();
    }

    beforeNavigate(async (event) => {
        const {willUnload} = event;
        if (!willUnload && canBeSaved) {
            const {to, cancel} = event;
            const {route} = to;
            cancel();
            routeToProceedBypassedNavigation = route.id.replace(/\/\([^)]+\)/, '');
            modalStore.trigger(notSavedConfirmationModal);
        }
    });

    onMount(() => {
        resetSentenceCompareState();
        setCurrentTranslationValue();
    });

    afterUpdate(() => {
        if (sentence.isNew && once) {
            setCurrentTranslationValue();
            resetSentenceCompareState();
            once = false;
        }

        if (selectedLang !== prevSelectedLang && selectedLang != -1) {
            setCurrentTranslationValue();
        }
        prevSelectedLang = selectedLang;
    });

    const resetSentenceCompareState = () => {
        initialState = {...sentence, translations: sentence.translations.map((t) => ({...t}))};
    };
    const resetSentenceAudioCompareState = () => {
        initialState.audioUrl = sentence.audioUrl;
        initialState = {...initialState};
    };

    let tour = new Shepherd.Tour({
        useModalOverlay: true,
        defaultStepOptions: {
            classes: 'shadow-md bg-purple-dark',
            scrollTo: {behavior: 'smooth', block: 'center'},
            useModalOverlay: true
        }
    });

    tour.addStep({
        attachTo: {
            element: `.${uniqClass}`
        },
        classes: 'example-step-extra-class',
        when: {
            show() {
                const vElement = this.getElement() as HTMLElement;
                if (vElement)
                    vElement.focus = () => {
                        /* Do nothing */
                    };
            }
        }
    });

    const checkCanBeSaved = () => {
        if (isTaskMode) {
            canBeSaved =
                !_.isEqual(
                    _.omit(initialState,
                        ['audioRecordedButNotYetSaved']),
                    _.omit(sentence,
                        ['audioRecordedButNotYetSaved'])
                ) &&
                sentence.value.length > 0 &&
                currentTranslationValue?.length > 0;
        } else {
            let enTranslationValue = sentence?.translations?.find((t) => t.lang === 'EN')?.value ?? '';
            let ruTranslationValue = sentence?.translations?.find((t) => t.lang === 'RU')?.value ?? '';
            const initialStateWithoutAudio = {
                ..._.omit(initialState,
                    ['audioRecordedButNotYetSaved'])
            };
            const sentenceWithoutAudio = {
                ..._.omit(sentence,
                    ['audioRecordedButNotYetSaved'])
            };
            const enTranslationNotEmpty = enTranslationValue.length > 0;
            const ruTranslationNotEmpty = ruTranslationValue.length > 0;

            canBeSaved = !_.isEqual(initialStateWithoutAudio, sentenceWithoutAudio)
                && sentence.value.length > 0
                && (enTranslationNotEmpty || ruTranslationNotEmpty);
        }
    };

    const checkCanBeRollBack = () => {
        canBeRollBack = !_.isEqual(
            _.omit(initialState, ['audioRecordedButNotYetSaved']),
            _.omit(sentence, ['audioRecordedButNotYetSaved']));
    };

    const detectChanges = () => {
        checkCanBeSaved();
        checkCanBeRollBack();
        if (isTaskMode && checkToDisableSaveButtonInTask) {
            checkToDisableSaveButtonInTask();
        }

        if (canBeRollBack && !tourStarted) {
            toggleShepherd(true)
        } else if (tourStarted && !canBeRollBack) {
            toggleShepherd(false)
        }
    };

    const toggleShepherd = (value) => {
        if (value) {
            tourStarted = true;
            tour.start();
            $LockedDragAndDropState = true;
            const pageWithScroll: HTMLElement = document.getElementById('page');
            pageWithScroll.style.overflow = 'hidden';
            const container: HTMLElement = document.querySelector('.shepherd-modal-overlay-container');
            container.style.zIndex = '500';
        } else {
            tourStarted = false;
            tour.complete();
            $LockedDragAndDropState = false;
            const element: HTMLElement = document.getElementById('page');
            element.style.overflow = 'auto';
        }
    }

    const setCurrentTranslationValue = () => {
        currentTranslationValue =
            sentence?.translations?.find((t) => t?.lang === selectedLang)?.value ?? '';
    };

    const changeLanguage = () => {
        setCurrentTranslationValue();
        detectChanges();
    };

    const changeGender = () => {
        sentence.sex = sentence.sex === 'f' ? 'm' : 'f';
        detectChanges();
    };

    const changeFavorite = () => {
        sentence.isFavorite = !sentence.isFavorite;
        detectChanges()
    }

    const changeDisplayMode = () => {
        displayAsText = !displayAsText;
        sentence.displayAsText = displayAsText;
        $CurrentEditableState.sentences = $CurrentEditableState.sentences.map((sentenceState) => {
            if (sentenceState.id === sentence.id) {
                sentenceState.displayAsText = displayAsText;
            }
            return sentenceState;
        });
    };

    const onTranslationChanged = () => {
        let translationItem = sentence.translations.find((x) => x.lang === selectedLang);
        if (!translationItem) {
            sentence.translations.push({
                id: generateGuid(),
                lang: selectedLang,
                value: currentTranslationValue
            } as TranslationDto);
        } else {
            translationItem.value = currentTranslationValue ?? '';
        }

        detectChanges();
    };

    const deleteSentence = async () => {
        const client = new SentenceApiClient();
        const responseModal = await triggerModal({
            title: $t('sentences.modalSentenceDelete.title'),
            body: $t('sentences.modalSentenceDelete.body'),
            buttonTextCancel: $t('sentences.modalSentenceDelete.buttonTextCancel'),
            buttonTextConfirm: $t('sentences.modalSentenceDelete.buttonTextConfirm')
        });

        if (responseModal) {
            let result = await client.deleteSentence(sentence.id);
            if (result) {
                NotificationStore.push({
                    type: NotificationType.success,
                    message: $t('sentences.sentenceNotification.delete.success')
                });
                await invalidate('load:sentences');
                dispatch('globalDelete')
            } else {
                NotificationStore.push({
                    type: NotificationType.error, message: $t('sentences.sentenceNotification.delete.error')
                });
            }
            toggleShepherd(false);
        } else {
            NotificationStore.push({
                type: NotificationType.success, message: $t('sentences.sentenceNotification.delete.cancel')
            })
        }
    }


    const triggerModal = async ({title, body, buttonTextCancel, buttonTextConfirm}: {
        title: string,
        body: string,
        buttonTextCancel: string,
        buttonTextConfirm: string
    }) => {

        return await new Promise<boolean>((resolve) => {
            const modal: ModalSettings = {
                type: 'confirm',
                title: `<h1 dir="auto">${title}</h1>`,
                body: `<p dir="auto">${body}</p><br/>`,
                buttonTextCancel,
                buttonTextConfirm,
                modalClasses: 'justify-between card',
                response: (r: boolean) => resolve(r)
            };
            modalStore.trigger(modal)
        })
    }

    const replaceSentence = async () => {
        const sentenceToReplace: SentenceInTaskDto = prepareDuplicatedSentence(sentence);
        let result = await new SentenceApiClient().createUpdateSentence(sentenceToReplace);
        const replacedSentence = {
            ...result,
            createdByUser: {
                firstname: $page?.data?.user?.firstname,
                lastname: $page?.data?.user?.lastname
            },
            updatedByUser: {
                firstname: $page?.data?.user?.firstname,
                lastname: $page?.data?.user?.lastname
            },
            createdAt: new Date(),
            updatedAt: new Date(),
            index: sentence.index,
            displayAsText: sentence.displayAsText
        };
        $CurrentEditableState.sentences = $CurrentEditableState.sentences.map((x) => x.id === sentence.id ? replacedSentence : x);
        sentence = replacedSentence;
        return result;
    };

    const handleResult = (result) => {
        if (result) {
            sentence.isNew = false;
            NotificationStore.push({
                type: NotificationType.success,
                message: $t('sentences.sentenceNotification.save.success')
            });
            resetSentenceCompareState();
            detectChanges();
            dispatch('saveSentence')
        } else if (!result && sentence.isFavorite) {
            NotificationStore.push({
                type: NotificationType.success,
                message: $t('sentences.sentenceNotification.save.cancel')
            });
        } else {
            return;
        }
    }


    const rollbackChanges = () => {
        sentence = {
            ...initialState,
            translations: initialState.translations.map((translation) => ({...translation}))
        };

        setCurrentTranslationValue();
        detectChanges();
    };

    const onAudioChanges = () => {
        resetSentenceAudioCompareState();
        detectChanges();
    };

    const onAudioCanceled = () => {
        sentence.audioUrl = '';
        detectChanges();
    };

    const prepareDuplicatedSentence = (sentence: SentenceInTaskDto): SentenceDto => {
        const {level, sex, value, translations} = sentence;
        return {
            id: generateGuid(),
            level: level,
            sex: sex,
            value: value,
            translations: translations?.filter(x => x.lang === selectedLang).map((x) => {
                return {
                    id: generateGuid(),
                    lang: x.lang,
                    value: x.value
                };
            }),
            isFavorite: false,
        };
    };

    const save = async () => {
        const client = new SentenceApiClient();
        let result;
        sentence = _.omit(sentence, ['audioRecordedButNotYetSaved']) as SentenceInTaskDto;

        if (isTaskMode && !sentence.isNew) {
            await new Promise<'create' | 'saveThis' | 'cancel'>((resolve) => {
                modalStore.trigger({
                    type: 'component',
                    component: 'saveSentenceModal',
                    response: (r: 'create' | 'saveThis' | 'cancel') => {
                        resolve(r)
                    },
                    meta: {role: role, isFavorite: sentence.isFavorite}
                });
            }).then(async (r: 'create' | 'saveThis' | 'cancel') => {
                if (r === 'create') {
                    result = await replaceSentence();
                } else if (r === 'saveThis') {
                    result = await client.createUpdateSentence(sentence);
                } else {
                    return;
                }
            });
        } else {
            result = await client.createUpdateSentence(sentence);
        }
        handleResult(result);
    }
</script>


<div class="{uniqClass}  flex gap-2  card  py-4 px-5 variant-glass-primary align-middle">
    <div class="flex flex-col gap-2 w-full">
        <div class="flex gap-10">
            <div class="flex flex-col flex-1">
                <div>
                    <He useLocalState={isTaskMode}>
                        <BaseInput
                                disabled={disableSentence}
                                className=""
                                name={'value'}
                                bind:value={sentence.value}
                                on:input={detectChanges}
                        />
                    </He>
                </div>
                <div class="pt-4">
                    <AudioControls
                            {id}
                            {sentence}
                            disabled={disableSentence}
                            on:audioUploaded={onAudioChanges}
                            on:audioCanceled={onAudioCanceled}
                            bind:audioUrl={sentence.audioUrl}
                            bind:audioRecordedButNotYetSaved={sentence.audioRecordedButNotYetSaved}
                    />
                </div>
            </div>
            <div class="flex flex-col items-center gap-3">
                <BaseSelect
                        on:change={changeLanguage}
                        disabled={isTaskMode || disableSentence}
                        name="lang"
                        options={mapEnumToOptions(LanguageFilter)}
                        bind:value={selectedLang}
                />
                <BaseSelect
                        name="level"
                        disabled={disableSentence}
                        options={mapEnumToOptions(LevelFilter)}
                        bind:value={sentence.level}
                        on:change={detectChanges}
                />

            </div>
            <div class="flex flex-col flex-1">
                <div class="{!currentTranslationValue && isTaskMode ? 'pulse-ping-animation':''}">
                    <BaseInput
                            disabled={disableSentence}
                            name={'translation'}
                            bind:value={currentTranslationValue}
                            dir="ltr"
                            on:input={onTranslationChanged}
                    />
                </div>
                <div class="flex justify-between items-center pt-4">
                    <div class="flex items-center gap-4">
                        <BaseButton disabled={!canBeSaved} on:click={save}>
                            <IconDeviceFloppy/>
                        </BaseButton>
                        <BaseButton disabled={!canBeRollBack} on:click={rollbackChanges}>
                            <IconArrowBackUp/>
                        </BaseButton>
                    </div>
                    <div>
                        {#if sentence.translations}
                            {#each sentence.translations as translation}
                                {#if translation?.value?.length > 0}
                                    <div class="badge variant-filled-tertiary mx-1">{translation.lang}</div>
                                {/if}
                            {/each}
                        {/if}
                    </div>
                    <div>
                        {#if isTaskMode && !!sentence.audioUrl}
                            {#if displayAsText}
                                <BaseButton disabled={disableSentence} on:click={changeDisplayMode}>
                                    <IconLetterT/>
                                </BaseButton>
                            {:else}
                                <BaseButton disabled={disableSentence} on:click={changeDisplayMode}>
                                    <IconHeadphones/>
                                </BaseButton>
                            {/if}
                        {/if}
                        {#if sentence.sex === 'f'}
                            <BaseButton disabled={disableSentence} className="variant-filled-error"
                                        on:click={changeGender}>
                                <IconWoman/>
                            </BaseButton>
                        {:else}
                            <BaseButton disabled={disableSentence} className="variant-filled-primary"
                                        on:click={changeGender}>
                                <IconMan/>
                            </BaseButton>
                        {/if}
                    </div>

                </div>
            </div>
        </div>
        <SentenceDescription sentence={sentence}/>
    </div>
    <AdminButtons isFavorite={sentence.isFavorite} isNew={sentence.isNew} changeFavorite={changeFavorite}
                  deleteSentence={deleteSentence}/>
</div>

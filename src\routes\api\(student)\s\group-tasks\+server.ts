import {wrapFunc} from '$api/core/misc/response-wrapper';
import {
    getAllTasksByStudentId,
    getStudentTasksByGroupId,
} from '$api/core/services/task.service';
import type {RequestEvent} from '@sveltejs/kit';
import {paramsToKeyValue} from "$api/core/utils";

export const GET = async ({locals, url}: RequestEvent): Promise<Response> =>
    wrapFunc(async () => {
        const {groupId} = paramsToKeyValue(url.searchParams);
        if (groupId || locals.user.currentGroup) {
            return await getStudentTasksByGroupId(groupId || locals.user.currentGroup, locals.user.id)
        }

        return await getAllTasksByStudentId(locals.user.id)
    });

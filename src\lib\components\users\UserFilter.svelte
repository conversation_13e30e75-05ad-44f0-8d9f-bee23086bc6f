<script lang="ts">
	import BaseInput from '../common/BaseInput.svelte';
	import { get } from 'svelte/store';
	import { debounce } from 'lodash';
	import { t } from '$lib/i18n/config';
	import {UserFilterState} from "$lib/state/user-filter-state";

	let inputValue: string;
	const onInput = debounce(
		() => UserFilterState.set({ ...get(UserFilterState), search: inputValue }),
		1000
	);
</script>

<div class="flex">
	<BaseInput
		on:input={onInput}
		name="search"
		placeHolder={$t('users.placeHolderSearch')}
		bind:value={inputValue}
	/>
</div>

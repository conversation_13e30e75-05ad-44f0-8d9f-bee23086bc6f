<script lang="ts">

    import TranslationMode from "$components/t/task-modes/TranslationMode.svelte";
    import {ResultPopupDetailsState} from "$lib/state/result-popup-details.state";
    import {durationToSecondsToHuman, getDateString, getTimeFromDateString} from "$lib/common/utils";
    import ResultBadge from "$components/t/ResultBadge.svelte";
    import InfoBadge from "$components/common/InfoBadge.svelte";

</script>

<div class="flex flex-col">
    <div class="flex-col p-2 my-5 gap-3 !rounded variant-glass-surface" dir="auto">
        <div>
            <p class="p-2 font-semibold">Name:
                <InfoBadge text={$ResultPopupDetailsState.name}/>
            </p>
        </div>
        {#if $ResultPopupDetailsState.whatsapp}
            <div>
                <p class="p-2 font-semibold">Whatsapp:
                    <InfoBadge text={$ResultPopupDetailsState.whatsapp}/>
                </p>
            </div>
        {/if}
        <div>
            <p class="p-2 font-semibold">Mode:
                <InfoBadge text={$ResultPopupDetailsState.mode}/>
            </p>
        </div>
        <div>
            <p class="p-2 font-semibold">Started at:
                <InfoBadge
                        text={`${getDateString($ResultPopupDetailsState.startedAt)} ${getTimeFromDateString($ResultPopupDetailsState.startedAt)}`}/>
            </p>
        </div>
        <div>
            <p class="p-2 font-semibold">Finished at:
                <InfoBadge
                        text={`${getDateString($ResultPopupDetailsState.finishedAt)} ${getTimeFromDateString($ResultPopupDetailsState.finishedAt)}`}/>
            </p>
        </div>
        <div>
            <p class="p-2 font-semibold">Spent time:
                <InfoBadge text={durationToSecondsToHuman($ResultPopupDetailsState.spentTime)}/>
            </p>
        </div>
        <div>
            <p class="p-2 font-semibold">Score:
                <ResultBadge result={$ResultPopupDetailsState.scorePercent} size="md"/>
            </p>
        </div>
    </div>
    <TranslationMode model={$ResultPopupDetailsState} hintsEnabled={false}/>
</div>

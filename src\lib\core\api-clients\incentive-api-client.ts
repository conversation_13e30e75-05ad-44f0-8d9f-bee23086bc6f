import {BaseApiClient} from '$lib/core/api-clients/base-api-client';
import {IncentiveState} from '$lib/state/incentive-state';
import {get} from 'svelte/store';
import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import {IncentivePagingState} from '$lib/state/incentive-paging-state';
import axios from "axios";

export class IncentiveApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public deleteIncentiveContent = async (id: string): Promise<IncentiveContentDto> =>
        await this.deleteOrThrow(`/api/incentive?id=${id}`);

    public createIncentiveContent = async (
        content: IncentiveContentDto
    ): Promise<IncentiveContentDto> =>
        await this.postDataOrThrow<IncentiveContentDto>('/api/incentive', content);

    public updateIncentiveContent = async (
        content: IncentiveContentDto
    ): Promise<IncentiveContentDto> =>
        await this.putDataOrThrow<IncentiveContentDto>('/api/incentive', content);



    public getIncentiveContent = async (
        getAllIncentiveContent?: boolean
    ): Promise<TableDataDto<IncentiveContentDto>> => {
        const {search} = get(IncentiveState);
        let {take, skip} = get(IncentivePagingState);
        if (getAllIncentiveContent) {
            take = -1;
            skip = 0;
        }

        return await this.getDataOrThrow(`/api/incentive?search=${search}&take=${take}&skip=${skip}`);
    };

    public getIncentiveContentById = async (id: string): Promise<IncentiveContentDto> =>
        await this.getDataOrThrow(`/api/incentive?id=${id}`);
}

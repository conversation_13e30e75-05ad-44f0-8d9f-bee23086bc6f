import type { Duration } from 'date-fns';

export interface StudentDto {
	id: string;
	tz: string;
	email: string;
	firstname: string;
	lastname: string;
	middlename: string;
	dob: Date;
	phone: string;
	whatsApp: string;
	currentGroup: string;
	comment: string;
	teacherComment: string;
	document1Url: string;
	document2Url: string;
	city: string;
	teudatOleUrl: string;
	photoUrl: string;
}

export interface ExtendedStudentDto extends StudentDto {
	registrationDate: Date;
	currentGroupStartDate: Date;
	lastTaskScore: number;
	lastTaskDelay: Duration;
	averageTaskScore: number;
	averageTaskDelay: Duration;
}

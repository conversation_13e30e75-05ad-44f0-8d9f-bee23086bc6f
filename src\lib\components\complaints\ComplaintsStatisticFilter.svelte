<script lang="ts">
    import BaseSelect from "$components/common/BaseSelect.svelte";
    import {ComplaintsStatisticFilter} from "$lib/state/complaints-statistic-state";
    import {t} from "$lib/i18n/config";

    export let updaters;

    $: latestUpdaterOptions = [
        {displayValue: 'none', value: ''},
        ...updaters.reduce((acc, c) => {
            const existItem = acc.find((el) => el.value === c?.task_sentences?.sentence?.updatedByUser?.id);
            if (!existItem && c?.task_sentences?.sentence?.updatedByUser) {
                acc.push({
                    displayValue: `${c.task_sentences.sentence.updatedByUser.firstname} ${c.task_sentences.sentence.updatedByUser.lastname}`,
                    value: c.task_sentences.sentence.updatedByUser.id
                })
            }
            return acc
        }, [])
    ];

</script>


<div class=" card mt-6 p-5 w-full text-token flex justify-between items-center variant-glass-primary">
    <div class="flex flex-row gap-10 items-baseline">
        <div>
            <BaseSelect title={$t('complaints.statistic.filter.titleUpdate')}
                        bind:value={$ComplaintsStatisticFilter.latestUpdater}
                        options={latestUpdaterOptions}
            />
        </div>
    </div>
</div>
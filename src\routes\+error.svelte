<script lang="ts">
	import { page } from '$app/stores';
	import { t } from '$lib/i18n/config';
	import { IconArrowBackUp } from '@tabler/icons-svelte';
</script>

<svelte:head>
	<link rel="stylesheet" href="https://unpkg.com/keyboard-css@1.2.4/dist/css/main.min.css" />
</svelte:head>

<div dir="auto" class="w-full h-full flex flex-col items-center justify-center mt-20">
	<h1 class="text-xl font-bold cmyk">{$t('common.errorTitle')}</h1>
	<p class="mt-7 text-4xl cmyk">{$page?.error?.message ?? 'error •͡˘㇁•͡˘'}</p>
	<button on:click={() => history.back()} class="mt-10 px-16 kbc-button kbc-button-lg">
		<IconArrowBackUp size="90" />
	</button>
</div>

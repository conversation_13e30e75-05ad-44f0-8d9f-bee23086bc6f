<script lang="ts">
    import {t} from "$lib/i18n/config";
    import {format} from "date-fns";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconEdit, IconSend} from "@tabler/icons-svelte";
    import ResultBadge from "$components/t/ResultBadge.svelte";
    import {durationToMinutesToHuman} from "$lib/common/utils";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {createEventDispatcher} from "svelte";
    import {goto} from "$app/navigation";
    import {page} from "$app/stores";
    import GroupHistoryTableRow from "$components/groups/GroupHistoryTableRow.svelte";


    export let groupHistory;

    export let groups;

    const dispatchModal = createEventDispatcher();

    export let generalHolidays;


    $:tableTitles = [
        $t('groups.groupHistory.table.name'),
        $t('groups.groupHistory.table.group'),
        $t('students.students.table.head.regDate'),
        $t('groups.groupHistory.table.start'),
        $t('groups.groupHistory.table.end'),
        $t('groups.groupHistory.table.taskStart'),
        $t('groups.groupHistory.table.comment'),
        $t('groups.groupHistory.table.lastTaskScore'),
        $t('groups.groupHistory.table.averageTaskScore'),
        $t('groups.groupHistory.table.lastTaskDelay'),
        $t('groups.groupHistory.table.averageTaskDelay'),
        $t('groups.groupHistory.table.whatsapp'),
        $t('groups.groupHistory.table.statistics'),
        $t('groups.groupHistory.table.action')
    ]


</script>

<div class="table-container mt-5">
    <table class="table table-hover !overflow-auto table-compact ">
        <thead>
        <tr>
            {#each tableTitles as title}
                {#if title === $t('groups.groupHistory.table.statistics')}
                    <OnlyForRole>
                        <th class="text-right">{title}</th>
                    </OnlyForRole>
                {:else}
                    <th class="text-right">{title}</th>
                {/if}
            {/each}
        </tr>
        </thead>
        <tbody>
        {#each groupHistory as history}
            {@const groupId = groups.find((group) => group.id === history.student.currentGroup)?.id}
            <GroupHistoryTableRow {groupId} {history}
                                  {generalHolidays}
                                  on:triggerSendMessageModal={(e)=>dispatchModal('triggerSendMessageModal',{...e.detail})}
                                  on:triggerEditModal={(e)=>dispatchModal('triggerEditModal',{...e.detail})}/>
        {/each}
        </tbody>
    </table>
</div>
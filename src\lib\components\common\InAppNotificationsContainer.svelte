<script lang="ts">
import InAppNotification from "$lib/components/common/InAppNotification.svelte";
import NotificationState from "$lib/state/notification-state.js";

</script>

<div class="relative z-[999]">
    <div class="fixed bottom-0 left-0 right-0 mx-auto mb-4 space-y-4 w-full max-w-md">
        {#each $NotificationState as n}
            <InAppNotification notification="{n.data}"/>
        {/each}
    </div>
</div>

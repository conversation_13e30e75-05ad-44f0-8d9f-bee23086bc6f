<script lang="ts">
    import {t} from '$lib/i18n/config';
    import {IconHourglassHigh, IconCircleCheck} from '@tabler/icons-svelte';
    import {getTotalEnabledTaskModesCount, getTaskModesDone} from "$lib/common/task-helpers";
    import ResultBadge from "$components/t/ResultBadge.svelte";
    import {format} from "date-fns";

    export let task;

</script>

<div class="w-full card variant-glass-primary p-4 flex justify-between gap-2" dir="ltr">
    <div class="flex flex-col gap-5">
        <div>
            <p class="text-lg font-bold">
                {$t(`studentTasks.taskItem.${task?.type}`)} {$t(`studentTasks.taskItem.from`)} {format(new Date(task?.date), 'dd.MM.yyyy')}
            </p>
        </div>
        <div>
            <p>{task?.commentPublic}</p>
        </div>
    </div>
    <div class="flex gap-2 card px-3 border-surface-200-700-token border-[1px] !w-min-[300px]">
        <div class="flex flex-row justify-around items-center md:gap-4">

            {#if !task?.results?.length > 0}
                <p class="flex h-full justify-center items-center">
                    <IconHourglassHigh class="text-yellow-600"/>
                </p>
            {:else}
                <div class="flex flex-col h-fit items-center justify-center gap-1 text-center">
                    <div class="flex gap-1 order-last">
                        {`${getTotalEnabledTaskModesCount(task) > 1 ? `${getTaskModesDone(task?.results?.at(0)?.results)?.length ?? 0}/${getTotalEnabledTaskModesCount(task)}` : ''}`}
                        <IconCircleCheck class="text-green-700 mx-1"/>
                    </div>
                    <ResultBadge result={task?.results?.[0]?.currentScore}/>
                </div>
            {/if}
        </div>
    </div>
</div>

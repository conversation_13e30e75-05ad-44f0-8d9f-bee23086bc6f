import { wrapFunc } from '$api/core/misc/response-wrapper';
import type { RequestEvent } from '@sveltejs/kit';
import { paramsToKeyValue, toBoolean } from '$api/core/utils';
import {
	createUpdateSentence,
	deleteSentence,
	getSentences
} from '$api/core/services/sentence.service';
import { mapper } from '$common/core/mapper';
import type { SentenceFilterDto } from '$common/models/filters/sentence-filter.dto';


export const GET = async (event: RequestEvent) =>
	wrapFunc(async () => {
		const { take, skip, audio, level, search, isStrict, lang, isFavorite, updatedBy } =
			paramsToKeyValue(event.url.searchParams);

		return await getSentences(mapper<SentenceFilterDto, unknown>({
			take: +take,
			skip: +skip,
			audio: +audio,
			level: +level,
			updatedBy,
			search,
			lang,
			isStrict: toBoolean(isStrict),
			isFavorite: toBoolean(isFavorite)
		}))
	}, event);

export const POST = async (event: RequestEvent) =>
	wrapFunc(async () => {
		const sentence = await event.request.json();

		return await createUpdateSentence(sentence, event?.locals?.user?.id);
	}, event);

export const DELETE = async ({ url }: RequestEvent) =>
	wrapFunc(async () => {
		const { id } = paramsToKeyValue(url.searchParams);
		return await deleteSentence(id);
	});

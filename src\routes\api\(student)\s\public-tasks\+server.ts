import { wrapFunc } from '$api/core/misc/response-wrapper';
import { getPublicTaskById } from '$api/core/services/task.service';
import type { RequestEvent } from '@sveltejs/kit';
import {paramsToKeyValue} from "$api/core/utils";

export const GET = async ({ url }: RequestEvent): Promise<Response> =>
	wrapFunc(async () => {
		const { id } = paramsToKeyValue(url.searchParams);

		if (id) {
			return await getPublicTaskById(id);
		}

		return {};
	});

<script lang="ts">
    import {
        getTotalEnabledTaskModesCount,
        getTaskModesDone,
        getBestTranslationResult
    } from '$lib/common/task-helpers';
    import {t} from '$lib/i18n/config';
    import {format} from 'date-fns';
    import type {CompletionTaskDto} from '$common/models/dtos/task.dto';
    import ResultBadge from '$components/t/ResultBadge.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconEye, IconSend} from '@tabler/icons-svelte';
    import {createEventDispatcher} from 'svelte';
    import {TaskMode} from '$common/models/enums';
    import {ResultPopupDetailsState} from '$lib/state/result-popup-details.state';
    import {getModalStore} from '@skeletonlabs/skeleton';

    export let type: 'class' | 'home';
    export let date: string;
    export let results;
    export let calculateDoneIn: (result: CompletionTaskDto, mode?: string) => string;
    export let commentPublic: string;
    export let row;

    export let currentStudent;

    $: notOnlyTranslationMode = results?.results?.some((r) => r.mode !== 'translation');
    const dispatch = createEventDispatcher();
    const modalStore = getModalStore();

    $: countDone = calculateDoneIn(results);
    $: modeAttempts = countModesAttempts(results);

    const countModesAttempts = (result: CompletionTaskDto) => {
        return Object.values(TaskMode)
            .map((x) => results?.results?.filter((y) => y.mode == x)?.length ?? 0)
            .join('/');
    };

    const openDetailsPopup = (r) => {

        ResultPopupDetailsState.set({
            ...r,
            name: `${currentStudent?.firstname} ${currentStudent?.lastname}`,
            whatsapp: currentStudent?.whatsApp || ''
        });
        modalStore.trigger({type: 'component', component: 'resultDetailsPopup'});
    };

    $:nameTask = `${type === 'class' ? $t('t.class') : $t('t.home')} ${format(
        new Date(date),
        'dd.MM.yyyy'
    )}`;
</script>

<tr>
    <td>{nameTask} </td>
    <td>{commentPublic}</td>
    <td>{countDone}</td>
    {#if results}
        <td>
            <ResultBadge result={results?.currentScore}/>
        </td>
    {:else}
        <td/>
    {/if}
    <td>{modeAttempts || 0}</td>
    <td class="flex gap-2 break-all max-w-[230px]">
        <div class=" flex gap-2 justify-end">
            <BaseButton
                    className={!notOnlyTranslationMode ? 'invisible' : ''}
                    size="sm"
                    on:click={() => dispatch('changeAccordeonState')}
            >
                <svg
                        class="-mr-1 h-5 w-5 text-gray-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                >
                    <path
                            fill-rule="evenodd"
                            d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                            clip-rule="evenodd"
                    />
                </svg>
                {getTaskModesDone(results?.results).length}/{getTotalEnabledTaskModesCount(row)}
            </BaseButton>
            <BaseButton
                    className="w-1/2"
                    size="sm"
                    disabled={!results ||
					results?.results?.filter((x) => x.mode === TaskMode.translation).length === 0}
                    on:click={() => openDetailsPopup(getBestTranslationResult(results?.results))}
            >
                <IconEye size={20} stroke="1.5"/>
            </BaseButton>
            <BaseButton on:click={() => dispatch('openSendNotificationStudentModal')} size="sm">
                <IconSend size={20} stroke="1.5"/>
            </BaseButton>
        </div>
    </td>
</tr>

<script>
    import BaseButton from '$components/common/BaseButton.svelte';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import BaseInput from '$components/common/BaseInput.svelte';
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import BreadcrumbStudentGroupModal from '$components/students/BreadcrumbStudentGroupModal.svelte';
    import {t} from '$lib/i18n/config';
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";

    const modalStore = getModalStore();

    async function handleSubmit(event) {
        const data = new FormData(this);
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'success') {
            await invalidate('load:students');
            modalStore.close();
            NotificationStore.push({
                type: NotificationType.success,
                message: t.get('students.students.notifications.deleteStudentGroup.success')
            }, 5);
        }
    }
</script>

<div
        class="flex flex-col modal card rounded-xl sm:w-2/3 lg:w-[650px] p-5 shadow-xl space-y-4 "
>
    <BreadcrumbStudentGroupModal
            currentModalName={`${$t('students.students.modal.deleteStudentGroupModal.delete')}
		${$t('students.students.modal.deleteStudentGroupModal.group')}`}
            groupName={$GroupHistoryState?.selectedGroup?.group?.name}
    />
    <form
            method="POST"
            action="?/deleteStudentGroup"
            on:submit|preventDefault={handleSubmit}
            class="flex flex-col items-center p-5 justify-between gap-10 w-full"
    >
        <label for="id" class="hidden">
            <BaseInput name="id" bind:value={$GroupHistoryState.selectedGroup.id}/>
        </label>

        <header class="text-2xl font-bold">
            {$t('students.students.modal.deleteStudentGroupModal.title')}
        </header>
        <div class="w-full flex justify-between">
            <div class="w-20">
                <BaseButton className="w-full" on:click={() => modalStore.close()}
                >{$t('students.students.modal.deleteStudentGroupModal.buttons.cancel')}</BaseButton
                >
            </div>
            <div class="w-20">
                <BaseButton className="w-full" type="submit"
                >{$t('students.students.modal.deleteStudentGroupModal.buttons.save')}</BaseButton
                >
            </div>
        </div>
    </form>
</div>

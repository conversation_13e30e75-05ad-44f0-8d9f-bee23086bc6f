<script lang="ts">
    import BaseInput from "$components/common/BaseInput.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconCircleLetterX, IconDeviceFloppy} from "@tabler/icons-svelte";
    import {getModalStore} from '@skeletonlabs/skeleton';
    import type {CreateUpdateGroupScheduleDto, GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
    import {
        daysScheduleToDbFormatString,
        DbFormatStringToDaysScheduleObject,
        formatToPickerDate, loadingWrap
    } from "$lib/common/utils";
    import DailySchedule from "$components/groups/DailySchedule.svelte";
    import {deserialize} from '$app/forms';
    import {mapper} from "$common/core/mapper";

    import {invalidate} from "$app/navigation";
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";
    import FormErrorMessage from "$components/common/FormErrorMessage.svelte";
    import _ from 'lodash';
    import {t} from "$lib/i18n/config";

    const modalStore = getModalStore()

    const data = $modalStore[0]?.meta;

    let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;


    let formData = {
        ...data,
        daysSchedule: DbFormatStringToDaysScheduleObject(data.daysSchedule),
        dateStart: formatToPickerDate(new Date(data?.dateStart || ''))
    };


    function handleDayChange(event: Event) {
        const target = event.target as HTMLInputElement;
        formData.daysSchedule[target?.name] = target?.checked ? 1 : 0;
    }

    async function handleSubmit(event) {
        const dataToSent = new FormData();
        let dataToParse;


        dataToParse = _.omit(mapper<CreateUpdateGroupScheduleDto, any>({
            ...formData,
            daysSchedule: daysScheduleToDbFormatString(formData.daysSchedule),
            dateStart: new Date(formData.dateStart),
            updatedElementIndex: data?.updatedElementIndex
        }), 'action');


        for (const key in dataToParse) {
            dataToSent.append(key, dataToParse[key]);
        }

        const response = await fetch(this.action, {
            method: 'POST',
            body: dataToSent,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            modalErrors = result.data.errors;
        } else {
            await loadingWrap(async () => {
                await invalidate('load:groups');
                await invalidate('load:groups/id');
            });

            modalStore.close();

            if (formData?.action === 'create') {
                NotificationStore.push({
                    type: NotificationType.success,
                    message: $t(`groups.scheduleTable.notifications.create`)
                }, 5);
            } else {
                NotificationStore.push({
                    type: NotificationType.success,
                    message: $t(`groups.scheduleTable.notifications.update`)
                }, 5);
            }

        }
    }


</script>

<div class="modal card p-4 w-1/2 shadow-xl space-y-2">
    <h1 class="text-2xl font-bold m-3">{$t(`groups.scheduleTable.modal.title.${formData.action}`)}</h1>
    <form action={formData.action==='create'?'?/createSchedule':`?/updateSchedule`}
          on:submit|preventDefault={handleSubmit} class="modal-form">
        <div
                class="p-2 flex justify-around  gap-5"
        >
            <label for="id" class="hidden">
                <BaseInput name="id" value={formData.id}/>
            </label>

            <div class="flex w-[20%]">
                <label for="date" class="label">
                    <span class="title mb-1 font-medium text-base without-ampm">{$t(`groups.scheduleTable.modal.formFields.date`)}</span>
                    <input dir="rtl" class="input h-[43px] rounded px-[5px]" type="date"
                           bind:value={formData.dateStart}
                    >
                </label>
            </div>
            <div class="flex w-[20%]">
                <label for="days" class="flex flex-col ">
                    <span class="title mb-1 font-medium text-base without-ampm">{$t(`groups.scheduleTable.modal.formFields.days`)}</span>
                    <DailySchedule
                            Su={formData.daysSchedule.Su}
                            Mo={formData.daysSchedule.Mo}
                            Tu={formData.daysSchedule.Tu}
                            We={formData.daysSchedule.We}
                            Th={formData.daysSchedule.Th}
                            {handleDayChange}
                    />
                    <FormErrorMessage {modalErrors} fieldName="daysSchedule"/>
                </label>
            </div>

            <div class="flex  w-[20%]">
                <label for="hoursPerSession" class="flex flex-col ">
                    <span class="title mb-1 font-medium text-base without-ampm">{$t(`groups.scheduleTable.modal.formFields.hoursPerSession`)}</span>
                    <input name="hoursPerSession" bind:value={formData.hoursPerSession} type="number"
                           class="input h-[43px] rounded pr-[5px] "
                           min="1" max="10">
                    <FormErrorMessage {modalErrors} fieldName="hoursPerSession"/>

                </label>
            </div>

        </div>


        <div class="p-2 mt-5 flex justify-between">
            <BaseButton type="submit">
                <IconDeviceFloppy/>
                {$t(`groups.scheduleTable.modal.buttons.${formData.action}`)}

            </BaseButton>
            <BaseButton on:click={() => modalStore.close()}>
                <IconCircleLetterX/>
                {$t(`groups.scheduleTable.modal.buttons.cancel`)}
            </BaseButton>
        </div>
    </form>
</div>
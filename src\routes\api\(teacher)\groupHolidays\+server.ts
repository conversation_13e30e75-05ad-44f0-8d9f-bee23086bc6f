import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {createGroupHoliday, deleteGroupHoliday, getGroupHolidays} from "$api/core/services/groupHolidays.service";
import {paramsToKeyValue} from "$api/core/utils";
import {mapper} from "$common/core/mapper";
import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";

export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {groupId} = paramsToKeyValue(url.searchParams);
        return await getGroupHolidays(groupId);
    });



export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const groupHoliday = await event.request.json();
        const dto = mapper<GroupHolidayDto, unknown>({...groupHoliday, date: new Date(groupHoliday.date)})
        return await createGroupHoliday(dto)
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {groupHolidayId} = paramsToKeyValue(url.searchParams);
        return await deleteGroupHoliday(groupHolidayId);
    });

<script lang="ts">
    import {page} from "$app/stores";
    import {onMount} from "svelte";
    import {ComplaintApiClient} from "$lib/core/api-clients/complaint-api.client";
    import {goto} from "$app/navigation";
    import {ComplaintFilterState} from "$lib/state/complaint-filter-state";
    import {HandledComplaintFilter} from "$common/models/enums";
    import {modeCurrent} from "@skeletonlabs/skeleton";
    import BaseButton from '$components/common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import ContentTiny from "$components/common/ContentTiny.svelte";


    const firstname = $page?.data?.user?.firstname;
    const lastname = $page?.data?.user?.lastname;
    let countComplaints = 0;


    const handleClick = async () => {
        $ComplaintFilterState.latestUpdater = $page?.data?.user?.id;
        $ComplaintFilterState.isHandled = HandledComplaintFilter.notHandled;
        await goto('/complaints')
    }


    onMount(async () => {
        countComplaints = await new ComplaintApiClient().getTheNumberComplaintsByUserId($page?.data?.user?.id)
    })


</script>

<div class="flex w-full h-[90vmin] justify-center items-center text-2xl">
    <div class="morasha {$modeCurrent ? '' : 'invert'}"></div>
    {#if countComplaints > 0}
        <div class="flex flex-col items-center z-[999] gap-2">
            <p class="font-bold">{$t('home.hello')}, {firstname} {lastname}</p>
            <p class="font-bold text-error-600">
                {$t('home.warningMessage1')} {countComplaints} {$t('home.warningMessage2')}
            </p>
            <div class="w-full flex justify-center">
                <BaseButton on:click={handleClick} className="w-[30%]">
                    {$t('home.fix')}
                </BaseButton>
            </div>
        </div>
    {/if}
</div>



<style lang="scss">
  .morasha {
    background: url("/logo.png") no-repeat;
    background-size: contain;
    width: 24vmin;
    height: 25vmin;;
  }

  div > div.morasha {
    position: absolute;
    -webkit-animation: moveX 3.5s linear 0s infinite alternate, moveY 5s linear 0s infinite alternate;
    -moz-animation: moveX 3.5s linear 0s infinite alternate, moveY 5s linear 0s infinite alternate;
    -o-animation: moveX 3.5s linear 0s infinite alternate, moveY 5s linear 0s infinite alternate;
    animation: moveX 3.5s linear 0s infinite alternate, moveY 5s linear 0s infinite alternate;
  }

  @-webkit-keyframes moveX {
    from {
      left: 0;
    }
    to {
      left: calc(100% - 24vmin);
    }
  }

  @-moz-keyframes moveX {
    from {
      left: 0;
    }
    to {
      left: calc(100% - 24vmin);
    }
  }

  @-o-keyframes moveX {
    from {
      left: 0;
    }
    to {
      left: calc(100% - 24vmin);
    }
  }

  @keyframes moveX {
    from {
      left: 0;
    }
    to {
      left: calc(100% - 24vmin);
    }
  }

  @-webkit-keyframes moveY {
    from {
      top: 0;
    }
    to {
      top: calc(100% - 25vmin);
    }
  }

  @-moz-keyframes moveY {
    from {
      top: 0;
    }
    to {
      top: calc(100% - 25vmin)
    }
  }

  @-o-keyframes moveY {
    from {
      top: 0;
    }
    to {
      top: calc(100% - 25vmin)
    }
  }

  @keyframes moveY {
    from {
      top: 0;
    }
    to {
      top: calc(100% - 25vmin)
    }
  }
</style>
import type { PageLoad } from './$types';
import { SentenceApiClient } from '$lib/core/api-clients/sentence-api-client';
import {UserApiClient} from "$lib/core/api-clients/user-api-client";

export const ssr = false;

export const load: PageLoad = async ({ depends }) => {
	try {
		depends('load:sentences');
		return {
			sentences: new SentenceApiClient().getSentences(),
			users: new UserApiClient().getUsers()
		}
	} catch (error) {
		return {
			errorMessage: error
		};
	}
};
<script lang="ts">
    import {IconArrowsSort, IconSortDescending, IconSortAscending, IconPinned} from '@tabler/icons-svelte';
    import type {BaseSortingDto} from '$common/models/filters/base-filter.dto';
    import type {Writable} from 'svelte/store';
    import {t} from "$lib/i18n/config";
    import {invalidate} from "$app/navigation";


    export let header;
    export let state: Writable<BaseSortingDto>;

    const onSort = async () => {
        state.set({
            ...$state,
            sortBy: $state.sortBy === header.sortBy && $state.sortDir === 'desc' ? undefined : header.sortBy,
            sortDir: $state.sortBy !== header.sortBy ? 'asc' : 'desc'
        });
        await invalidate('load:groups/id');
    };
</script>

<div class=" flex flex-row items-center {header.sortBy ? 'cursor-pointer' : ''}" on:click={onSort}>
    {#if header.sortBy}
        {#if $state.sortBy === header.sortBy && $state.sortDir === 'asc'}
            <div class="mx-2">
                <IconSortAscending/>
            </div>
        {:else if $state.sortBy === header.sortBy && $state.sortDir === 'desc'}
            <div class="mx-2">
                <IconSortDescending/>
            </div>
        {:else}
            <div class="mx-2">
                <IconArrowsSort/>
            </div>
        {/if}
    {/if}
    <div class="flex flex-col justify-center items-center">
        {#if header.pinned}
            <div class="mb-2">
                <IconPinned/>
            </div>
        {/if}
        <div>{$t(header?.title)}</div>
    </div>

</div>

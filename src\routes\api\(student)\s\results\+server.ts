import {wrapFunc} from '$api/core/misc/response-wrapper';
import {createUpdateResult, getStudentResultsByTaskId} from '$api/core/services/task.service';
import type {RequestEvent} from '@sveltejs/kit';
import {paramsToKeyValue} from "$api/core/utils";
import {CompletionTaskDto} from "$common/models/dtos/task.dto";

export const GET = async ({locals, url}: RequestEvent): Promise<Response> =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);

        return await getStudentResultsByTaskId(id, locals.user.id);
    });

export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const data = await event.request.json() as { result: CompletionTaskDto, userId: string };
        return await createUpdateResult(data?.result, event?.locals?.user?.id || data.userId || null);
    });
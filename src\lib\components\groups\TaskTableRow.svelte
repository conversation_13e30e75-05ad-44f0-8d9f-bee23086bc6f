<script lang="ts">
    import {intlFormat} from 'date-fns';
    import {t} from '$lib/i18n/config.js';
    import {
        IconCopy,
        IconDeviceDesktopShare,
        IconDeviceDesktopQuestion,
        IconEdit,
        IconInfoCircle
    } from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher, onMount} from 'svelte';
    import type {ExtendedTaskDto} from '$common/models/dtos/task.dto';
    import {goto} from '$app/navigation';
    import TaskTypeBadge from "$components/common/TaskTypeBadge.svelte";
    import TaskModes from "$components/tasks/TaskModes.svelte";
    import {browser} from "$app/environment";
    import InfoBadge from "$components/common/InfoBadge.svelte";
    import type {PopupSettings} from "@skeletonlabs/skeleton";
    import {popup} from '@skeletonlabs/skeleton';


    const dispatch = createEventDispatcher();

    export let row: ExtendedTaskDto;
    export let studentsCount: number;
    export let groupName: string;
    export let isPublic: boolean;
    export let isTaskPage = false;

    let isOpenDropDown = false;

    let menu = null;


    function createToolTipHover(index) {
        return {
            event: 'click',
            target: `item-${index}`,
            placement: 'top'
        };
    }

    onMount(() => {
        if (browser) {
            const handleOutsideClick = (event) => {
                if (isOpenDropDown && !menu.contains(event.target)) {
                    isOpenDropDown = false;
                }
            };

            const handleEscape = (event) => {
                if (isOpenDropDown && event.key === 'Escape') {
                    isOpenDropDown = false;
                }
            };

            document.addEventListener('click', handleOutsideClick, false);
            document.addEventListener('keyup', handleEscape, false);

            return () => {
                document.removeEventListener('click', handleOutsideClick, false);
                document.removeEventListener('keyup', handleEscape, false);
            };
        }
    });

    const handleClickOnDropDown = (dispatchName: string) => {
        dispatch(dispatchName);
        isOpenDropDown = false;
    };

    const goToGroup = async () => {
        await goto(`/groups/${row.groupId}`);
    };
</script>

<tr class={row.isPublic ? '!bg-surface-300-600-token' : ''}>
    <td class="flex gap-2">
        <div bind:this={menu} dir="ltr" class="relative">
            <button
                    type="button"
                    class="flex items-center variant-filled h-full"
                    id="menu-button"
                    aria-expanded="true"
                    aria-haspopup="true"
            >
                <BaseButton class="flex-1 p-0 pl-0 w-fit" on:click={() => handleClickOnDropDown('copy')}>
                    <IconCopy size={20} stroke="1.5"/>
                </BaseButton>
                <div class="flex-1 p-1 pr-2" on:click={() => (isOpenDropDown = !isOpenDropDown)}>
                    <svg
                            class="-mr-1 h-5 w-5 text-gray-400"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                    >
                        <path
                                fill-rule="evenodd"
                                d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                                clip-rule="evenodd"
                        />
                    </svg>
                </div>
            </button>
            {#if isOpenDropDown}
                <div
                        class="fade-in absolute bg-surface-50-900-token right-0 bottom-8 mt-2 w-36 z-50 list-outside origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="menu-button"
                        tabindex="-1"
                >
                    <div dir="rtl" role="none">
						<span
                                on:click={() => handleClickOnDropDown('copy')}
                                class="text-surface-900-50-token text-start block px-4 py-2 text-sm cursor-pointer hover:bg-primary-hover-token"
                                role="menuitem"
                                tabindex="-1"
                                id="menu-item-0"
                        >
							{$t('students.students.table.copyButton')}
						</span>
                        <hr/>
                        <span
                                on:click={() => handleClickOnDropDown('duplicate')}
                                class="text-surface-900-50-token text-start block px-4 py-2 text-sm cursor-pointer hover:bg-primary-hover-token"
                                role="menuitem"
                                tabindex="-1"
                                id="menu-item-1"
                        >
							{$t('students.students.table.duplicateButton')}
						</span>
                    </div>
                </div>
            {/if}
        </div>

        <BaseButton
                disabled={!row.sentences.length > 0}
                on:click={() => {
				dispatch('openPopUp', {
					lang: row.lang,
					random: false,
					taskFont: row.hebrewFont,
					sentences: row.sentences
				});
			}}
        >
            <IconDeviceDesktopShare size={20} stroke="1.5"/>
        </BaseButton>
        <BaseButton
                className="bg-secondary text-black border-[1px] border-accent"
                disabled={!row.sentences.length > 0}
                on:click={() => {
				dispatch('openPopUp', {
					lang: row.lang,
					random: true,
					taskFont: row.hebrewFont,
					sentences: row.sentences
				});
			}}
        >
            <IconDeviceDesktopQuestion size={20} stroke="1.5"/>
        </BaseButton>
        <button use:popup={createToolTipHover(row.id)}
                class="btn btn-md variant-filled bg-secondary gap-2 text-black border-[1px] border-accent">
            <IconInfoCircle size={20} stroke="1.5"/>
        </button>
        <div class="card variant-filled-primary w-fit max-w-[300px] h-fit max-h-[250px]" data-popup="item-{row.id}">
            <div class="flex flex-col max-h-[250px] overflow-hidden overflow-y-auto p-2">
                {#each row.sentences as sentence,index}
                    <div class="flex gap-1 h-fit">
                        <p>{index+1}.</p>
                        <p>{sentence.value}</p>
                    </div>

                {/each}
            </div>
            <div class="arrow variant-filled-primary"/>
        </div>
    </td>
    <td
    >{intlFormat(new Date(row.date), {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    })}</td
    >
    <td>
        <TaskTypeBadge isActive={row.isActive} type={!isPublic ? row.type : 'sandbox'}/>
    </td>
    <td on:click={async () => await goToGroup()}>
        <div class="cursor-pointer w-full flex font-medium !p-1 !m-0 text-blue-600 dark:text-blue-400 hover:underline badge text-sm btn btn-ghost justify-start">
            {groupName}
        </div>
    </td>
    <td class="">
        {#if row.createdBy?.length > 1}
            <div class="inline-flex">
                <InfoBadge text={row.createdBy}></InfoBadge>
            </div>
        {/if}
        <div class="inline-flex">
            {row.commentPublic}
        </div>

    </td>
    <td class="flex gap-1">
        <TaskModes mode="translation"/>
        {#each row.additionalTasks as additional}
            {#if additional.enabled}
                <TaskModes mode={additional.mode}/>
            {/if}
        {/each}
    </td>
    <td>
        <BaseButton size="sm" on:click={() => dispatch('goEditTask')}>
            {$t('students.students.table.editButton')}
            <IconEdit size={20} stroke="1.5"/>
        </BaseButton>
    </td>
    {#if !isTaskPage}
        <td class="flex gap-2 break-all max-w-[230px] ">
            <div class=" flex gap-2 justify-end" dir="auto">
                <BaseButton
                        disabled={studentsCount === 0 && !isPublic}
                        size="sm"
                        on:click={() => dispatch('changeAccordeonState')}
                >
                    <svg
                            class="-mr-1 h-5 w-5 text-gray-400"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                            aria-hidden="true"
                    >
                        <path
                                fill-rule="evenodd"
                                d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                                clip-rule="evenodd"
                        />
                    </svg>
                    {row?.results?.length}/{isPublic ? '?' : studentsCount}
                </BaseButton>
            </div>
        </td>
    {/if}

</tr>

<style>
    td {
        vertical-align: middle !important;
    }
</style>

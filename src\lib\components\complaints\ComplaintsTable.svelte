<script lang="ts">
    import {t} from "$lib/i18n/config.js";
    import BaseButton from "$components/common/BaseButton.svelte";
    import Sentence from "$components/sentences/Sentence.svelte";
    import InfoBadge from "$components/common/InfoBadge.svelte";
    import type {ComplaintDto} from "$common/models/dtos/complaint.dto.js";
    import {IconCircleCheckFilled} from '@tabler/icons-svelte';
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";
    import {page} from "$app/stores";


    export let complaints: ComplaintDto[];

    export let loadMoreFunc;


    let accordeonState = complaints.map((x) => {
        return false;
    });

    $: tableTitles = [
        $t('complaints.table.titles.sentence'),
        $t('complaints.table.titles.description'),
        $t('complaints.table.titles.task'),
        $t('complaints.table.titles.complaints'),
        $t('complaints.table.titles.latestUpdater'),
        $t('complaints.table.titles.actions')
    ];


    export let handleComplaint: (taskId: string, sentenceId: string, isHandled: boolean) => void;

    const userRole = $page?.data?.user?.role;

</script>


<div class="h-full ">
    <table dir='rtl' class="table table-hover table-compact h-fit overflow-auto">
        <thead on:keypress>
        {#each tableTitles as title}
            <th class="text-start">{title}</th>
        {/each}
        </thead>
        <tbody class="!flex-1 overflow-auto">
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each complaints as complaint,index (complaint.id)}
                <tr>
                    <td class="w-[30%]">{complaint.task_sentences.sentence.value}</td>
                    <td class="w-[40%] text-end">
                        {#each complaint.complaints as c,index}
                            <p class="mt-1">
                                <span class="font-bold text-base">
                                    {#if userRole === 'admin'}
                                        {c.name}:
                                        {:else}
                                        {$t('complaints.table.student')} {index + 1}:
                                    {/if}
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-300">{c.comment}</span>
                            </p>
                        {/each}
                    </td>
                    <td class="w-[10%]">
                        <InfoBadge url="/tasks/{complaint?.taskId}" target="_blank" text="task"/>
                    </td>
                    <td class="w-[10%] text-center"><span class="font-bold text-lg">{complaint?.count}</span></td>
                    <td class="w-[10%]">
                        {#if complaint.task_sentences.sentence.updatedByUser}
                            <p>{complaint.task_sentences.sentence.updatedByUser.firstname} {complaint.task_sentences.sentence.updatedByUser.lastname}</p>
                        {:else}
                            -
                        {/if}
                    </td>
                    <td class="w-[10%] flex gap-x-3">
                        <BaseButton size="sm" on:click={()=>{
                            accordeonState[index] = !accordeonState[index]
                        }}>
                            {$t('complaints.editButton')}
                            <svg
                                    class="-mr-1 h-5 w-5 text-gray-400"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                    aria-hidden="true"
                            >
                                <path
                                        fill-rule="evenodd"
                                        d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                                        clip-rule="evenodd"
                                />
                            </svg>
                        </BaseButton>
                        {#if !complaint.isHandled}
                            <BaseButton
                                    on:click={()=>handleComplaint(complaint.taskId,complaint.sentenceId,complaint.isHandled)}
                                    size="sm">
                                {$t('complaints.handledButton')}
                                <IconCircleCheckFilled/>
                            </BaseButton>
                        {/if}
                    </td>
                </tr>
                <tr>
                    {#if accordeonState[index]}
                        <td colspan="5">
                            <table class="table">
                                <div class="">
                                    <Sentence
                                            id={complaint.task_sentences.sentence.id}
                                            bind:sentence={complaint.task_sentences.sentence}
                                            isComplaintsMode={true}
                                            selectedLang={complaint.task_sentences.task.lang}
                                            displayAsText={complaint.task_sentences.defaultTaskView}
                                            on:saveSentence={()=>handleComplaint(complaint.taskId,complaint.sentenceId,complaint.isHandled)}
                                    />
                                </div>

                            </table>
                        </td>
                    {/if}
                </tr>
            {/each}
        </InfiniteTableScrollContainer>
        </tbody>
    </table>
</div>

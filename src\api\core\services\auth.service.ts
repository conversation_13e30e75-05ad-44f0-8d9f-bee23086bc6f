import {db} from '../service-clients/db';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import {Routes} from '$common/core/routes';
import type {RequestEvent} from '@sveltejs/kit';
import {redis} from '../service-clients/redis';
import {Constants} from '../constants';
import {config} from '../config';
import type {UserDto} from '$common/models/dtos/user.dto';
import {mapper} from '$common/core/mapper';
import type {StudentDto} from '$common/models/dtos/student.dto';

export const loginForTeacher = async (username: string, password: string, event: RequestEvent) => {
    const user = await db.users.findFirst({where: {tz: username}, include: {permissions: true}});

    if (!user || !(await bcrypt.compare(password, user.password))) {
        return {error: 'Invalid credentials'};
    }

    if (user.role === Constants.DisabledRole) {
        return {error: 'sign-in.formFieldsErrors.disabledUser'};
    }

    const token = jwt.sign(composeTeacherAccessToken(user), config.auth.JWT_SECRET, {
        expiresIn: config.auth.JWT_ACCESS_TOKEN_LIFETIME + 'm'
    });

    await setTokens(token, user.id, event);

    return {success: true};
};

export const loginForStudent = async (username: string, password: string, event: RequestEvent) => {
    try {
        const student = await db.students.findFirst({where: {tz: username}});

        const split = password.split('-');
        const mapSplit = split.map(x => +x);
        const dobUser = new Date(mapSplit[2], mapSplit[1] - 1, mapSplit[0]);

        if (!student || !compareDob(student.dob, dobUser)) {
            return {error: {message: 'sign-in.formFieldsErrors.invalidCreds', known: true}};
        }


        const token = jwt.sign(
            composeStudentAccessToken(mapper<StudentDto, unknown>(student)),
            config.auth.JWT_SECRET,
            {
                expiresIn: config.auth.JWT_ACCESS_TOKEN_LIFETIME + 'm'
            }
        );
        await setTokens(token, student.id, event);

        return {success: true};

    } catch (e) {
        console.error(e);
        return {error: 'Unhandled login error'};
    }
};

const compareDob = (dobFromDb: Date, dobFromUser: Date): boolean =>
    dobFromDb.getDay() === dobFromUser.getDay() &&
    dobFromDb.getMonth() === dobFromUser.getMonth() &&
    dobFromDb.getFullYear() === dobFromUser.getFullYear();

export const setRefreshToken = async (id: string) => {
    if (!redis.isOpen) {
        await redis.connect();
    }
    await redis.set(id, 'refresh');
};

const setTokens = async (token: string, userId: string, event: RequestEvent) => {
    setAccessToken(token, event);
    await setRefreshToken(userId);
};

export const setAccessToken = (token: string, event: RequestEvent): void => {
    event.cookies.set(Constants.AuthCookieName, `Bearer ${token}`, {
        httpOnly: true,
        path: '/',
        secure: true,
        sameSite: 'none',
        maxAge: Number.parseInt(config.auth.JWT_REFRESH_TOKEN_LIFETIME) * 60
    });
};

const composeTeacherAccessToken = (user: UserDto) => {
    const {id, email, firstname, lastname, tz, role, permissions} = user;

    return {id, email, firstname, lastname, tz, role, permissions};
};

const composeStudentAccessToken = (student: StudentDto) => {
    const {id, email, firstname, lastname, tz, currentGroup} = student;
    
    return {id, email, firstname, lastname, tz, currentGroup, whatsApp: student?.whatsapp, role: Constants.StudentRole};
};

export const refreshTeacherToken = async (userId: string, event: RequestEvent) => {
    if (!redis.isOpen) {
        await redis.connect();
    }
    const refreshTokenExists = await redis.get(userId);
    if (!refreshTokenExists) throw new Error('no staff refresh token found');
    const user = await db.users.findUniqueOrThrow({where: {id: userId}, include: {permissions: true}});
    const token = jwt.sign(composeTeacherAccessToken(user), config.auth.JWT_SECRET, {
        expiresIn: config.auth.JWT_ACCESS_TOKEN_LIFETIME + 'm'
    });

    await setTokens(token, user.id, event);

    const {id, email, firstname, lastname, tz, role, permissions} = user;
    event.locals.user = {id, email, firstname, lastname, tz, role, permissions};
};

export const refreshStudentToken = async (studentId: string, event: RequestEvent) => {
    if (!redis.isOpen) {
        await redis.connect();
    };
    const refreshTokenExists = await redis.get(studentId);
    if (!refreshTokenExists) throw new Error('no student refresh token found');
    const student = await db.students.findUniqueOrThrow({where: {id: studentId}});
    const token = jwt.sign(
        composeStudentAccessToken(mapper<StudentDto, unknown>(student)),
        config.auth.JWT_SECRET,
        {
            expiresIn: config.auth.JWT_ACCESS_TOKEN_LIFETIME + 'm'
        }
    );

    await setTokens(token, student.id, event);

    const {id, email, firstname, lastname, tz, currentGroup, whatsapp} = student;
    event.locals.user = {
        id,
        email,
        firstname,
        lastname,
        tz,
        currentGroup,
        whatsapp: whatsapp,
        role: Constants.StudentRole
    }
};

export const logout = (event: RequestEvent) => {
    event.cookies.set(Constants.AuthCookieName, ``, {
        httpOnly: true,
        path: '/',
        secure: true,
        sameSite: 'strict',
        maxAge: 0
    });
};

export const redirectToLogin = (event: RequestEvent): Response => {
    return Response.redirect(event.url.origin + Routes.StudentLogin, 302);
};

export const setDisabledToken = async (id: string) => {
    if (!redis.isOpen) {
        await redis.connect();
    }
    console.log('setting disabled token for userId:', id);
    await redis.set(id, 'disabled');
}

export const checkUser = async(id: string) => {
    if (!redis.isOpen) {
        await redis.connect();
    }
    const refreshTokenExists = await redis.get(id);

    return refreshTokenExists;
}

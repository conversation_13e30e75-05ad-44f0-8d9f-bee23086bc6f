import {db} from "$api/core/service-clients/db";
import {mapper} from "$common/core/mapper";
import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";


export const getGroupHolidays = async (groupId: string) => {
    const data = await db.groupHolidays.findMany({
        where: {
            groupId
        },
        orderBy: {
            date: 'asc'
        }
    });

    return data.map((gh) => mapper<GroupHolidayDto, unknown>(gh))
}


export const createGroupHoliday = async (dto: GroupHolidayDto) => {
    const data = await db.groupHolidays.create({
        data: {
            id: dto.id,
            groupId: dto.groupId,
            comment: dto.comment,
            date: dto.date
        }
    })

    return mapper<GroupHolidayDto, unknown>(data)
}


export const deleteGroupHoliday = async (groupHolidayId: string) => {
    const data = await db.groupHolidays.delete({
        where: {
            id: groupHolidayId
        }
    });

    return mapper<GroupHolidayDto, unknown>(data)
}
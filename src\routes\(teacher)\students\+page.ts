import type { PageLoad } from './$types';
import { GroupApiClient } from '$lib/core/api-clients/group-api-client';
import { StudentApiClient } from '$lib/core/api-clients/student-api-client';
import {GeneralHolidaysApiClient} from "$lib/core/api-clients/generalHolidays-api.client";

export const ssr = false;
export const load: PageLoad = async ({ depends }) => {
	try {
		depends('load:students');
		const client = new StudentApiClient();
		return {
			requests: client.getStudentRequests(),
			students: client.getStudents(),
			groups: new GroupApiClient().getGroups(true),
			generalHolidays: new GeneralHolidaysApiClient().getGeneralHolidays()
		};
	} catch (error) {
		return error;
	}
};

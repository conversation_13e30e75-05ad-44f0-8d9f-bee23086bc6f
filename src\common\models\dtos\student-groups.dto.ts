export interface StudentGroupsDto {
	id: string;
	studentId: string;
	groupId: string;
	dateStartActual: Date;
	dateEndActual: Date;
	dateStartTasks: Date;
	dateEndTasks: Date;
	group: {
		id: string;
		name: string;
	};
}

export interface StopCurrentGroupDto {
	id: string;
	dateEndActual: Date;
	studentId: string;
}

export interface SwitchGroupDto {
	studentId: string;
	idSelectedGroup: string;
	dateStartSelectedGroup: string;
	taskStartSelectedGroup: string;
}

export interface UpdateStudentGroupDto {
	id: string;
	groupId: string;
	studentId: string;
	dateStartActual: string | null;
	dateStartTasks: string | null;
	dateEndActual: string | null;
}

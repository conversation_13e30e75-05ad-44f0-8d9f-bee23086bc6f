<script>
    export let result;
    export let text = '';
    export let size = 'xs';
</script>

{#if result >= 0 && result}
    {#if result >= 80}
		<span
                class="font-bold w-fit mt-1 bg-green-100 text-green-800 text-{size} mr-1 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300"
        >
			{text}
            {Math.floor(result)}%
		</span>
    {:else if result >= 50}
		<span
                class="font-bold w-fit mt-1 bg-yellow-100 text-yellow-800 text-{size} mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300"
        >
			{text}
            {Math.floor(result)}%
		</span>
    {:else if result >= 0}
		<span
                class="font-bold w-fit mt-1 bg-red-100 text-red-800 text-{size} mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300"
        >
			{text}
            {Math.floor(result)}%
		</span>
    {/if}
{/if}

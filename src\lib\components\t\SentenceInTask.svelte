<script lang="ts">
    import BaseInput from '$components/common/BaseInput.svelte';
    import {IconLanguage, IconPlayerPlayFilled, IconVenus, IconAlertTriangle} from '@tabler/icons-svelte';
    import {getModalStore, popup} from '@skeletonlabs/skeleton';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher} from 'svelte';
    import type {TranslationCompletion} from '$common/models/dtos/task.dto';
    import He from '$components/common/He.svelte';
    import {ComplaintModalState} from "$lib/state/complaint-modal-state";
    import {page} from "$app/stores";
    import {t} from "$lib/i18n/config";
    import {Constants} from "$api/core/constants";

    const dispatcher = createEventDispatcher();

    export let hintEnabled = true;
    export let sentence: TranslationCompletion;
    export let index: number;
    export let revealMode = false;
    export let disabled = false;
    let audio: HTMLAudioElement;
    const modalStore = getModalStore();
    const pageInfo = $page;


    const handleClickBtn = () => {
        audio.play();
    };

    const composeAudioToPlay = (fileName) =>
        fileName
            ? `https://${pageInfo.data.envs.VITE_S3_BUCKET}.s3.${pageInfo.data.envs.VITE_S3_REGION}.amazonaws.com/${pageInfo.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${fileName}`
            : undefined;

    function createPopUpSettings(index) {
        return {
            event: 'click',
            target: `item-${index}`,
            placement: 'right'
        };
    }

    const openModalForComplaint = () => {
        modalStore.trigger({type: 'component', component: 'complaintSentenceModal'});
        $ComplaintModalState.sentenceId = sentence.sentenceId;
        $ComplaintModalState.createdBy = `${pageInfo?.data?.user?.firstname} ${pageInfo?.data?.user?.lastname}`
        $ComplaintModalState.taskId = pageInfo?.params?.id;
    }

    const showHintConsideringSpecialStudent = (correctValue: string, specialTz: string = 'FF823855') => {
        if ($page?.data?.user?.tz as string !== specialTz)
        {
            return correctValue;
        } else {
            const motivationalEmojis = [
                '🎯✨🏆', '🚀💫⭐', '🎉🔥💪', '🌟💎🎊', '🏅🎈🎁',
                '📈🌱💚', '⚡💥🎉', '🌟🔥💎', '🎊🚀✨', '💫⭐🏆',
                '🧠💡✨', '🎯💪🌟', '🎮🎉🌈', '🎵🎶💃', '🏁🏆🎉'
            ];
            const randomEmoji = motivationalEmojis[Math.floor(Math.random() * motivationalEmojis.length)];
            return randomEmoji;
        }
    };

</script>


<div class="w-full flex flex-col gap-2 mb-5">
    <div dir="ltr" class="w-full break-all flex items-start gap-1">
        <button
                class="border-2 border-primary-900-50-token rounded"
                use:popup={createPopUpSettings(index)}
                on:click={() => {
				if (!revealMode) sentence.hintUsedTimes++;
			}}
                disabled={!hintEnabled}
        >
            <IconLanguage/>
        </button>
        <div class="px-5 py-2 bg-white border-black border-[1px] rounded font-semibold text-black z-50 tracking-wider"
             data-popup="item-{index}" onselectstart="return {$page.data.isDev}">
            <He>{showHintConsideringSpecialStudent(sentence.correctValue)}</He>
            <div class="arrow bg-white border-black border-[1px]"></div>
        </div>
        {#if sentence.displayAsText}
            <div
                    class="justify-between flex break-normal w-full text-base font-semibold {sentence.sex ===
				'f'
					? 'text-error-600-300-token'
					: ''}"
            >
                <div class="flex ml-1" onselectstart="return {$page.data.isDev}">
                    <p>{`${index}.`}</p>
                    <p class="mx-1">{sentence.taskValue}</p>
                </div>
                {#if sentence.sex === 'f'}
                    <div>
                        <IconVenus size="30"/>
                    </div>
                {/if}
            </div>
        {:else}
            <audio
                    bind:this={audio}
                    class="w-full hidden"
                    controls
                    src={composeAudioToPlay(sentence.audioUrl)}>
            </audio>
            <p class="ml-1 mr-3">{index}.</p>
            <BaseButton size="sm" on:click={handleClickBtn} className="h-6">
                <IconPlayerPlayFilled size="20"/>
                {$t('sentences.inTask.listenButton')}
            </BaseButton>
        {/if}
    </div>
    <div class="relative">
        {#if revealMode && sentence.mistakes.length > 0}
            <div class="top-[-25px] right-0 text-green-700 break-words" dir="auto">
                <He>{sentence.correctValue}</He>
            </div>
        {/if}
        <He>
            {#if !revealMode}
                <BaseInput
                        bind:value={sentence.answerValue}
                        inputClasses={revealMode && sentence.mistakes.length === 0
                        ? 'border-green-700'
                        : revealMode
                        ? 'border-red-700'
                        : ''}
                        disabled={revealMode || disabled}
                        disablePaste={!$page.data.isDev}
                        on:input|once={() => dispatcher('inputStarted')}
                />
            {:else}
                <div>
                    <p class="border-[2px] p-1 { + sentence.mistakes.length === 0
                        ? 'border-green-700'
                        : 'border-red-700'}">
                        {sentence.answerValue}
                    </p>
                </div>
                <div class="flex justify-between">
                    <div class="text-red-700 break-all" dir="auto">{sentence.mistakes}</div>
                    {#if pageInfo?.data?.user?.role === Constants.StudentRole}
                        <div>
                            <BaseButton on:click={openModalForComplaint} size="sm"
                                        className="h-5 bg-transparent dark:!bg-transparent text-sm font-mono text-warning-600-300-token align-middle">
                                {$t('sentences.inTask.complaint')}
                                <IconAlertTriangle size="14"/>
                            </BaseButton>
                        </div>
                    {/if}
                </div>
            {/if}
        </He>
    </div>
</div>


import { IncentiveApiClient } from '$lib/core/api-clients/incentive-api-client';
import type { PageLoad } from '../../../../.svelte-kit/types/src/routes/(teacher)/incentive/$types';

export const ssr = false;

export const load: PageLoad = async ({ depends }) => {
	try {
		depends('load:incentive');
		return {
			incentiveContent: new IncentiveApiClient().getIncentiveContent()
		};
	} catch (error) {
		return error;
	}
};

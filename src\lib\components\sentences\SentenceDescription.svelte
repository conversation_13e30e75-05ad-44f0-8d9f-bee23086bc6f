<script lang="ts">
    import {t} from "$lib/i18n/config.js";
    import {format} from "date-fns";
    import {IconStarFilled} from "@tabler/icons-svelte";

    export let sentence;
</script>


<div class="flex justify-between w-full items-center">
	<div class="flex-1"/>
	<div>
		{#if sentence.isFavorite}
			<IconStarFilled class="text-warning-600"/>
		{/if}
	</div>
	<div class="flex flex-1 justify-end gap-4 text-accent text-sm italic font-thin">
		<div dir="auto" class="flex">
			<span class="mr-1">{$t('sentences.updated')} </span>
			{#if sentence.updatedByUser}
				<div class="flex ">
					<p>
							<span class="font-bold ">
								{sentence.updatedByUser.firstname} {sentence.updatedByUser.lastname}
							</span>,
					</p>
					<p>
						{format(new Date(sentence.updatedAt), 'dd/MM/yy kk:mm')}
					</p>
				</div>
			{:else}
				-
			{/if}
		</div>
		<div dir="auto" class="flex mr-2">
			<span class="mr-1">{$t('sentences.created')}</span>
			{#if sentence.createdByUser}
				<div class="flex ">
					<p>
							<span class="font-bold ">
								{sentence.createdByUser.firstname}
								{sentence.createdByUser.lastname}
							</span>,
					</p>
					<p>
						{format(new Date(sentence.createdAt), 'dd/MM/yy kk:mm')}
					</p>
				</div>
			{:else}
				-
			{/if}
		</div>
	</div>
</div>

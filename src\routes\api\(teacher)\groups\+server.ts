import {wrapFunc} from '$api/core/misc/response-wrapper';
import {getGroups} from '$api/core/services/group.service';
import {paramsToKeyValue, toBoolean} from '$api/core/utils';
import {mapper} from '$common/core/mapper';
import type {GroupFilterDto} from '$common/models/filters/group-filter.dto';
import type {RequestEvent} from '@sveltejs/kit';
import type {BaseSortingDto} from "$common/models/filters/base-filter.dto";

export const GET = async ({url, locals}: RequestEvent) => {
    return wrapFunc(async () => {
        const {skip, take, name, hoursSchedule, level, lang, isActive, isPublic, sortBy, sortDir} = paramsToKeyValue(
            url.searchParams
        );
        const creatorId = locals?.user?.id;

        const sort = sortBy ? mapper<BaseSortingDto, unknown>({
            sortBy: sortBy,
            sortDir: sortDir
        }) : undefined;

        return await getGroups(
            mapper<GroupFilterDto, unknown>({
                take: +take,
                skip: +skip,
                name,
                lang: lang === '-1' ? -1 : lang,
                level: +level,
                hoursSchedule: +hoursSchedule,
                isActive: isActive === '-1' ? -1 : +isActive,
                isPublic: isPublic === '-1' ? -1 : +isPublic,
            }), creatorId, sort);
    });
};

<script lang="ts">
    import {t} from "$lib/i18n/config.js";
    import {CampaignFilterState} from "$lib/state/campaign-filter-state.js";
    import {mapEnumToOptions} from "$lib/common/utils.js";
    import {CampaignFilter} from "$common/models/enums.js";
    import BaseSelect from "$components/common/BaseSelect.svelte";
    import BaseInput from "$components/common/BaseInput.svelte";
    import _ from "lodash";
    import type {UserDto} from "$common/models/dtos/user.dto";
	import {get} from "svelte/store";

	let inputValue = '';
    export let users: UserDto[];

    $:authorOptions = users?.map((author) => {
        return {displayValue: `${author.firstname} ${author.lastname}`, value: author.id}
    })

	const onInput = _.debounce(() => $CampaignFilterState = {...get(CampaignFilterState), searchByRecipient: inputValue}, 1000);
</script>

<div class=" card mt-6 p-5 w-full text-token flex justify-between items-center variant-glass-primary">
	<form name="post">
		<div class="flex flex-row gap-10 items-baseline">
			<div class="w-72">
				<BaseInput
						name="search"
						bind:value={inputValue}
						on:input={onInput}
						title={$t('campaigns.filter.search')}
				/>
			</div>
			<div class="flex w-full gap-10 items-baseline">
				<BaseSelect
						name="author"
						title={$t('campaigns.filter.author')}
						options={[{displayValue:'none',value:''},...authorOptions]}
						bind:value={$CampaignFilterState.authorId}
				/>
				<BaseSelect
						name="type"
						title={$t('campaigns.filter.type')}
						options={[{displayValue:'none',value:''},...mapEnumToOptions(CampaignFilter)]}
						bind:value={$CampaignFilterState.type}
				/>
			</div>
		</div>
	</form>
</div>

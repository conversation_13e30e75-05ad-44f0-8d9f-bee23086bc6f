import { wrapFunc } from '$api/core/misc/response-wrapper';
import { getUsers } from '$api/core/services/user.service';
import { paramsToKeyValue } from '$api/core/utils';
import type { RequestEvent } from '@sveltejs/kit';
export const GET = async ({ url }: RequestEvent) =>
	wrapFunc(async () => {
		const { take, skip, search } = paramsToKeyValue(url.searchParams);

		return await getUsers({
			take: +take,
			skip: +skip,
			search
		});
	});

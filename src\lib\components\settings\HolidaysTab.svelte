<script lang="ts">
    import {generateGuid} from "$common/core/utils";
    import {formatToPickerDate, loadingWrap} from "$lib/common/utils";
    import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
    import {GeneralHolidaysApiClient} from "$lib/core/api-clients/generalHolidays-api.client";
    import {invalidate} from "$app/navigation";
    import {t} from "$lib/i18n/config";
    import GeneralHolidaysTable from "$components/settings/GeneralHolidaysTable.svelte";


    export let generalHolidays: GeneralHolidayDto[] = [];


    $: tableTitles = [
        $t('settings.holidays.table.date'),
        $t('settings.holidays.table.comment'),
        $t('settings.holidays.table.action'),
    ];


    const save = async (generalHoliday: GeneralHolidayDto) => {
        await new GeneralHolidaysApiClient().postGeneralHoliday(generalHoliday);
        await loadingWrap(async () => {
            await invalidate('load:settings');
        });
    }

    const createNewGeneralHoliday = () => {
        generalHolidays = [
            {
                id: generateGuid(),
                date: formatToPickerDate(new Date()),
                comment: '',
                isNew: true
            },
            ...generalHolidays
        ]
    }

    const deleteHoliday = async (generalHoliday: GeneralHolidayDto) => {
        if (generalHoliday.isNew) {
            generalHolidays = generalHolidays.filter((g) => g.id !== generalHoliday.id)
        } else {
            await new GeneralHolidaysApiClient().deleteGeneralHoliday(generalHoliday.id);
            await loadingWrap(async () => {
                await invalidate('load:settings');
            });
        }
    }


</script>


<GeneralHolidaysTable {generalHolidays} {deleteHoliday}
                      {createNewGeneralHoliday} {tableTitles}
                      {save}/>
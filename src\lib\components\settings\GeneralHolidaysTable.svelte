<script lang="ts">
    import {format} from "date-fns";
    import {t} from "$lib/i18n/config.js";
    import BaseInput from "$components/common/BaseInput.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";


    export let createNewGeneralHoliday: () => void;

    export let tableTitles: string[];

    export let save: (generalHoliday: GeneralHolidayDto) => void;


    export let deleteHoliday: (generalHoliday: GeneralHolidayDto) => void;

    export let generalHolidays: GeneralHolidayDto[] = [];

</script>


<div class="flex flex-col gap-5 h-full ">
    <BaseButton on:click={createNewGeneralHoliday} className="self-start">
        +
    </BaseButton>
    <table class="table overflow-y-auto overflow-x-auto table-hover table-compact h-fit">
        <thead on:keypress>
        <tr>
            {#each tableTitles as title}
                <th class="text-right {title==='Date'?'w-[20%]':''} {title==='Comment'?'w-[65%]':''} {title==='Action'?'w-[15%]':''}">{title}</th>
            {/each}
        </tr>
        </thead>
        <tbody>
        {#each generalHolidays as holiday,index}
            <tr>
                {#if holiday.isNew}
                    <td>
                        <input dir="rtl" class="input text-center h-[43px] rounded pr-[5px] w-[70%]" type="date"
                               bind:value={generalHolidays[index].date}>
                    </td>
                {:else}
                    <td>{format(new Date(holiday.date), 'dd.MM.yyyy')}</td>
                {/if}
                {#if holiday.isNew}
                    <td class="">
                        <BaseInput class="w-full" type="text" bind:value={generalHolidays[index].comment}/>
                    </td>
                {:else}
                    <td>{holiday.comment}</td>
                {/if}
                <td>
                    <BaseButton on:click={()=>deleteHoliday(holiday)}>
                        {$t('settings.holidays.table.buttons.delete')}
                    </BaseButton>
                    {#if holiday.isNew}
                        <BaseButton on:click={()=>save(holiday)}>
                            {$t('settings.holidays.table.buttons.save')}
                        </BaseButton>
                    {/if}
                </td>
            </tr>
        {/each}

        </tbody>
    </table>
</div>

<script lang="ts">
	import {IconClockHour9, IconEar, IconEyeOff, IconGift, IconHeadphones, IconLanguage, IconMicrophone} from '@tabler/icons-svelte';
	import BaseButton from '$components/common/BaseButton.svelte';
	import {getCertainCompletionState, time} from '$lib/state/task-current-completion.state';
	import {NotificationType, TaskMode} from '$common/models/enums';
	import {page} from '$app/stores';
	import _ from 'lodash';
	import {onMount} from 'svelte';
	import ResultBadge from '$components/t/ResultBadge.svelte';
	import {add, intervalToDuration, min} from 'date-fns';
	import {durationToCountdown} from '$lib/common/utils';
	import {
		getAveragePercentResult,
		getBestResultForMode,
		getTotalEnabledTaskModesCount,
		getTaskModesDone, switchMode, wasTaskModeDone
	} from '$lib/common/task-helpers';
	import {t} from '$lib/i18n/config';
	import NotificationStore from '$lib/state/notification-state';

	export let useTempState = false;
	export let bypassBefore60 = false;

	let additionalTasksOpenTimes;
	let modesEnabledByPassingMinimum;

	$: currentState = getCertainCompletionState($page.params.id, !useTempState);
	$: getLockedStateByTaskMode = (mode: TaskMode) => {
		if (modesEnabledByPassingMinimum) {
			const locked = additionalTasksOpenTimes?.[mode] && $time < additionalTasksOpenTimes?.[mode];
			if (!locked) return {locked, countdown: null};

			const interval = intervalToDuration({
				start: $time,
				end: additionalTasksOpenTimes[mode]
			});

			const countdown = durationToCountdown(interval);

			return {locked, countdown};
		}
	};

    const getMicrophone = async () => {
        const res = await navigator.mediaDevices.enumerateDevices();
        console.log(res);
        return res.some(device => device.kind === "audioinput");
    }

	// Helper function to check if any completed mode has score >= 60%
	const hasPassingScore = () => {
		// Check all completed task modes for a passing score
		const completedModes = $currentState?.results || [];
		return completedModes.some(result => result?.scorePercent >= 60);
	};

	onMount(() => {
		modesEnabledByPassingMinimum = hasPassingScore() || (useTempState && bypassBefore60);
		if (modesEnabledByPassingMinimum) {
			additionalTasksOpenTimes = getTimeToOpenDelayedTaskModes();
		}

		console.log($currentState)
	});

	const getTimeToOpenDelayedTaskModes = () => {
		const tasksWithDelay = _.values($currentState?.task?.additionalTasks).filter(
			(x) => x.delay > 0
		);

		return _.reduce(
			tasksWithDelay.map((x) => {
				return {[x.mode]: getTimeToOpenTaskMode(x.mode)};
			}),
			function (result, currentObject) {
				return _.assign(result, currentObject);
			},
			{}
		);
	};

	const getTimeToOpenTaskMode = (mode: TaskMode) => {
		const minutesDelay =
			_.values($currentState?.task?.additionalTasks)?.find((x) => x.mode === mode)?.delay || 0;
		// Find earliest successful completion from any mode with score >= 60%
		const earliestSuccessfulCompletion = min(
			$currentState?.results
				.filter((x) => x?.scorePercent >= 60)
				.map((x) => new Date(x.finishedAt))
		);

		return add(earliestSuccessfulCompletion, {minutes: minutesDelay});
	};

	const getResultSummary = () => {
		const totalEnabledTaskModesCount = getTotalEnabledTaskModesCount($currentState?.task);
		const taskModesDoneCount = getTaskModesDone($currentState?.results)?.length ?? 0;
		const averageResult = getAveragePercentResult($currentState);

		return {totalEnabledTaskModesCount, taskModesDoneCount, averageResult};
	};

	$: isHere = (mode: TaskMode) => $currentState.currentMode === mode;

	const switchTo = (mode: TaskMode) => {
		currentState.set(switchMode(mode, $currentState));
	}
</script>

<div class="w-full variant-glass-primary card p-4 flex flex-col gap-x-5">
    {#if getResultSummary()}
        {@const {totalEnabledTaskModesCount, taskModesDoneCount, averageResult} = getResultSummary()}
        <div class="flex flex-col gap-2 self-center justify-center items-center max-w-[400px]">
            <p class="font-bold text-xl">
                {#if averageResult < 60}
                    {$t('t.additionalTasksNavigation.title.bad')}
                {:else if averageResult < 85}
                    {$t('t.additionalTasksNavigation.title.good')}
                {:else}
                    {$t('t.additionalTasksNavigation.title.awesome')}
                {/if}
            </p>

            {#if modesEnabledByPassingMinimum}
                <h2 class="font-bold text-lg">
                    {taskModesDoneCount}/{totalEnabledTaskModesCount}, {$t(
                    't.additionalTasksNavigation.subtitle'
                )}
                    <ResultBadge result={averageResult} size="md"/>
                </h2>
            {:else}
                <p class="mt-2 text-center font-bold text-yellow-800 dark:text-yellow-300">
                    {$t('t.additionalTasksNavigation.paragraph')}
                </p>
            {/if}
        </div>
    {/if}


    {#if modesEnabledByPassingMinimum}
        <div
                dir="ltr"
                class="grid grid-cols-2 gap-5 md:grid-cols-3 lg:grid lg:grid-cols-4 lg:gap-5 mt-9"
        >
            <div class="flex flex-col" on:click={getResultSummary}>
                <p class="mb-1 animate-bounce {isHere(TaskMode.translation) ? '' : 'invisible'}">
                    👇 {$t('t.additionalTasksNavigation.direction')}
                </p>
                <BaseButton
                        on:click={() => switchTo(TaskMode.translation)}
                        disabled={isHere(TaskMode.translation) || !modesEnabledByPassingMinimum}
                >
                    <IconLanguage/>
                    {$t('t.additionalTasksNavigation.translations')}
                </BaseButton>
                {#if wasTaskModeDone($currentState?.results, TaskMode.translation)}
                    <ResultBadge result={getBestResultForMode(TaskMode.translation, $currentState)}/>
                {/if}
            </div>

            {#if $currentState?.task?.additionalTasks?.listen?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.listen)}
                <div class="flex flex-col">
                    <p class="mb-1 animate-bounce {isHere(TaskMode.listen) ? '' : 'invisible'}">
                        👇 {$t('t.additionalTasksNavigation.direction')}
                    </p>
                    <BaseButton
                            on:click={() => switchTo(TaskMode.listen)}
                            disabled={isHere(TaskMode.listen) || locked}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconHeadphones/>
                            {$t('t.additionalTasksNavigation.listening')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState.results, TaskMode.listen)}
                        <ResultBadge result={getBestResultForMode(TaskMode.listen, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.audiodic?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.audiodic)}
                <div class="flex flex-col">
                    <p class="mb-1 animate-bounce {isHere(TaskMode.audiodic) ? '' : 'invisible'}">
                        👇 {$t('t.additionalTasksNavigation.direction')}
                    </p>
                    <BaseButton
                            on:click={() => switchTo(TaskMode.audiodic)}
                            disabled={isHere(TaskMode.audiodic) || locked}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconEar/>
                            {$t('t.additionalTasksNavigation.dictation')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState?.results, TaskMode.audiodic)}
                        <ResultBadge result={getBestResultForMode(TaskMode.audiodic, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.bytime?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.bytime)}
                <div class="flex flex-col">
                    <p class="mb-1 animate-bounce {isHere(TaskMode.bytime) ? '' : 'invisible'}">
                        👇 {$t('t.additionalTasksNavigation.direction')}
                    </p>
                    <BaseButton
                            on:click={() => switchTo(TaskMode.bytime)}
                            disabled={isHere(TaskMode.bytime) || locked}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconClockHour9/>
                            {$t('t.additionalTasksNavigation.translationsShort')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState.results, TaskMode.bytime)}
                        <ResultBadge result={getBestResultForMode(TaskMode.bytime, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.phantom?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.phantom)}
                <div class="flex flex-col">
                    <p class="mb-1 animate-bounce {isHere(TaskMode.phantom) ? '' : 'invisible'}">
                        👇 {$t('t.additionalTasksNavigation.direction')}
                    </p>
                    <BaseButton
                            on:click={() => switchTo(TaskMode.phantom)}
                            disabled={isHere(TaskMode.phantom) || locked}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconEyeOff/>
                            {$t('t.additionalTasksNavigation.phantomDictation')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState?.results, TaskMode.phantom)}
                        <ResultBadge result={getBestResultForMode(TaskMode.phantom, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.voice?.enabled}
            {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.voice)}
            <div class="flex flex-col">
                <p class="mb-1 animate-bounce {isHere(TaskMode.voice) ? '' : 'invisible'}">
                    👇 {$t('t.additionalTasksNavigation.direction')}
                </p>
                <BaseButton
                        on:click={async () => { if (await getMicrophone()) {
                             switchTo(TaskMode.voice)
                        }
                        else {
                            NotificationStore.push({
                                type: NotificationType.error,
                                message: $t('t.additionalTasksNavigation.noMicrophone')}, 7) }}
                        }
                        disabled={isHere(TaskMode.voice) || locked}
                >
                    {#if locked}
                        <IconGift/>
                        {countdown}
                    {:else}
                        <IconMicrophone/>
                        {$t('t.additionalTasksNavigation.voice')}
                    {/if}
                </BaseButton>
                {#if wasTaskModeDone($currentState?.results, TaskMode.voice)}
                    <ResultBadge result={getBestResultForMode(TaskMode.voice, $currentState)}/>
                {/if}
            </div>
        {/if}

        </div>
    {/if}
</div>

{#if $page.data?.user?.firstname === "Tal"}
    <div class="flex justify-between">
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:130;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="26.6000000pt" height="20.5000000pt"
             viewBox="0 0 266.000000 205.000000">
            <g transform="translate(0.000000,205.000000) scale(0.100000,-0.100000)">
                <path style="fill:transparent; stroke:darkgreen;stroke-width:150;"
                      d="M693 1673 c2 -5 48 -83 101 -174 53 -91 96 -171 96 -176 0 -6 -93 -170 -206 -364 -113 -195 -249 -428 -301 -518 -53 -90 -98 -172 -100 -182 -5 -18 3 -19 198 -19 l204 0 51 88 c94 159 260 446 310 534 48 86 66 103 78 73 3 -9 94 -168 203 -355 l198 -340 207 0 c115 0 208 2 208 4 0 3 -45 82 -100 176 -55 94 -100 176 -100 182 0 6 26 57 59 112 32 56 156 270 276 476 120 206 233 400 251 430 l33 55 -206 3 c-114 1 -210 -1 -214 -5 -6 -7 -154 -260 -367 -627 -42 -73 -46 -77 -59 -60 -8 11 -102 171 -209 357 l-196 337 -210 0 c-115 0 -207 -3 -205 -7z"/>
            </g>
        </svg>
    </div>
{/if}

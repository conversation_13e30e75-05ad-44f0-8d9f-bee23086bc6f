<script lang="ts">
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconEdit, IconX} from '@tabler/icons-svelte';
    import type {StudentGroupsDto} from '$common/models/dtos/student-groups.dto';
    import {t} from '$lib/i18n/config';
    import {onMount} from "svelte";
    import {StudentEditModalState} from "$lib/state/student-edit-state";
    import {calculateStudentStudyHoursAndDays} from "$lib/common/utils";
    import {format} from "date-fns";

    export let openModalByName: (group: StudentGroupsDto, fieldName: string) => void;

    let studentGroupLearningStatistic: { groupId: '', totalHours: number, totalDays: number, from: Date }[] = [];


    onMount(() => {
        for (const argument of $StudentEditModalState.studentLearningHistory) {
            let statistics = calculateStudentStudyHoursAndDays([{...argument}], $StudentEditModalState.generalHolidays);
            studentGroupLearningStatistic = [...studentGroupLearningStatistic, {
                groupId: argument.groupId,
                from: argument.from, ...statistics
            }];
        }
    })

    $:console.log($GroupHistoryState)

</script>

<div class="p-2 flex flex-col gap-2 variant-glass-primary">
    <div class="grid grid-cols-8">
        <div class="font-bold col-start-1 col-end-3">
            {$t('students.students.modal.groupsHistory.table.group')}
        </div>
        <div class="font-bold text-center">
            {$t('students.students.modal.groupsHistory.table.startlearning')}
        </div>
        <div class="font-bold text-center">
            {$t('students.students.modal.groupsHistory.table.starttasks')}
        </div>
        <div class="font-bold text-center">
            {$t('students.students.modal.groupsHistory.table.totalDays')}
        </div>
        <div class="font-bold text-center">
            {$t('students.students.modal.groupsHistory.table.totalHours')}
        </div>
        <div class="font-bold text-center">
            {$t('students.students.modal.groupsHistory.table.endlearning')}
        </div>

        <div class=""></div>
    </div>

    {#each $GroupHistoryState.groupsHistory as group}
        {@const groupLearningStatistic = studentGroupLearningStatistic.find((slg) => slg.groupId === group.groupId && slg.from === group.dateStartActual)}

        <div class="grid grid-cols-8 gap-x-3 items-center">
            <div class="col-start-1 col-end-3 break-all">{group.group.name}</div>
            <div class="flex justify-center">
                {#if group.dateStartActual}
                    {format(new Date(group.dateStartActual), 'dd.MM.yyyy')}
                {/if}
            </div>
            <div class="flex justify-center">
                {#if group.dateStartTasks}
                    {format(new Date(group.dateStartTasks), 'dd.MM.yyyy')}
                {/if}
            </div>
            <div class="text-center">
                {groupLearningStatistic?.totalDays ? groupLearningStatistic?.totalDays : '–'}
            </div>
            <div class="text-center">
                {groupLearningStatistic?.totalHours ? groupLearningStatistic?.totalHours : '–'}
            </div>
            {#if !group.dateEndActual}
                <div class="flex justify-center">
                    <BaseButton
                            on:click={() => {
							openModalByName(group, 'stopStudentGroupModal');
						}}
                            size="sm"
                    >
                        {$t('students.students.modal.groupsHistory.stop')}
                    </BaseButton>
                </div>
            {:else}
                <div>
                    {format(new Date(group.dateEndActual), 'dd.MM.yyyy')}
                </div>
            {/if}

            <div class="flex gap-2 justify-center">
                <div>
                    <BaseButton
                            on:click={() => {
							openModalByName(group, 'updateStudentGroupModal');
						}}
                            size="sm"
                    >
                        <IconEdit size="22"/>
                    </BaseButton>
                </div>
                <div
                        on:click={() => {
						openModalByName(group, 'deleteStudentGroupModal');
					}}
                >
                    <BaseButton size="sm">
                        <IconX size="22"/>
                    </BaseButton>
                </div>
            </div>
        </div>
    {/each}
</div>

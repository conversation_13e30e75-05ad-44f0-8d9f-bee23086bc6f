<script lang="ts">
    import {onMount} from 'svelte';
    import {StudentApiClient} from '$lib/core/api-clients/student-api-client';
    import {type StudentGroupsDto} from '$common/models/dtos/student-groups.dto';
    import {differenceInDays, isFuture, isWithinInterval} from 'date-fns';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import {GroupApiClient} from '$lib/core/api-clients/group-api-client';
    import GroupsHistorySkeleton from '$components/students/GroupsHistorySkeleton.svelte';
    import GroupsHistoryTable from '$components/students/GroupsHistoryTable.svelte';
    import {t} from '$lib/i18n/config';

    export let studentId;
    export let isAcceptingModal = false;
    const modalStore = getModalStore();
    let loading = true;

    $: findGroupForSwitching = () => {
        if (
            !Array.isArray($GroupHistoryState.groupsHistory) ||
            $GroupHistoryState.groupsHistory.length === 0
        )
            return null;
        let currenGroup;
        let closestGroup = null;
        let diffInDays = Infinity;
        if (
            $GroupHistoryState.groupsHistory.length === 1 &&
            !$GroupHistoryState.groupsHistory[0]?.dateEndActual
        ) {
            currenGroup = $GroupHistoryState.groupsHistory[0];
        } else {
            for (const item of $GroupHistoryState.groupsHistory) {
                const {dateStartActual, dateEndActual} = item;
                if (dateStartActual && dateEndActual) {
                    if (
                        isWithinInterval(new Date(), {
                            start: new Date(dateStartActual),
                            end: new Date(dateEndActual)
                        })
                    ) {
                        currenGroup = item;
                    }
                } else {
                    const diff = differenceInDays(new Date(dateStartActual), new Date());
                    if (isFuture(new Date(dateStartActual)) && diff >= 0 && diff < diffInDays) {
                        diffInDays = diff;
                        closestGroup = item;
                    } else {
                        closestGroup = item;
                    }
                }
            }
        }
        if (!currenGroup) {
            currenGroup = closestGroup;
        }
        return currenGroup;
    };

    $: groupForSwitching = findGroupForSwitching();
    $: disableSwitch = isAcceptingModal;

    onMount(async () => {
        const data = await new StudentApiClient().getGroupsHistoryByStudentId(studentId);
        const groups = await new GroupApiClient().getGroups(true);
        $GroupHistoryState.groups = [...groups.data];
        $GroupHistoryState.groupsHistory = [...data];
        loading = false;
    });

    const openModalByName = (group: StudentGroupsDto, modalName: string) => {
        const updatedState = {...$GroupHistoryState, selectedGroup: {...group}};
        $GroupHistoryState = {...updatedState};
        setTimeout(() => $modalStore = [{type: 'component', component: modalName}, ...$modalStore], 100);
    };


</script>

<div class="flex flex-col gap-4">
    {#if !disableSwitch }
        <div class="self-end">
            <BaseButton
                    on:click={() => {
					openModalByName(findGroupForSwitching(), 'switchStudentGroupModal');
				}}
            >{$t('students.students.modal.groupsHistory.switch')}
            </BaseButton>
        </div>
    {/if}
    {#if loading}
        <GroupsHistorySkeleton/>
    {:else if $GroupHistoryState.groupsHistory.length > 0}
        <GroupsHistoryTable {openModalByName}/>
    {/if}
</div>

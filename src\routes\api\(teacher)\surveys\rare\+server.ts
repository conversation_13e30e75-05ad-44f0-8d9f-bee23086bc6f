import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {mapper} from "$common/core/mapper";
import type {RareSurveyDto} from "$common/models/dtos/rare-survey.dto";
import {updateGroupRareSurvey} from "$api/core/services/group.service";


export const PUT = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const rareSurvey = await event.request.json();
        const dto = mapper<RareSurveyDto, unknown>(rareSurvey);
        await updateGroupRareSurvey(dto);
    });
import {z} from 'zod';

export const groupSchemaForCreatingGroup = z
    .object({
        daysSchedule: z.string().refine((value) => !/^0*$/.test(value), {
            message: 'groups.modal.formFieldsErrors.daysSchedule'
        }),
        timeStart: z.string().regex(/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/, 'groups.modal.formFieldsErrors.timeStart'),
        timeEnd: z.string().regex(/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/, 'groups.modal.formFieldsErrors.timeEnd'),
        totalHoursAmount: z.number({invalid_type_error: 'groups.modal.formFieldsErrors.totalHoursAmountInvalidType'})
            .min(10, {message: 'groups.modal.formFieldsErrors.totalHoursAmountMin'})
            .max(1000, {message: 'groups.modal.formFieldsErrors.totalHoursAmountMax'}),
        hoursSpendBySession: z.number({invalid_type_error: 'groups.modal.formFieldsErrors.hoursSpendBySessionInvalidType'})
            .min(1, {message: 'groups.modal.formFieldsErrors.hoursSpendBySessionMin'})
            .max(10, {message: 'groups.modal.formFieldsErrors.hoursSpendBySessionMax'})
        ,
        dateStart: z.string().refine((value) => !isNaN(Date.parse(value)), {
            message: 'groups.modal.formFieldsErrors.dateStart'
        }),
        dateEnd: z.string().refine((value) => !isNaN(Date.parse(value)), {
            message: 'groups.modalformFieldsErrors.dateEnd'
        })
    })
    .refine((value) => !(new Date(`2000-01-01 ${value.timeStart}`) > new Date(`2000-01-01 ${value.timeEnd}`)), {
        message: 'groups.modal.formFieldsErrors.timeStartMoreThenTimeEnd',
        path: ['timeStart']
    })
    .refine((value) => !(Date.parse(value.dateStart) > Date.parse(value.dateEnd)), {
        message: 'groups.modal.formFieldsErrors.dateStartMoreThenDateEnd',
        path: ['dateStart', 'dateEnd']
    });

import type {SentenceDto, SentenceInTaskDto} from '$common/models/dtos/sentence.dto';
import type {TaskMode} from '$common/models/enums';
import type {TaskIncentiveContentWithContentDto} from '$common/models/dtos/task-incentive-content-with-content.dto';
import type {UserDto} from '$common/models/dtos/user.dto';

export interface TaskDto {
    id: string;
    groupId: string;
    type: 'home' | 'class' | 'sandbox';
    date: string;
    time: string;
    dateTime: string;
    lang: 'EN' | 'RU'
    commentPublic: string;
    commentPrivate: string;
    content: any;
    page_content: {
        id: string,
        taskId: string,
        incentiveContentId: string,
        content_items: {
            id: string,
            pageContentId: string,
            type: 'lexical' | 'file' | 'audio',
            content: any,
            position: number
        }[]
    },
    isActive: boolean;
    hintsEnabled: boolean;
    navigationInPublicTaskEnabled: boolean;
    navigationInPublicTaskOnlyAfter60: boolean;
    isDeleted: boolean;
    duplicatedFromTaskId: string;
    sentences: SentenceInTaskDto[];
    additionalTasks: AdditionalTaskContainer;
    task_incentive_content: TaskIncentiveContentWithContentDto[];
    hebrewFont: boolean;
    allowAnonymous: boolean;
    startingMode: TaskMode;
    createdBy: string,
    updatedBy: string,
    createdByUser: {
        firstname: string,
        lastname: string
    },
    updatedByUser: {
        firstname: string,
        lastname: string
    }
}

export interface TaskWithResultsDto extends TaskDto {
    results: CompletionTaskStateModel[];
}

export interface ExtendedTaskDto extends TaskDto {
    isPublic: boolean;
}

export interface EditableTaskDto extends ExtendedTaskDto {
    sentencesToChoose: SentenceDto[];
    sentencesToChooseCount: number;
    users: UserDto[];
    isNew: boolean;
}

export interface FileUploadDto {
    success: 1 | 0;
    file?: FileDto;
}

export interface FileDto {
    name: string;
    title: string;
    size: number;
    type: string;
    url: string;
}

export interface AdditionalTaskDto {
    id: string;
    mode: TaskMode;
    time: number;
    delay: number;
    maxScore: number;
    hintEnabled: boolean;
    enabled: boolean;
    speedMode: string
    allowAnonymous: boolean;
    allowRetry: boolean;
}

export interface AdditionalTaskContainer {
    listen: AdditionalTaskDto;
    bytime: AdditionalTaskDto;
    phantom: AdditionalTaskDto;
    audiodic: AdditionalTaskDto;
    voice: AdditionalTaskDto;
}

export interface TaskResultDto {
    mode: TaskMode;
    startedAt: Date;
    finishedAt: Date;
    spentTime: Duration;
    scorePercent: number;
    scoreAbsolute: number;
    hintsUsed: number;
    completions: SentenceCompletion[] | TranslationCompletion[];
}

export interface SentenceCompletion {
    index: number;
    audioUrl: string;
    correctValue: string;
    sentenceId: string;
    isCorrect: boolean;
}

export interface TranslationCompletion extends SentenceCompletion {
    displayAsText: boolean;
    taskValue: string;
    answerValue: string;
    sex: 'm' | 'f';
    mistakes: string;
    hintUsedTimes: number;
}

export interface CompletionTaskStateModel {
    id: string;
    currentMode: TaskMode;
    currentResult: TaskResultDto | null;
    task: TaskDto;
    results: TaskResultDto[];
    currentScore: number;
    currentScoreAbsolute: number;
    intermediateState: boolean;
}

export interface CompletionTaskDto
    extends Omit<CompletionTaskStateModel, 'task' | 'currentMode' | 'intermediateState'> {
    taskId: string;
    taskReleaseDate: Date;
}

export interface PublicCompletionTaskStateModel extends CompletionTaskStateModel {
    name: string | null;
    whatsapp: string | null;
}

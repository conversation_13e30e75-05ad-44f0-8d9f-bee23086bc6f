<script lang="ts">
	import {page} from "$app/stores";
	import {type Permission} from "$common/models/enums";
	import {Constants} from "$api/core/constants";
	import type {PermissionDto} from "$common/models/dtos/user.dto";

	export let visibleFor = 'admin'
	export let permission: Permission;
	export let additionalCondition = true;
	export let bypassCondition = false;

	let userPermissions: PermissionDto[] = $page.data.user.permissions;
	let isOpenAccess = userPermissions?.find((p) => p.permission === permission);
</script>


{#if $page.data?.user?.role === Constants.AdminRole || ($page.data?.user?.role !== Constants.AdminRole && isOpenAccess && additionalCondition) || bypassCondition}
    <slot name="authorized"/>
    <slot/>
{:else}
    <slot name="forbidden"/>
{/if}


<!--{#if role === visibleFor}-->
<!--    <slot/>-->
<!--{/if}-->

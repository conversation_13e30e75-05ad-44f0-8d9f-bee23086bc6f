import { writable } from 'svelte/store';
import type { CampaignDto } from '$common/models/dtos/notification.dto';

export const initialSendNotificationModalState: CampaignDto = {
	id: '',
	type: 'student',
	title: '',
	message: '',
	recipientId: '',
	createdAt: undefined,
	createdBy: '',
	author: undefined,
	groupLang: 'ru'
};

export const SendNotificationModalState = writable({...initialSendNotificationModalState});

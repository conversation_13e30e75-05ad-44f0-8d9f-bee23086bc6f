import {sveltekit} from '@sveltejs/kit/vite';
import {defineConfig} from 'vite';
import {viteStaticCopy} from 'vite-plugin-static-copy'

export default ({mode}) => {
    console.info(`App is started on ${mode} env.`)
    const plugins =  [viteStaticCopy({
            targets: [
                {src: 'node_modules/tinymce/*', dest: 'tinymce'}
            ]
        }), sveltekit()];

    return defineConfig({
        base: '',
        plugins,
        optimizeDeps: {
            exclude: ['@ffmpeg/ffmpeg', '@ffmpeg/util']
        },
    });
}

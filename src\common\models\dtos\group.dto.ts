import type {LanguageFilter} from '$common/models/enums';
import type {HoursScheduleFilter, LevelFilter} from '$common/models/enums';
import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";
import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";

export interface DaysSchedule {
    Su: 0 | 1;
    Mo: 0 | 1;
    Tu: 0 | 1;
    We: 0 | 1;
    Th: 0 | 1;
}

export interface StopGroupDto {
    id: string;
    dateEnd: string;
}

export interface GroupDto {
    id: string;
    name: string;
    dateStart: Date;
    dateEnd: Date;
    level: number;
    lang: string;
    daysSchedule: string;
    hoursSchedule: number;
    timeStart: Date;
    timeEnd: Date;
    whatsappUrl?: string;
    comment?: string;
    isActive?: boolean;
    isPublic?: boolean;
    studentsCount?: number;
    hoursSpendBySession: number;
    totalHoursAmount: number;
    groupScheduleChanges?: GroupScheduleDto[],
    groupHolidaysExceptions?: GroupHolidayExcDto[],
    groupHoliday?: GroupHolidayDto[]
}

export interface FormGroup {
    action: 'update' | 'create';
    level: LevelFilter;
    lang: LanguageFilter;
    isPublic: boolean;
    isActive: boolean;
    hoursSchedule: HoursScheduleFilter;
    daysSchedule: DaysSchedule;
    whatsappUrl: string;
    comment: string;
    dateStart: string;
    dateEnd: string;
    timeStart: string;
    timeEnd: string;
    totalHoursAmount: number;
    hoursSpendBySession: number;
    groupScheduleChanges: GroupScheduleDto[],
    generalHolidays: GeneralHolidayDto[],
    groupHolidays: GroupHolidayDto[],
    groupHolidaysExc: GroupHolidayExcDto[]
}

<script lang="ts">
	import {t} from '$lib/i18n/config';
	import LoadingSpinner2 from "$components/common/LoadingSpinner2.svelte";
	import {LocaleState} from "$lib/state/locale.state";

	export let data;

	$: loaded = false;
</script>

<div class="flex justify-center w-full flex-col" dir="ltr">
    <div class="self-center p-4 max-w-[760px] flex flex-col gap-5 w-full mt-5 mb-5">
        <h1 class="text-xl text-center font-bold mb-5">{$t('studentTasks.practice.title')}</h1>
        {#if !loaded}
            <LoadingSpinner2></LoadingSpinner2>
        {/if}
    </div>
</div>
<div class="flex flex-col gap-2 w-full mb-2">
    <div class="flex flex-col gap-2">
        <iframe class="{!loaded ? 'invisible' : ''}"
                on:load={() => setTimeout(() => loaded = true, 300)}
                src="https://tables.ulpanmorasha.com/practice?embed=1&lang={$LocaleState}"
        >
        </iframe>
    </div>
</div>

<style>
    iframe {
        display: block; /* iframes are inline by default */
        background: #000;
        border: none; /* Reset default border */
        height: 100vh; /* Viewport-relative units */
        width: calc(100 vw-160px);
    }
</style>

<script lang="ts">
    import {IconClockHour9, IconEar, IconEyeOff, IconGift, IconHeadphones, IconLanguage, IconMicrophone} from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {getCertainCompletionState, time} from '$lib/state/task-current-completion.state';
    import {TaskMode} from '$common/models/enums';
    import {page} from '$app/stores';
    import _ from 'lodash';
    import {onMount} from 'svelte';
    import ResultBadge from '$components/t/ResultBadge.svelte';
    import {add, intervalToDuration, min} from 'date-fns';
    import {durationToCountdown} from '$lib/common/utils';
    import {
        getAveragePercentResult,
        getBestResultForMode,
        getTotalEnabledTaskModesCount,
        getTaskModesDone, switchMode, wasTaskModeDone
    } from '$lib/common/task-helpers';
    import {t} from '$lib/i18n/config';

	export let useTempState = false;
	export let bypassBefore60 = false;

    let additionalTasksOpenTimes;
    let modesEnabledByPassingMinimum;

    $: currentState = getCertainCompletionState($page.params.id, !useTempState);
    $: getLockedStateByTaskMode = (mode: TaskMode) => {
        if (modesEnabledByPassingMinimum) {
            const locked = additionalTasksOpenTimes?.[mode] && $time < additionalTasksOpenTimes?.[mode];
            if (!locked) return {locked, countdown: null};

            const interval = intervalToDuration({
                start: $time,
                end: additionalTasksOpenTimes[mode]
            });

            const countdown = durationToCountdown(interval);

            return {locked, countdown};
        }
    };

    // Helper function to check if any completed mode has score >= 60%
    const hasPassingScore = () => {
        // Check all completed task modes for a passing score
        const completedModes = $currentState?.results || [];
        return completedModes.some(result => result?.scorePercent >= 60);
    };

    onMount(() => {
        modesEnabledByPassingMinimum = hasPassingScore() || (useTempState && bypassBefore60);
        if (modesEnabledByPassingMinimum) {
            additionalTasksOpenTimes = getTimeToOpenDelayedTaskModes();
        }
    });

    const getTimeToOpenDelayedTaskModes = () => {
        const tasksWithDelay = _.values($currentState?.task?.additionalTasks).filter(
            (x) => x.delay > 0
        );

        return _.reduce(
            tasksWithDelay.map((x) => {
                return {[x.mode]: getTimeToOpenTaskMode(x.mode)};
            }),
            function (result, currentObject) {
                return _.assign(result, currentObject);
            },
            {}
        );
    };

    const getTimeToOpenTaskMode = (mode: TaskMode) => {
        const minutesDelay =
            _.values($currentState?.task?.additionalTasks)?.find((x) => x.mode === mode)?.delay || 0;
        // Find earliest successful completion from any mode with score >= 60%
        const earliestSuccessfulCompletion = min(
            $currentState?.results
                .filter((x) => x.scorePercent >= 60)
                .map((x) => new Date(x.finishedAt))
        );

        return add(earliestSuccessfulCompletion, {minutes: minutesDelay});
    };

    const getResultSummary = () => {
        const totalEnabledTaskModesCount = getTotalEnabledTaskModesCount($currentState?.task);
        const taskModesDoneCount = getTaskModesDone($currentState?.results)?.length ?? 0;
        const averageResult = getAveragePercentResult($currentState);

        return {totalEnabledTaskModesCount, taskModesDoneCount, averageResult};
    };

    $: isHere = (mode: TaskMode) => $currentState.currentMode === mode;

    const switchTo = (mode: TaskMode) => {
        currentState.set(switchMode(mode, $currentState));
    }
</script>

<div class="w-full variant-glass-primary card p-4 flex flex-col gap-x-5">
    {#if modesEnabledByPassingMinimum}
        <div
                dir="ltr"
                class="grid grid-cols-2 gap-5 md:grid-cols-3 lg:grid lg:grid-cols-4 lg:gap-5 mt-9"
        >
            <div class="flex flex-col">
                <BaseButton
                        on:click={() => switchTo(TaskMode.translation)}
                >
                    <IconLanguage/>
                    {$t('t.additionalTasksNavigation.translations')}
                </BaseButton>
                {#if wasTaskModeDone($currentState?.results, TaskMode.translation)}
                    <ResultBadge result={getBestResultForMode(TaskMode.translation, $currentState)}/>
                {/if}
            </div>

            {#if $currentState?.task?.additionalTasks?.listen?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.listen)}
                <div class="flex flex-col">
                    <BaseButton
                            on:click={() => switchTo(TaskMode.listen)}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconHeadphones/>
                            {$t('t.additionalTasksNavigation.listening')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState.results, TaskMode.listen)}
                        <ResultBadge result={getBestResultForMode(TaskMode.listen, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.audiodic?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.audiodic)}
                <div class="flex flex-col">
                    <BaseButton
                            on:click={() => switchTo(TaskMode.audiodic)}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconEar/>
                            {$t('t.additionalTasksNavigation.dictation')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState?.results, TaskMode.audiodic)}
                        <ResultBadge result={getBestResultForMode(TaskMode.audiodic, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.bytime?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.bytime)}
                <div class="flex flex-col">
                    <BaseButton
                            on:click={() => switchTo(TaskMode.bytime)}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconClockHour9/>
                            {$t('t.additionalTasksNavigation.translationsShort')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState.results, TaskMode.bytime)}
                        <ResultBadge result={getBestResultForMode(TaskMode.bytime, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.phantom?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.phantom)}
                <div class="flex flex-col">
                    <BaseButton
                            on:click={() => switchTo(TaskMode.phantom)}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconEyeOff/>
                            {$t('t.additionalTasksNavigation.phantomDictation')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState?.results, TaskMode.phantom)}
                        <ResultBadge result={getBestResultForMode(TaskMode.phantom, $currentState)}/>
                    {/if}
                </div>
            {/if}

            {#if $currentState?.task?.additionalTasks?.voice?.enabled}
                {@const {locked, countdown} = getLockedStateByTaskMode(TaskMode.voice)}
                <div class="flex flex-col">
                    <BaseButton
                            on:click={() => switchTo(TaskMode.voice)}
                    >
                        {#if locked}
                            <IconGift/>
                            {countdown}
                        {:else}
                            <IconMicrophone/>
                            {$t('t.additionalTasksNavigation.voice')}
                        {/if}
                    </BaseButton>
                    {#if wasTaskModeDone($currentState?.results, TaskMode.voice)}
                        <ResultBadge result={getBestResultForMode(TaskMode.voice, $currentState)}/>
                    {/if}
                </div>
            {/if}
        </div>
    {/if}
</div>

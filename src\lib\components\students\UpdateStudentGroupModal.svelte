<script lang="ts">
    import {type ModalSettings, getModalStore} from '@skeletonlabs/skeleton';
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import {StudentEditModalState} from '$lib/state/student-edit-state';
    import {IconCircleLetterX, IconDeviceFloppy} from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import SveltyPicker from 'svelty-picker';
    import {areIntervalsOverlapping, isAfter, isEqual, isWithinInterval} from 'date-fns';
    import {onDestroy, onMount} from 'svelte';
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import BaseInput from '$components/common/BaseInput.svelte';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import BreadcrumbStudentGroupModal from '$components/students/BreadcrumbStudentGroupModal.svelte';
    import GroupIntersectionError from '$components/students/GroupIntersectionError.svelte';
    import {getDateString} from '$lib/common/utils';
    import {t} from '$lib/i18n/config';
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";

    $: groupOptions = createGroupOptions();
    const modalStore = getModalStore();
    let disableSave = false;
    let minimumValueDateStartForDateStartActual: null | Date;
    let maximumValueDateEndForDateStartActual: null | Date;
    let minimumValueDateStartForDateEndActual: null | Date;
    let maximumValueDateEndForDateEndActual: null | Date;
    let messageIntersectsGroup: string;
    let isDateEndRequired = !!$GroupHistoryState.selectedGroup.dateEndActual;
    let confirmUser = false;

    const studentName = `${$StudentEditModalState.firstname} ${$StudentEditModalState.lastname}`;

    const initialDateToPicker = (initialDate: string | null) =>
        initialDate ? new Date(initialDate) : null;

    const changeDateForDateEndActual = () => {
        const selectedGroup = $GroupHistoryState.selectedGroup;
        const el = $GroupHistoryState.groups.find((group) => group.id === selectedGroup.groupId);

        if (
            selectedGroup.dateEndActual &&
            !isAfter(new Date(selectedGroup.dateEndActual), new Date(selectedGroup.dateStartActual))
        ) {
            selectedGroup.dateEndActual = getDateString(el.dateEnd);
        }

        minimumValueDateStartForDateEndActual = selectedGroup.dateStartActual || new Date(el.dateStart);
    };

    const checkDateToIntersects = () => {
        const start = $GroupHistoryState.selectedGroup.dateStartActual;
        const end = $GroupHistoryState.selectedGroup.dateEndActual;
        const overlappedGroup = checkIfIntervalOverlapping(start, end);
        if (overlappedGroup) {
            messageIntersectsGroup = `${$t('students.messageerror')} ${overlappedGroup} `;
            disableSave = true;
        } else {
            messageIntersectsGroup = '';
            disableSave ? (disableSave = true) : (disableSave = false);
        }
    };

    const createGroupOptions = () => {
        const groupsIntersecting = $GroupHistoryState.groupsHistory.filter(
            (element) => element.groupId !== $GroupHistoryState.selectedGroup.groupId
        );
        const groupsWithoutGroupsFromHistory = $GroupHistoryState.groups
            .map((element) => {
                const similarGroup = groupsIntersecting.find((g) => g.groupId === element.id);
                if (similarGroup) {
                    return null;
                } else {
                    return element.isPublic ? null : element;
                }
            })
            .filter((element) => element !== null);
        return groupsWithoutGroupsFromHistory.filter((g) => g.isActive).map((group) => {
            return {value: group.id, displayValue: group.name};
        });
    };

    const checkIfIntervalOverlapping = (start: Date, end: Date): string | null => {
        if (!start && !end) {
            disableSave = false;
            return null;
        }
        const groupsHistoryWithoutSelected = $GroupHistoryState.groupsHistory.filter(
            (gr) => gr.id !== $GroupHistoryState.selectedGroup.id
        );

        if (start && end) {
            disableSave = false;
            return checkIntervalOverlap(
                {start: new Date(start), end: new Date(end)},
                groupsHistoryWithoutSelected
            );
        } else {
            !start ? (disableSave = true) : (disableSave = false);
            return checkDateOverlap(start ? start : end, groupsHistoryWithoutSelected);
        }
    };

    const checkDateOverlap = (date: Date, studentGroups): string | null => {
        for (const studentGroupRecord of studentGroups) {
            if (!studentGroupRecord.dateStartActual || !studentGroupRecord.dateEndActual) continue;
            if (isEqual(new Date(date), new Date(studentGroupRecord.dateEndActual))) {
                continue;
            }

            return isWithinInterval(new Date(date), {
                start: new Date(studentGroupRecord.dateStartActual),
                end: new Date(studentGroupRecord.dateEndActual)
            })
                ? studentGroupRecord.group.name
                : null;
        }
    };

    const checkIntervalOverlap = (
        inputInterval: { start: Date; end: Date },
        studentGroups
    ): string | null => {
        const elementOverlapping = studentGroups.find((studentGroupRecord) =>
            areIntervalsOverlapping(
                {
                    start: new Date($GroupHistoryState.selectedGroup.dateStartActual),
                    end: new Date($GroupHistoryState.selectedGroup.dateEndActual)
                },
                {
                    start: new Date(studentGroupRecord.dateStartActual),
                    end: studentGroupRecord.dateEndActual
                        ? new Date(studentGroupRecord.dateEndActual)
                        : new Date(studentGroupRecord.dateStartActual)
                }
            )
        );
        return elementOverlapping ? elementOverlapping.group.name : null;
    };

    const onChangeSelect = async () => {
        const newSelectedGroup: GroupDto = $GroupHistoryState.groups.find(
            (group) => group.id === $GroupHistoryState.selectedGroup.groupId
        );
        const findGroupInHistory = $GroupHistoryState.groupsHistory.find(
            (groupStudent) => groupStudent.groupId === newSelectedGroup.id
        );
        $GroupHistoryState.selectedGroup.dateStartActual = findGroupInHistory
            ? getDateString(findGroupInHistory.dateStartActual)
            : getDateString(newSelectedGroup.dateStart.toString());
        $GroupHistoryState.selectedGroup.dateEndActual = findGroupInHistory
            ? getDateString(findGroupInHistory.dateEndActual)
            : getDateString(newSelectedGroup.dateEnd.toString());
        $GroupHistoryState.selectedGroup.dateStartTasks = findGroupInHistory
            ? getDateString(findGroupInHistory.dateStartTasks)
            : getDateString(newSelectedGroup.dateStart.toString());
        minimumValueDateStartForDateStartActual = newSelectedGroup?.dateStart ? new Date(newSelectedGroup.dateStart) : new Date();
        maximumValueDateEndForDateStartActual = newSelectedGroup?.dateEnd ? new Date(newSelectedGroup.dateEnd) : new Date();
        minimumValueDateStartForDateEndActual = newSelectedGroup?.dateStart ? new Date(newSelectedGroup.dateStart) : new Date();
        maximumValueDateEndForDateEndActual = newSelectedGroup?.dateEnd ? new Date(newSelectedGroup.dateEnd) : new Date();
        messageIntersectsGroup = '';
        checkDateToIntersects();
    };

    async function handleSubmit(event) {
        const data = new FormData(this);
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());

        if (result.type === 'success') {
            await invalidate('load:students');
            modalStore.close();
            NotificationStore.push({
                type: NotificationType.success,
                message: t.get('students.students.notifications.updateStudentGroup.success')
            }, 5);
        }
    }

    const onClickSelect = async () => {
        if (!$GroupHistoryState.confirm) {
            $GroupHistoryState.confirm = await new Promise<boolean>((resolve) => {
                const modal: ModalSettings = {
                    type: 'confirm',
                    title: $t('students.students.modal.editWarning.title'),
                    body: $t('students.students.modal.editWarning.body'),
                    buttonTextCancel: $t('students.students.modal.editWarning.cancel'),
                    buttonTextConfirm: $t('students.students.modal.editWarning.confirm'),
                    modalClasses: 'flex flex-col gap-10',
                    response: (r: boolean) => resolve(r)
                };
                modalStore.trigger(modal);
                let confirmModal = $modalStore.find((element) => element.type === 'confirm');
                if (confirmModal) {
                    $modalStore = $modalStore.filter((obj) => obj.type !== 'confirm');
                    $modalStore.unshift(confirmModal);
                }
            });
        }
    };

    onMount(() => {
        const {dateStartActual, dateEndActual, dateStartTasks} = $GroupHistoryState.selectedGroup;
        $GroupHistoryState.selectedGroup.dateStartActual = getDateString(dateStartActual);
        $GroupHistoryState.selectedGroup.dateEndActual = getDateString(dateEndActual);
        $GroupHistoryState.selectedGroup.dateStartTasks = getDateString(dateStartTasks);
        const selectedGroup = $GroupHistoryState.groups.find(
            (group) => group.id === $GroupHistoryState.selectedGroup.group.id
        );
        minimumValueDateStartForDateStartActual = selectedGroup?.dateStart ? new Date(selectedGroup.dateStart) : new Date();
        maximumValueDateEndForDateStartActual = selectedGroup?.dateEnd ? new Date(selectedGroup.dateEnd) : new Date();
        minimumValueDateStartForDateEndActual = $GroupHistoryState.selectedGroup.dateStartActual;
        maximumValueDateEndForDateEndActual = selectedGroup?.dateEnd ? new Date(selectedGroup.dateEnd) : new Date();
    });

    onDestroy(() => ($GroupHistoryState.confirm = false));
</script>

{#if $modalStore[0]?.component === 'updateStudentGroupModal'}
    <form
            method="POST"
            on:submit|preventDefault={handleSubmit}
            action="?/updateStudentGroup"
            class="flex flex-col modal card p-10  sm:w-2/3 lg:w-1/2 shadow-xl space-y-4"
    >
        <BreadcrumbStudentGroupModal
                currentModalName="{$t('students.students.modal.updateStudentGroupModal.edit')} {$t(
				'students.students.modal.updateStudentGroupModal.group'
			)}"
                groupName={$GroupHistoryState?.selectedGroup?.group?.name}
        />

        <div class="hidden">
            <BaseInput name="id" bind:value={$GroupHistoryState.selectedGroup.id}/>
        </div>
        <div class="hidden">
            <BaseInput name="studentId" bind:value={$StudentEditModalState.id}/>
        </div>

        <div class="flex flex-col gap-10">
            <BaseSelect
                    class="w-10"
                    name="groupId"
                    title="Group"
                    bind:value={$GroupHistoryState.selectedGroup.groupId}
                    options={groupOptions}
                    on:change={onChangeSelect}
                    on:click={onClickSelect}
            />
            <div class="flex justify-between items-center">
                <div class="flex gap-5">
                    <div class="h-28 max-w-[140px]">
						<span class="block input__title font-medium text-base mb-1"
                        >{$t('students.students.modal.updateStudentGroupModal.fields.datestart')}
						</span>
                        <SveltyPicker
                                name="dateStartActual"
                                mode="date"
                                initialDate={initialDateToPicker($GroupHistoryState.selectedGroup.dateStartActual)}
                                startDate={minimumValueDateStartForDateStartActual}
                                endDate={maximumValueDateEndForDateStartActual}
                                bind:value={$GroupHistoryState.selectedGroup.dateStartActual}
                                inputClasses="w-full input rounded h-10 p-2"
                                todayBtn={false}
                                on:change={() => {
								changeDateForDateEndActual();
								checkDateToIntersects();
							}}
                        />
                    </div>
                    <div class="h-28 max-w-[140px]">
						<span class="block input__title font-medium text-base mb-1"
                        >{$t('students.students.modal.updateStudentGroupModal.fields.taskstart')}
						</span>
                        <SveltyPicker
                                name="dateStartTasks"
                                mode="date"
                                initialDate={initialDateToPicker($GroupHistoryState.selectedGroup.dateStartTasks)}
                                bind:value={$GroupHistoryState.selectedGroup.dateStartTasks}
                                inputClasses="w-full input rounded h-10 p-2"
                                todayBtn={false}
                        />
                    </div>
                </div>
                <div class="h-28 max-w-[140px] ">
					<span class="block input__title font-medium text-base mb-1">
						{$t('students.students.modal.updateStudentGroupModal.fields.dateend')}
					</span>
                    <SveltyPicker
                            name="dateEndActual"
                            mode="date"
                            required={isDateEndRequired}
                            initialDate={initialDateToPicker($GroupHistoryState.selectedGroup.dateEndActual)}
                            startDate={minimumValueDateStartForDateEndActual}
                            bind:value={$GroupHistoryState.selectedGroup.dateEndActual}
                            inputClasses="w-full input rounded h-10 p-2"
                            todayBtn={false}
                            on:change={checkDateToIntersects}
                    />
                </div>
            </div>
        </div>
        {#if messageIntersectsGroup}
            <GroupIntersectionError intersectionError={messageIntersectsGroup}/>
        {/if}
        <div class=" flex justify-between">
            <BaseButton disabled={disableSave} type="submit">
                <IconDeviceFloppy/>
                {$t('students.buttons.save')}
            </BaseButton>
            <BaseButton on:click={() => modalStore.close()}>
                <IconCircleLetterX/>
                {$t('students.buttons.cancel')}
            </BaseButton>
        </div>
    </form>
{/if}

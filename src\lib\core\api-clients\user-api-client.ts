import type { TableDataDto } from '$common/models/dtos/table-data.dto';
import type { UserDto } from '$common/models/dtos/user.dto';
import { UserFilterState } from '$lib/state/user-filter-state';
import { get } from 'svelte/store';
import { UserPagingState } from '$lib/state/user-paging-state';
import { BaseApiClient } from '$lib/core/api-clients/base-api-client';

export class UserApiClient extends BaseApiClient {
	constructor() {
		super();
	}

	public getUsers = async (): Promise<TableDataDto<UserDto>> => {
		const { search } = get(UserFilterState);
		const { take, skip } = get(UserPagingState);
		return await this.getDataOrThrow(`/api/users?take=${take}&skip=${skip}&search=${search}`);
	};
}

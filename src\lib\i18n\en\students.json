{"tabs": {"requests": "Requests", "students": "Students"}, "requests": {"table": {"head": {"tz": "TZ", "fullname": "Fullname", "email": "Email", "whatsapp": "Whatsapp", "groupStartDate": "Group start", "learnStartDate": "Study start", "lang": "<PERSON>", "level": "Level", "action": "Action", "application": "Application date"}, "handleButton": "<PERSON><PERSON>", "deleteButton": "Delete"}, "modal": {"createTitle": "Create Student Request", "formFields": {"titles": {"city": "City", "groupLevel": "Group Level", "groupLang": "Group Language", "groupStartDate": "Group Start Date", "learnStartDate": "Learn Start Date"}}, "formFieldsErrors": {"city": "City is required", "groupLevel": "Group level is required", "groupLang": "Group language is required", "groupStartDate": "Group start date is required", "learnStartDate": "Learn start date is required"}, "buttons": {"create": "Create", "cancel": "Cancel"}, "previousmodalname": "Student acceptance", "incompletegroup": "This student has an incomplete group, please set a date to complete the group.", "studentTitle": "Student data from request", "groupTitle": "Group settings for new student", "groupSubtitle": "Group settings for new student", "levelSubtitle": "Chosen level is", "langSubtitle": "and group language is", "groupStartSubtitle": "chosen group date start is", "submit": "Save", "cancel": "Cancel", "group": "Group", "dob": "Date of birth", "groupStart": "Start learning", "tasksStart": "Tasks from"}, "modalDelete": {"title": "Are you sure?", "body": "This action will remove the student request", "buttonTextCancel": "Cancel", "buttonTextConfirm": "Delete", "notification": {"success": "The student's request has been successfully deleted", "cancel": "Student request deletion canceled", "error": "Error occurred!"}}, "notifications": {"handle": {"success": "The student was successfully added"}, "create": {"success": "Student request was successfully created"}}, "addRequestButton": "Add Request"}, "students": {"filters": {"search": "Search", "isActive": "Only Is Active", "info": "Full info", "placeHolderSearch": "Search"}, "table": {"head": {"tz": "TZ", "fullname": "Fullname", "email": "Email", "whatsapp": "Whatsapp", "group": "Group", "action": "Action", "regDate": "Date of registration", "startInGroupDate": "Start date in the group", "comment": "Comments", "lastTaskResult": "The last result", "averageTaskResult": "Average task result", "lastTaskDelay": "Last task delay", "averageTaskDelay": "Average task delay", "statistics": "Statistics"}, "hours": "Hours", "days": "Days", "editButton": "Edit", "copyButton": "Copy link", "duplicateButton": "Duplicate task"}, "modal": {"previousmodalname": "Update a student", "title": "Update a student", "submit": "Save", "cancel": "Cancel", "statistics": "Statistics", "totalHours": "Total hours of study", "totalDays": "Total days of study", "fields": {"lastname": "Lastname", "firstname": "Firstname", "dob": "Date of birth", "phone": "Phone", "email": "Email", "whatsapp": "Whatsapp", "comment": "Comment", "tz": "<PERSON><PERSON><PERSON> zehut", "photo": "Photo", "teudatOle": "<PERSON><PERSON><PERSON>", "city": "City", "groupLevel": "Group Level", "groupLang": "Group Language", "groupStartDate": "Group Start Date", "learnStartDate": "Learn Start Date"}, "pastDataDescription": "If a student has taken our courses before, their past details are marked with an *", "formFieldsErrors": {"tz": "TZ Must contain 6 to 12 characters", "firstname": "The firstname must contain at least 2 characters", "lastname": "The lastname must contain at least 2 characters", "email": "The student email must be correct", "phone": "The student phone must be correct", "whatsapp": "The whatsapp phone must be correct", "dob": "The date of birth must not be empty"}, "groupsHistory": {"switch": "Switch group", "stop": "Stop", "table": {"totalDays": "Total days", "totalHours": "Total hours", "endlearning": "End learning", "starttasks": "Start tasks", "startlearning": "Start learning", "group": "Group"}}, "updateStudentGroupModal": {"edit": "Edit", "group": "group", "fields": {"group": "Group", "datestart": "Date start", "taskstart": "Task Start", "dateend": "Date end"}}, "switchStudentGroupModal": {"breadcrumb": "Switch student group", "fromgroup": "From group", "fields": {"dateend": "Date end", "tonewgroup": "To new group", "datestart": "Date start", "taskstart": "Task Start"}}, "stopStudentGroupModal": {"stop": "Stop", "group": "group", "title": "What is the ending date?", "fields": {"dateend": "Date end"}}, "deleteStudentGroupModal": {"delete": "Delete", "group": "group", "title": "Are you sure?", "buttons": {"save": "Yes", "cancel": "No"}}, "editWarning": {"title": "Are you sure?", "body": "This change will affect the student's group history", "cancel": "cancel", "confirm": "confirm"}}, "notifications": {"edit": {"success": "Student successfully updated"}, "updateStudentGroup": {"success": "The student's group has been successfully updated"}, "deleteStudentGroup": {"success": "The student's group has been successfully deleted"}, "stopStudentGroup": {"success": "The student's group has been successfully stopped"}, "switchStudentGroup": {"success": "The student's group has been successfully switched"}}}, "modalUsers": {"titles": {"titleToCreate": "Create new user", "titleToUpdate": "Change data user"}}, "buttons": {"save": "Save", "cancel": "Cancel"}, "messageerror": "On these dates, the student already has classes in group"}
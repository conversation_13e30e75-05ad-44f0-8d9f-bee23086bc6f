<script lang="ts">
    import {t} from '$lib/i18n/config';
    import {getDrawerStore, LightSwitch, ListBox, ListBoxItem} from '@skeletonlabs/skeleton';
    import LanguageSelect from '../common/LanguageSelect.svelte';
    import ThemeSwitcher from '../common/ThemeSwitcher.svelte';
    import {IconHome, IconLogout2} from '@tabler/icons-svelte';
    import {page} from '$app/stores';
    import {ForbiddenByRoleRoutes} from '$common/core/routes';
    import BaseButton from '$components/common/BaseButton.svelte';

    const drawerStore = getDrawerStore();
    const role = $page.data?.user?.role;
    const menuItems = [
        {translationKey: 'students', url: '/students', prefetch: true},
        {translationKey: 'users', url: '/users', prefetch: true},
        {translationKey: 'sentences', url: '/sentences', prefetch: true},
        {translationKey: 'groups', url: '/groups', prefetch: true},
        {translationKey: 'tasks', url: '/tasks', prefetch: false},
        {translationKey: 'incentive', url: '/incentive', prefetch: false},
        {translationKey: 'campaigns', url: '/campaigns', prefetch: true},
        {translationKey: 'complaints', url: '/complaints', prefetch: true},
        {translationKey: 'settings', url: '/settings', prefetch: true},
    ];

    $:console.log($page.data.envs.VITE_TABLES_URL)

    const getMenuItemsByRole = () =>
        menuItems.filter((route) => !ForbiddenByRoleRoutes[role]?.some((x) => x === route.url));

    $: classesActive = (href: string) =>
        href === $page.url.pathname ? '!bg-primary-300 dark:!bg-transparent dark:border-2 dark:border-primary-500  font-bold' : '';
</script>


<div id="aside-bar" class="p-2 h-full bg-surface-200-700-token shadow-lg !rounded-none w-full">
    <ListBox class="h-full">
        <div class="flex flex-col h-full">
            {#if $page?.data?.user?.firstname?.length > 1}
                <div class="mt-5 mb-5 break-words w-full  md:flex flex-wrap lg:hidden px-3" dir="auto">
                    <pre class="mx-2">👋</pre>
                    <pre>{$page?.data?.user?.firstname} {$page?.data?.user?.lastname}</pre>
                </div>
                <div class="lg:hidden md:flex">
                    <hr/>
                </div>
            {/if}
            <div class="sm:flex lg:hidden w-full flex-row justify-center gap-x-2 my-5" dir="ltr">
                <LightSwitch width="w-[70px]" class="z-[999]" dir="ltr"/>
            </div>
            <div class="lg:hidden md:flex">
                <hr/>
            </div>
            <div class="flex-1 flex flex-col gap-1 mt-5">
                <a href={'/'} on:click={() => drawerStore.close()}>
                    <ListBoxItem
                            name="home"
                            group="home"
                            value="home"
                            class="!p-2 bg-transparent font-semibold dark:!bg-transparent border-2 border-transparent  !text-primary hover:bg-primary-300 dark:hover:border-2 dark:hover:border-primary-500 {classesActive(
							'/'
						)}"
                            rounded="rounded"
                            selected={$page.url.pathname === '/'}
                    >
                        <span class="flex flex-row gap-2 items-center">
                            <IconHome size={22} style="display: initial"/>
                            <span class="text-base">{$t(`common.menu.home`)}</span>
                        </span>
                    </ListBoxItem>
                </a>
                {#each getMenuItemsByRole() as menuItem}
                    <a
                            href={menuItem.url}
                            target={menuItem.blank?'_blank':''}
                            sveltekit:prefetch={menuItem.prefetch}
                            on:click={() => drawerStore.close()}

                    >
                        <ListBoxItem
                                name={menuItem.translationKey}
                                group={menuItem.translationKey}
                                value={menuItem.translationKey}
                                href={menuItem.url}
                                class="!p-2 bg-transparent  dark:!bg-transparent font-semibold !text-primary hover:bg-primary-300 border-2 border-transparent dark:hover:border-2 dark:hover:border-primary-500 {classesActive(
								menuItem.url
							)}"
                                rounded="rounded"
                        >
                            <span class="text-base">{$t(`common.menu.${menuItem.translationKey}`)}</span>
                        </ListBoxItem>
                    </a>
                {/each}
                <a on:click={() => drawerStore.close()}
                   href={$page.data.envs.VITE_TABLES_URL || 'https://tables.ulpanmorasha.com'} target="_blank"
                   class="!p-2 rounded bg-transparent dark:!bg-transparent font-semibold !text-primary hover:bg-primary-300 border-2 border-transparent dark:hover:border-2 dark:hover:border-primary-500"
                >
                    <span class="text-base">{$t(`common.menu.tables`)}</span>
                </a>
            </div>

            <div class="flex flex-col pl-2 gap-y-1">
                <ThemeSwitcher/>
                <LanguageSelect/>
                <hr class="my-3"/>
                <div class="">
                    <form class="self-end" method="post" action="/api/logOut">
                        <BaseButton type="submit" className=" rounded p-0 h-7 w-full bg-none">
							<span>
								<IconLogout2 size={20}/>
							</span>
                        </BaseButton>
                    </form>
                </div>
                <div class="flex flex-col  w-full items-center justify-center text-xs">
                    <p>All rights reserved</p>
                    <p> © מרשת שרה</p>
                </div>
            </div>
        </div>
    </ListBox>
</div>

import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import type {ExclusionsDto} from "$common/models/dtos/Exclusions.dto";
import type {TableDataDto} from "$common/models/dtos/table-data.dto";


export class ExclusionsApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getExclusions = async (): Promise<TableDataDto<ExclusionsDto>> => {
        return await this.getDataOrThrow(`/api/exclusions`);
    };

    public updateExclusions = async (data: ExclusionsDto) => {
        return await this.putDataOrThrow('/api/exclusions', data)
    }

    public deleteExclusions = async (id: string) => {
        return await this.deleteOrThrow(`/api/exclusions?id=${id}`)
    }
}
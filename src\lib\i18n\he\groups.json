{"title": "כיתות", "new": "ח<PERSON><PERSON>", "table": {"head": {"name": "שם", "dateStart": "תאריך התחלה", "language": "שפה", "level": "רמה", "days": "שעות לימוד", "hoursSchedule": "משמרת", "whatsapp": "WhatsApp-קיש<PERSON><PERSON> ל ", "comment": "הערה", "isActive": "סטטוס", "studentsCount": "מספר תלמידים", "statistics": "Статистика"}}, "filters": {"title": {"groupName": "שם כיתה", "active": "השתתפות", "type": "סוג", "lang": "שפה", "level": "רמה", "hoursSchedule": "משמרת"}, "values": {"hoursSchedule": {"morning": "<PERSON><PERSON><PERSON><PERSON>", "day": "צהריים", "evening": "ערב"}, "isActive": {"active": "פעיל", "inActive": "לא פעיל"}, "type": {"public": "sandbox", "ragil": "רגיל"}}}, "modal": {"title": {"create": "כיתה חדשה", "update": "עד<PERSON><PERSON><PERSON> קבוצה"}, "buttons": {"submit": "<PERSON><PERSON><PERSON> חדש", "cancel": "ביטול"}, "formFields": {"titles": {"hoursSchedule": "משמרת", "startTime": "<PERSON><PERSON><PERSON> התחלה", "endTime": "<PERSON><PERSON><PERSON> סיום ", "capacity": "מספר שעות לימוד", "dateEnd": "תאריך סופי", "dateStart": "תאריך התחלה", "lang": "שפה", "level": "רמה", "sandbox": "sandbox", "whatsAppLink": "WhatsApp-קיש<PERSON><PERSON> ל", "comment": "הערות", "name": "שם", "active": "סטטוס", "totalHoursAmount": "Общее количество часов", "hoursSpendBySession": "Часы на сеанс"}, "values": {"hoursSchedule": {"morning": "<PERSON><PERSON><PERSON><PERSON>", "day": "צהריים", "evening": "ערב"}}}, "formFieldsErrors": {"daysSchedule": "בחר יום אחד לפחות", "timeStart": "חו<PERSON>ה למלא זמן התחלה", "timeEnd": "חו<PERSON>ה למלא זמן סיום", "dateStart": "חובה למלא תאריך התחלה ", "dateEnd": "חובה למלא תאריך סיום", "timeStartMoreThenTimeEnd": "שעת תחילת השיעור חייבת לקדום לשעת סיומו", "dateStartMoreThenDateEnd": "תאריך תחילת הקורס חייב לקדום לתאריך סיומו", "totalHoursAmountMin": "Общее количество часов не может быть меньше 10.", "totalHoursAmountMax": "Общее количество часов не может превышать 1000.", "hoursSpendBySessionMin": "Ча<PERSON>ы, потраченные на сеанс, не могут быть меньше 1", "hoursSpendBySessionMax": "Продолжительность сеанса не может превышать 10 часов.", "totalHoursAmountInvalidType": "Общее количество часов должно быть числом!", "hoursSpendBySessionInvalidType": "Часы, потраченные на сеанс, должны быть числом!"}}, "scheduleTable": {"title": "Изменения расписания", "tableTitles": {"date": "Дата", "days": "<PERSON><PERSON>и", "hoursPerSession": "Время занятия", "action": "Действие"}, "buttons": {"edit": "Редактировать"}, "modal": {"title": {"create": "Создать расписание", "update": "Обновить расписание"}, "formFields": {"date": "Дата", "days": "<PERSON><PERSON>и", "hoursPerSession": "Время занятия"}, "buttons": {"create": "Создать", "update": "Обновить", "cancel": "Отмена"}}, "notifications": {"create": "Расписание успешно создано", "update": "Расписание успешно обновлено"}}, "tabsName": {"tasks": "תרגילים", "students": "תלמידים", "settings": "הגדרות", "history": "אר<PERSON><PERSON><PERSON><PERSON> הקבוצה"}, "groupSettings": {"stop": "סגור כיתה", "modal": {"stop": "Отсановить группу", "buttons": {"cancel": "Отмена", "save": "Сохранить"}, "notifications": {"success": "Группа была успешно остановлена"}}, "holidayTable": {"title": "Выходные", "typeLocal": "Локальный", "typeGlobal": "Глобальный", "tableTitles": {"date": "Дата", "comment": "Комментарий", "type": "Тип", "action": "Действие"}, "buttons": {"delete": "Удалить", "save": "Сохранить"}}}, "infoGroup": {"hoursSchedule": {"morning": "<PERSON><PERSON><PERSON><PERSON>", "day": "צהריים", "evening": "ערב"}, "isActive": {"active": "פעיל", "inActive": "לא פעיל"}, "isPublic": {"public": "קבוצת דמה", "nonpublic": "רגיל"}, "hours": "<PERSON>а<PERSON>ы", "days": "<PERSON><PERSON>и", "progress": "Прогресс"}, "studentAdditionalTask": {"title": "תרגילים", "table": {"name": "שם", "comment": "הערה", "done": "נעשה אחרי", "result": "ציון", "attempts": "ניסיונות", "actions": "פעולות"}, "breadcrumb": {"students": "תלמידים"}}, "groupHistory": {"table": {"name": "שם מלא", "group": "כיתה", "regDate": "תאריך רישום", "start": "תאריך התחלה", "end": "תאריך סיום", "taskStart": "תאריך שליחת התרגיל", "comment": "הערה", "lastTaskScore": "<PERSON><PERSON><PERSON><PERSON> אחרון", "averageTaskScore": "ציון ממוצע", "lastTaskDelay": "<PERSON><PERSON><PERSON> ביצוע של תרגיל אחרון", "averageTaskDelay": "<PERSON><PERSON><PERSON> ביצוע תרגיל ממוצע", "whatsapp": "Whatsapp", "statistics": "Статистика", "action": "טיפול"}}, "notifications": {"create": {"success": "Создание группы прошло успешно"}, "update": {"success": "Обновление группы прошло успешно"}}, "edit": "ערוך קבוצה", "groups": "קבוצות", "info": "פרטי הקבוצה", "comment": "הערה"}
import { writable } from 'svelte/store';
import type {BaseSortingDto} from "$common/models/filters/base-filter.dto";

export const pageSize = 25;
export const initialGroupPaging = {
	take: pageSize,
	skip: 0
};
export const GroupPagingState = writable({...initialGroupPaging});

export const initialGroupSorting : BaseSortingDto = {
	sortBy: undefined,
	sortDir: 'asc',
};
export const GroupSortingState = writable({...initialGroupSorting});
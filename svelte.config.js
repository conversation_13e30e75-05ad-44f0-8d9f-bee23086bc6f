import adapter from '@sveltejs/adapter-node';
import { vitePreprocess } from '@sveltejs/kit/vite';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://kit.svelte.dev/docs/integrations#preprocessors
	// for more information about preprocessors
	preprocess: vitePreprocess(),

	kit: {
		adapter: adapter(),
		alias: {
			$api: 'src/api',
			'$api/*': 'src/api/*',
			$components: 'src/lib/components',
			'$components/*': 'src/lib/components/*',
			$common: 'src/common',
			'$common/*': 'src/common/*'
		}
	}
};
export default config;

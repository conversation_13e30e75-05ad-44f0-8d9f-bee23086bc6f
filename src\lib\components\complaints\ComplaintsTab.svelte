<script lang="ts">
    import {t} from "$lib/i18n/config.js";
    import CountSpan from "$components/common/CountSpan.svelte";
    import ComplaintsFilter from "$components/complaints/ComplaintsFilter.svelte";
    import ComplaintsTable from "$components/complaints/ComplaintsTable.svelte";
    import {ComplaintsPagingState, pageSize} from "$lib/state/complaints-paging-state.js";
    import {loadingWrap} from "$lib/common/utils.js";
    import {invalidate} from "$app/navigation";
    import {ComplaintApiClient} from "$lib/core/api-clients/complaint-api.client.js";
    import NotificationState from "$lib/state/notification-state.js";
    import {NotificationType} from "$common/models/enums.js";

    export let count;

    export let complaints;

    export let updaters;


    const handleComplaint = async (taskId: string, sentenceId: string, isHandled: boolean) => {
        if (isHandled) return;
        const data = await new ComplaintApiClient().handledComplaint(taskId, sentenceId);
        if (data.success) {
            NotificationState.push({
                type: NotificationType.success,
                message: $t('complaints.notifications.handled.success')
            });
            await invalidate('load:complaints')
        } else {
            NotificationState.push({
                type: NotificationType.error,
                message: $t('complaints.notifications.handled.error')
            });
        }
    }

    const loadMore = async () => {
        if (count > complaints.length) {
            ComplaintsPagingState.set({
                skip: 0,
                take: pageSize + complaints.length
            });
            await loadingWrap(async () => {
                await invalidate('load:complaints');
            });
        }
    };
</script>

<div class="h-[calc(100vh-160px)] overflow-hidden flex flex-col">
    <h1 class="title mb-1 font-medium text-xl">
        {$t('complaints.title')}
        <CountSpan bind:count={count}/>
    </h1>
    <ComplaintsFilter {complaints} {updaters}/>

    <div class="mt-3 overflow-y-auto overflow-x-auto ">
        <ComplaintsTable loadMoreFunc={loadMore} {complaints} {handleComplaint}/>
    </div>
</div>

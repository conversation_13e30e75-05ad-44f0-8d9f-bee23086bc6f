<script lang="ts">
    import {
        getBestTranslationResult,
        getTaskModesDone,
        getTotalEnabledTaskModesCount
    } from '$lib/common/task-helpers';
    import ResultBadge from '$components/t/ResultBadge.svelte';
    import {page} from '$app/stores';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {ResultPopupDetailsState} from '$lib/state/result-popup-details.state';
    import {IconEye, IconSend} from '@tabler/icons-svelte';
    import {goto} from '$app/navigation';
    import {createEventDispatcher, onMount} from 'svelte';
    import _ from "lodash";

    export let students;
    export let task;

    export let findStudentsRelatedToTask;


    let studentsWithoutResults = [];


    const dispatch = createEventDispatcher();
    const modalStore = getModalStore();

    onMount(() => {
        studentsWithoutResults = findStudentsRelatedToTask(_.differenceWith(students, task.results, (student, result) => student.id === result.studentId), task);
    })

    const openDetailsPopup = (r, name, wh) => {
        ResultPopupDetailsState.set({...r, name, whatsapp: wh})
        modalStore.trigger({type: 'component', component: 'resultDetailsPopup'});
    };
</script>

<td class="!bg-transparent"></td>
<td class="!bg-transparent"></td>
<td class="!bg-transparent"></td>
<td colspan="4" class="!p-0 !mx-10 table-compact">
    <table class="table !p-0 !m-0 !table-compact">
        <tbody>
        {#each task.results as r}
            {@const student = students.find((x) => x.id === r.studentId)}
            <tr class="!m-0 !p-0 cursor-pointer h-fit">
                <td class="w-1/4" on:click={() => goto(`${$page.url.origin}${$page.url.pathname}/${student.id}`)}>
                    <div class="w-full flex font-medium !p-1 !m-0 text-blue-600 dark:text-blue-400 hover:underline badge text-sm btn btn-ghost">
                        {`${student?.firstname} ${student?.lastname}`}
                    </div>
                </td>
                <td class="w-[20%]">
                    <ResultBadge result={r?.currentScore ?? 0}/>
                </td>
                <td class="w-[20%] justify-center">
                    {getTaskModesDone(r?.results).length}/{getTotalEnabledTaskModesCount(task)}
                </td>
                <td class="w-[20%] justify-between text-center mx-10">
                    <BaseButton
                            className="w-1/2"
                            size="sm"
                            on:click={() => openDetailsPopup(getBestTranslationResult(r.results), `${student?.firstname} ${student?.lastname}`, student?.whatsApp)}
                    >
                        <IconEye size={20} stroke="1.5"/>
                    </BaseButton>

                    <BaseButton
                            on:click={() => dispatch('openSendNotificationStudentModal', { student })}
                            size="sm"
                    >
                        <IconSend size={20} stroke="1.5"/>
                    </BaseButton>
                </td>
            </tr>
        {/each}
        {#each studentsWithoutResults as swr}
            <tr class="!m-0 !p-0 cursor-pointer h-fit !bg-warning-50-900-token">
                <td class="w-1/4" on:click={() => goto(`${$page.url.origin}${$page.url.pathname}/${swr.id}`)}>
                    <div class="w-full flex font-medium !p-1 !m-0 text-blue-600 dark:text-blue-400 hover:underline badge text-sm btn btn-ghost">
                        {`${swr?.firstname} ${swr?.lastname}`}
                    </div>
                </td>
                <td class="w-[20%]">
                    <ResultBadge result={0}/>
                </td>
                <td class="w-[20%] justify-center">
                    0/{getTotalEnabledTaskModesCount(task)}
                </td>
                <td class="w-[20%] justify-between text-center mx-10">
                    <BaseButton
                            className="w-1/2"
                            size="sm"
                            disabled="true"
                    >
                        <IconEye size={20} stroke="1.5"/>
                    </BaseButton>

                    <BaseButton
                            on:click={() => dispatch('openSendNotificationStudentModal', { student: swr })}
                            size="sm"
                    >
                        <IconSend size={20} stroke="1.5"/>
                    </BaseButton>
                </td>
            </tr>
        {/each}
        </tbody>
    </table>
</td>

<style>
    table td {
        vertical-align: middle;
    }
</style>

<script>
    import BaseInput from '../common/BaseInput.svelte';
    import BaseButton from '../common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import _ from 'lodash';
    import {get} from 'svelte/store';
    import {StudentsRequestsFilterState} from "$lib/state/students-requests-filter.state";
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {StudentRequestCreateModalState} from '$lib/state/student-request-create-state';
    import {IconPlus} from '@tabler/icons-svelte';

    const modalStore = getModalStore();

    let inputValue = '';

    const onInput = _.debounce(
        () => StudentsRequestsFilterState.set({...get(StudentsRequestsFilterState), search: inputValue}),
        1000
    );

    const openCreateStudentRequestModal = () => {
        StudentRequestCreateModalState.set({
            tz: '',
            firstname: '',
            lastname: '',
            email: '',
            phone: '',
            whatsapp: '',
            dob: '',
            city: '',
            groupLevel: '',
            groupLang: '',
            groupStartDate: '',
            learnStartDate: ''
        });
        modalStore.trigger({type: 'component', component: 'createStudentRequestModal'});
    };
</script>

<div class="flex flex-col min-h-[80px]">
    <div class="flex flex-row gap-10 items-center w-full justify-between">
        <div class="flex gap-x-5">
            <div class="flex w-72">
                <BaseInput
                        name="search"
                        placeHolder={$t('students.students.filters.placeHolderSearch')}
                        on:input={onInput}
                        bind:value={inputValue}
                        title={$t('students.students.filters.search')}
                />
            </div>
        </div>
        <div class="mt-0">
            <div dir="ltr" class="w-full">
                <BaseButton on:click={openCreateStudentRequestModal} size="md">
                    {$t('students.requests.addRequestButton')}
                    <IconPlus/>
                </BaseButton>
            </div>
        </div>
    </div>
</div>

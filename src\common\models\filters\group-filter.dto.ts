import type {BaseFilterDto} from "./base-filter.dto";

export interface GroupFilterDto extends BaseFilterDto {
    level: number;
    name: string;
    lang: 'EN' | 'RU' | -1;
    isPublic: number;
    isActive: number;
    hoursSchedule: number;
}

export const initialGroupFilter: GroupFilterDto = {
    level: -1,
    name: '',
    lang: -1,
    isActive: 1,
    isPublic: 0,
    hoursSchedule: -1,
    take: 25,
    skip: 0
};

export const initialGroupFilterAll: GroupFilterDto = {
    take: -1,
    skip: 0,
    name: '',
    isActive: -1,
    isPublic: -1,
    hoursSchedule: -1,
    level: -1,
    lang: -1
}

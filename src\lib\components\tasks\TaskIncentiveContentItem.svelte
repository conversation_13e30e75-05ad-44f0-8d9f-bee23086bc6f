<script lang="ts">
    import {t} from '$lib/i18n/config.js';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import BaseSwitch from '$components/common/BaseSwitch.svelte';
    import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
    import ContentEditor from '$components/content/ContentEditor.svelte';
    import type {TaskIncentiveContentWithContentDto} from '$common/models/dtos/task-incentive-content-with-content.dto';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import {fade} from 'svelte/transition';
    import LexicalContent from "$components/common/LexicalContent.svelte";

    export let incentiveContent: IncentiveContentDto[];

    export let isLexical;

    export let incentiveContentId: string;
    $: incentiveContentOptions = createOptions($CurrentEditableState.task_incentive_content);
    export let selectValue: string;
    export let checkedValue: boolean;
    export let content;
    export let scoreThreshold;
    export let index: number;
    export let deleteItem: (id: number) => void;


    const createOptions = (allSelected: TaskIncentiveContentWithContentDto[]) => {
        const incentiveContentWithoutSelected = incentiveContent
            .map((element) => {
                const similar = allSelected?.find((s) => s.incentive_content.id === element.id);
                if (similar) {
                    return null;
                } else {
                    return element;
                }
            })
            .filter((element) => element !== null);
        const currentSelectedElement = incentiveContent.find(
            (element) => element.id === incentiveContentId
        );
        return [{...currentSelectedElement}, ...incentiveContentWithoutSelected]?.map((x) => ({
            displayValue: x.name || '',
            value: x?.id || ''
        }));
    };

    const onChangeSelect = () => {
        const foundElement = incentiveContent.find((element) => element.id === selectValue);
        if (foundElement) {
            content = foundElement.page_content ? foundElement?.page_content : foundElement.value;
            incentiveContentId = foundElement.id;
        }
    };

</script>

<div class="flex flex-col">
    <div class="flex justify-between items-end">
        <div class="flex gap-5 items-end min-w-[400px]">
            <BaseSelect
                    title={$t('tasks.additionalTask.incentiveContent.select')}
                    options={incentiveContentOptions}
                    bind:value={selectValue}
                    on:change={onChangeSelect}
            />
        </div>
        <div class="flex items-center gap-5">
            <div class="flex items-center gap-2">
                <span class="text-lg">{$t('tasks.additionalTask.incentiveContent.points')}</span>
                <input
                        bind:value={scoreThreshold}
                        min={0}
                        max={100}
                        type="number"
                        class="w-full input rounded p-2 remove-arrow"
                />
                <span class="text-lg">{$t('tasks.additionalTask.incentiveContent.from')}</span>
            </div>
            <div>
                <BaseSwitch bind:checked={checkedValue}/>
            </div>
        </div>
    </div>

    <div class="mt-5">
        {#if content?.content_items?.length > 0 }
            <LexicalContent
                    isReadMode={true}
                    bind:content={content}
            />
        {:else}
            {#key content}
                <div transition:fade class=" min-h-[300px]">
                    <ContentEditor readMode={true} {content}/>
                </div>
            {/key}
        {/if}


    </div>

    <div class="mt-5 mr-5">
        <BaseButton on:click={() => deleteItem(index)}>Delete</BaseButton>
    </div>
</div>

<style lang="scss">
  .remove-arrow::-webkit-inner-spin-button,
  .remove-arrow::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .remove-arrow {
    -moz-appearance: textfield;
  }
</style>

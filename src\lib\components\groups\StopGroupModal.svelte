<script lang="ts">
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import SveltyPicker from 'svelty-picker';
    import BaseInput from '$components/common/BaseInput.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconCircleLetterX, IconDeviceFloppy} from '@tabler/icons-svelte';
    import {GroupEditModalState} from '$lib/state/group-edit-state';
    import {t} from "$lib/i18n/config";
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";

    let stopDate: string;
    const modalStore = getModalStore();
    $: disableSave = !stopDate;

    async function handleSubmit(event) {
        const data = new FormData(this);
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'success') {
            await invalidate('load:groups/id');
            modalStore.close();
            NotificationStore.push({
                type: NotificationType.success,
                message: t.get('groups.groupSettings.modal.notifications.success')
            }, 5);
        }
    }
</script>

<div class="rounded-xl modal card  sm:w-2/3 lg:w-1/3 shadow-xl space-y-4 p-5">
    <form
            on:submit|preventDefault={handleSubmit}
            method="POST"
            action="?/stopGroup"
            class="flex flex-col items-center justify-between gap-10 p-2"
    >
        <label for="id" class="hidden">
            <BaseInput name="id" bind:value={$GroupEditModalState.id}/>
        </label>
        <header class="text-xl font-bold self-start flex gap-2">
            <div>
                {$t('groups.groupSettings.modal.stop')}
            </div>
            <div>
                {$GroupEditModalState.name}
            </div>
        </header>
        <SveltyPicker
                format="yyyy-mm-dd"
                weekStart={1}
                startDate={new Date($GroupEditModalState.dateStart)}
                required={true}
                name="dateEnd"
                mode="date"
                bind:value={stopDate}
                inputClasses="relative w-full input rounded h-10 p-2"
                todayBtn={false}
        />
        <div class="flex justify-between w-full">
            <BaseButton disabled={disableSave} type="submit">
                <IconDeviceFloppy/>
                {$t('groups.groupSettings.modal.buttons.save')}
            </BaseButton>
            <BaseButton on:click={() => modalStore.close()}>
                <IconCircleLetterX/>
                {$t('groups.groupSettings.modal.buttons.cancel')}
            </BaseButton>
        </div>
    </form>
</div>

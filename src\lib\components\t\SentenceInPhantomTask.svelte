<script lang="ts">
    import BaseInput from '$components/common/BaseInput.svelte';
    import {IconEye, IconLanguage} from '@tabler/icons-svelte';
    import {popup} from '@skeletonlabs/skeleton';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher} from 'svelte';
    import type {TranslationCompletion} from '$common/models/dtos/task.dto';
    import He from "$components/common/He.svelte";
    import {page} from "$app/stores";

    const dispatcher = createEventDispatcher();

    export let hintEnabled = true;
    export let time = 5;

    $:console.log(time)
    export let sentence: TranslationCompletion;
    export let index: number;
    export let revealMode = false;

    export let disabled = false;

    let flashTaskText = false;
    let alreadyUsedFlash = false;

	$: if (revealMode) {
		alreadyUsedFlash = false;
		flashTaskText = false;
	}

    const handleFlash = () => {
        if (!alreadyUsedFlash) {
            flashTaskText = true;
            setTimeout(() => flashTaskText = false, time )
        }
        alreadyUsedFlash = true;
    };

    function createPopUpSettings(index) {
        return {
            event: 'click',
            target: `item-${index}`,
            placement: 'top'
        };
    }
</script>

<div class="w-full flex flex-col gap-2 mb-5">
    <div dir="rtl" class="w-full break-all flex items-center">
        <button
                use:popup={createPopUpSettings(index)}
                on:click={() => {
                {disabled}
				if (!revealMode) sentence.hintUsedTimes++;
			}}
                disabled={!hintEnabled}
        >
            <IconLanguage/>
        </button>
        <div class="px-5 py-2 bg-white border-black border-[1px] rounded font-semibold text-black z-50 tracking-wider"
             data-popup="item-{index}">
            <He>{sentence.correctValue}</He>
            <div class="arrow bg-white border-black border-[1px]"></div>
        </div>
        {#if flashTaskText || revealMode}
            <b dir="auto">{`${index}. ${sentence.correctValue}`}</b>
        {:else}
            <p class="ml-1 mr-3">{index}.</p>
            <BaseButton size="sm" on:click={handleFlash} disabled={alreadyUsedFlash} className="h-8 mx-2">
                <IconEye size="20"/>
                SHOW
            </BaseButton>
        {/if}
    </div>
    <div class="relative">
        <He>
            {#if revealMode && sentence.mistakes.length > 0}
                <div class="top-[-25px] right-0 text-green-700 break-words" dir="auto">
                    <He>{sentence.correctValue}</He>
                </div>
            {/if}
            <BaseInput
                    bind:value={sentence.answerValue}
                    inputClasses={revealMode && sentence.mistakes.length === 0
					? 'border-green-700'
					: revealMode
					? 'border-red-700'
					: ''}
                    disabled={revealMode || disabled}
                    disablePaste={!$page.data.isDev}
                    on:input|once={() => dispatcher('inputStarted')}

            />
            <div class="text-red-700 break-all" dir="auto">{sentence.mistakes}</div>
        </He>
    </div>
</div>

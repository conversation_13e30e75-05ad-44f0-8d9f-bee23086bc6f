<script lang="ts">
    import BaseButton from "$components/common/BaseButton.svelte";
    import {dndzone} from 'svelte-dnd-action';
    import {generateGuid} from "$common/core/utils";
    import {IconCircleMinus, IconGripVertical} from "@tabler/icons-svelte";
    import {flip} from 'svelte/animate';
    import {createEventDispatcher} from "svelte";
    import Audio from "$components/page-content/Audio.svelte";
    import File from "$components/page-content/File.svelte";
    import ContentTiny from "$components/common/ContentTiny.svelte";
    import {t} from '$lib/i18n/config';


    const flipDurationMs = 100;
    const dispatcher = createEventDispatcher();

    let showContentTiny = true;

    export let isReadMode = false;

    export let content: {
        content_items: {
            id: string,
            pageContentId: string,
            type: 'lexical' | 'file' | 'audio',
            content: any,
            position: number
        }[]
    } = {
        content_items: []
    };


    const handleConsider = (event: CustomEvent<DndEvent<any>>) => {
        showContentTiny = false;
        content.content_items = event.detail.items;
        if (event.type === 'finalize') {
            content.content_items = content.content_items.map((x, i) => ({...x, position: i + 1}));
            showContentTiny = true;
        }
    };

    let isOpenList = false;

    const closeDropDown = () => {
        isOpenList = false;
    }

    const clickOutside = (node) => {
        const handleClick = event => {
            if (node && !node.contains(event.target) && !event.defaultPrevented) {
                node.dispatchEvent(
                    new CustomEvent('click_outside', node)
                )
            }
        }
        document.addEventListener('click', handleClick, true);
        return {
            destroy() {
                document.removeEventListener('click', handleClick, true);
            }
        }
    }


    const deleteContent = (id: string) => {
        content.content_items = content.content_items.filter((ci) => ci.id !== id).map((e, index) => ({
            ...e,
            position: index
        }))
    }


    const onClickDropDownItem = (item: 'lexical' | 'file' | 'audio') => {
        switch (item) {
            case "lexical":
                content.content_items = [{
                    id: generateGuid(),
                    pageContentId: '',
                    content: '',
                    position: 0,
                    type: 'lexical'
                }, ...content.content_items.map((e) => ({...e, position: e.position + 1}))]
                break;
            case 'file':
                content.content_items = [{
                    id: generateGuid(),
                    pageContentId: '',
                    content: '',
                    position: 0,
                    type: 'file'
                }, ...content.content_items.map((e) => ({...e, position: e.position + 1}))]
                break;
            case 'audio':
                content.content_items = [{
                    id: generateGuid(),
                    pageContentId: '',
                    content: '',
                    position: 0,
                    type: 'audio'
                }, ...content.content_items.map((e) => ({...e, position: e.position + 1}))]
                break;
        }
        isOpenList = false;
    }
</script>


<div class="flex flex-col gap-5 ">
    {#if !isReadMode}
        <div class="relative text-left flex justify-end">
            <BaseButton on:click={()=>{
                isOpenList = true;
            }}>
                {$t('editor.add')}
            </BaseButton>
            {#if isOpenList}
                <div use:clickOutside
                     on:click_outside={closeDropDown}
                     class="absolute right-0  z-10 mt-10 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                     role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
                    <div class="py-1" role="none">
                        <div on:click={()=>onClickDropDownItem('lexical')}
                             class="block px-4 py-2 text-sm text-gray-700">
                            {$t('editor.editor')}
                        </div>
                        <div on:click={()=>onClickDropDownItem('file')}
                             class="border-t-2 block px-4 py-2 text-sm text-gray-700">
                            {$t('editor.file')}
                        </div>
                        <div on:click={()=>onClickDropDownItem('audio')}
                             class="border-t-2 block px-4 py-2 text-sm text-gray-700">
                            {$t('editor.audio')}
                        </div>
                    </div>
                </div>
            {/if}
        </div>
    {/if}
    <section
            class="flex flex-col gap-2 overflow-y-auto overflow-x-hidden"
            use:dndzone={{
				items: content.content_items,
				flipDurationMs,
				dragDisabled: isReadMode,
				dropTargetStyle: { outline: 'rgb(201, 204, 209) solid 2px' },
				morphDisabled:true
			}}
            on:consider={handleConsider}
            on:finalize={handleConsider}>
        {#if content?.content_items?.length > 0}
            {#each content.content_items as item, index (item.id)}
                <div dir="ltr" class="flex items-center" animate:flip={{ duration: flipDurationMs }}>
                    {#if item.type === 'lexical'}
                        <div class="w-full flex justify-end items-center">
                            {#if showContentTiny}
                                <ContentTiny {isReadMode} bind:content={item.content}/>
                            {:else}
                                <div class="h-[200px] bg-white w-full">
                                </div>
                            {/if}
                            {#if !isReadMode}
                                <div class="flex flex-col items-center gap-2">
                                <span on:click={()=>{deleteContent(item.id)}}>
                                    <IconCircleMinus class="w-7 h-7 text-red-700 cursor-pointer"/>
                                </span>
                                    <IconGripVertical class="cursor-grab w-7 h-7"/>
                                </div>
                            {/if}
                        </div>
                    {:else if item.type === 'file'}
                        <div class="w-full flex items-center justify-center">
                            <File bind:content={item.content}/>
                            {#if !isReadMode}
                                <div class="flex flex-col items-center gap-2">
                                <span on:click={()=>{deleteContent(item.id)}}>
                                    <IconCircleMinus class="w-7 h-7 text-red-700 cursor-pointer"/>
                                </span>
                                    <IconGripVertical class="cursor-grab w-7 h-7"/>
                                </div>
                            {/if}
                        </div>
                    {:else if item.type === 'audio'}
                        <div class="w-full flex justify-center items-center ">
                            <div class="w-full">
                                <Audio id={item.id} {isReadMode} bind:content={item.content}/>
                            </div>
                            {#if !isReadMode}
                                <div class="flex flex-col items-center gap-2">
                                <span on:click={()=>{deleteContent(item.id)}}>
                                    <IconCircleMinus class="w-7 h-7 text-red-700 cursor-pointer"/>
                                </span>
                                    <IconGripVertical class="cursor-grab w-7 h-7"/>
                                </div>
                            {/if}
                        </div>
                    {/if}
                </div>
            {/each}
        {/if}
    </section>
    <!--{#if !isReadMode}-->
    <!--    <div class="border-[1px]"></div>-->
    <!--    <div class=" w-40 p-5 self-end">-->
    <!--        <BaseButton-->
    <!--                on:click={() =>-->
		<!--					dispatcher('contentUpdated')}>-->
    <!--            {$t('editor.save')}-->
    <!--        </BaseButton>-->
    <!--    </div>-->
    <!--{/if}-->
</div>
{"title": "Пользователи", "addUserButton": "Добавить пользователя", "placeHolderSearch": "Поиск", "table": {"head": {"tz": "Теуда́т-зеу́т", "firstname": "Имя", "lastname": "Фамилия", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone": "Телефон", "edit": "Действие"}, "editButton": "Редактировать"}, "modalUsers": {"titles": {"titleToCreate": "Создание нового пользователя", "titleToUpdate": "Обновление данных пользователя", "titlePermissions": "Разрешения"}, "submitButton": {"update": "Сохранить", "create": "Cоздать"}, "formFields": {"firstname": "Имя", "lastname": "Фамилия", "phone": "Телефон", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "Пароль", "tz": "ТЗ", "role": "Роль", "permissions": {"editFavorites": "Изменять избранные предложения", "deleteNonFavSentences": "Удалять предложения"}}, "formFieldsErrors": {"tzMin": "ТЗ должно содержать минимум 6 символов", "tzMax": "ТЗ не должно быть больше 12 символов", "firstname": "Имя должно содержать минимум из 2 букв", "lastname": "Фамилия должно содержать минимум из 2 букв", "emailLength": "Имейл должен содержать минимум 2 буквы", "email": "Неверный адрес электронной почты", "phone": "Телефон должен быть корректный", "passwordMin": "Пароль должен содержать минимум 5 символов", "passwordLetter": "Пароль должен содержать минимум 1 букву", "passwordDigit": "Пароль должен содержать минимум 1 цифру", "passwordToUpdate": "Пароль должен содержать как минимум 5 символов и состоять как минимум из 1 цифры и буквы"}, "selectFields": {"role": {"teacher": "Учитель", "admin": "Администратор", "disabled": "Заблокирован"}}, "notifications": {"create": {"success": "Пользователь успешно создан"}, "update": {"success": "Пользователь успешно обновлен"}}}}
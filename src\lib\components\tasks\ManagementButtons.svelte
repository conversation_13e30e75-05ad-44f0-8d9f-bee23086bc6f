<script lang="ts">
    import {IconArrowLeft, IconCopy, IconDeviceFloppy, IconDownload} from '@tabler/icons-svelte';
    import BaseSwitch from '$components/common/BaseSwitch.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import {createEventDispatcher} from 'svelte';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import {popup} from '@skeletonlabs/skeleton';
    import {format, isSameDay} from "date-fns";

    const dispatch = createEventDispatcher();
    export let isDisableSave: boolean;
    export let isToolTipActive;
    export let toolTipText = $t('tasks.managementButtons.saveHover');

    export let task;


    function createToolTipHover(index) {
        return {
            event: 'hover',
            target: `item-${index}`,
            placement: 'bottom'
        };
    }

    $: disabledLink = $CurrentEditableState.isNew || !isDisableSave || isToolTipActive;
    $: disabledDownload = $CurrentEditableState.isNew;


</script>

<div class="flex flex-col justify-between">
    <div class="flex flex-col gap-2">
        <div class=" grid grid-cols-2 gap-2">
            <div use:popup={createToolTipHover(1)} class="w-full">
                <BaseButton className="w-full" on:click={() => dispatch('save')} disabled={isDisableSave}>
                    {$t('tasks.managementButtons.save')}
                    <IconDeviceFloppy/>
                </BaseButton>
                {#if isToolTipActive}
                    <div class="card p-4 variant-filled-secondary z-[999]" data-popup="item-1">
                        <p>
                            {toolTipText}
                        </p>
                        <div class="arrow variant-filled-secondary"/>
                    </div>
                {/if}
            </div>

            <BaseButton on:click={() => dispatch('back')}>
                {$t('tasks.managementButtons.back')}
                <IconArrowLeft/>
            </BaseButton>
        </div>
        <div class="grid grid-cols-2 gap-2">
            <BaseButton on:click={() => dispatch('link')} disabled={disabledLink}>
                {$t('tasks.managementButtons.link')}
                <IconCopy/>
            </BaseButton>
            <BaseButton on:click={() => dispatch('download')} disabled={disabledDownload}>
                {$t('tasks.managementButtons.download')}
                <IconDownload/>
            </BaseButton>
        </div>
    </div>
    <div class="flex justify-end gap-2 items-center mt-4 w-fit self-end {!$CurrentEditableState.isActive?'dashed':'border-transparent border-[10px]'}">
        <p class="font-bold text-base">{$t('tasks.managementButtons.active')}</p>
        <BaseSwitch name="active" bind:checked={$CurrentEditableState.isActive}/>
    </div>
    <div dir="auto" class="flex">
        {#if task && task.createdAt && task.updatedAt}
            {#if isSameDay(new Date(task.createdAt), new Date(task.updatedAt)) && task.createdBy === task.updatedBy}
                <p class="flex gap-1">
                    <span class="">{$t('sentences.created')}</span>
                    <span class="font-bold">{task.createdBy || '-'},</span>
                    <span>{format(new Date(task.createdAt), 'dd/MM/yy kk:mm')}</span>
                </p>
            {:else}
                <div class="flex flex-col">
                    <div class="flex gap-1">
                        <span class="">{$t('sentences.created')}</span>
                        {#if task.createdBy}
                            <span class="font-bold">{task.createdBy},</span>
                        {/if}
                        <span>{format(new Date(task.createdAt), 'dd/MM/yy kk:mm')}</span>
                    </div>
                    <div class="flex gap-1">
                        <span class="">{$t('sentences.updated')}</span>
                        <span class="font-bold">{task.updatedBy},</span>
                        <span>{format(new Date(task.updatedAt), 'dd/MM/yy kk:mm')}</span>
                    </div>
                </div>
            {/if}
        {/if}

    </div>
</div>


<style lang="scss">

  .dashed {
    background: linear-gradient(90deg, rgb(var(--color-warning-600)) 50%, transparent 50%),
    linear-gradient(90deg, rgb(var(--color-warning-600)) 50%, transparent 50%),
    linear-gradient(0deg, rgb(var(--color-warning-600)) 50%, transparent 50%),
    linear-gradient(0deg, rgb(var(--color-warning-600)) 50%, transparent 50%);
    background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
    background-size: 16px 4px, 16px 4px, 4px 16px, 4px 16px;
    background-position: 0% 0%, 100% 100%, 0% 100%, 100% 0px;
    border-radius: 5px;
    padding: 10px;
    animation: dash 5s linear infinite;
  }

  @keyframes dash {
    to {
      background-position: 100% 0%, 0% 100%, 0% 0%, 100% 100%;
    }
  }
</style>

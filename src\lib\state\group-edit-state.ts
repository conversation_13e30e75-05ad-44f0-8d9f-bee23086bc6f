import {writable} from 'svelte/store';
import {HoursScheduleFilter, LanguageFilter, LevelFilter} from '$common/models/enums';
import type {FormGroup} from "$common/models/dtos/group.dto";
import {getTimeByHoursSchedule} from "../common/utils";

export const initialGroupForm: FormGroup = {
    action: 'create',
    lang: LanguageFilter.EN,
    level: LevelFilter.א,
    isPublic: false,
    isActive: true,
    hoursSchedule: HoursScheduleFilter.בוקר,
    daysSchedule: {
        Su: 0,
        Mo: 0,
        Tu: 0,
        We: 0,
        Th: 0
    },
    whatsappUrl: '',
    comment: '',
    dateStart: '',
    dateEnd: '',
    timeStart: getTimeByHoursSchedule(HoursScheduleFilter.בוקר).startInitialTime,
    timeEnd: getTimeByHoursSchedule(HoursScheduleFilter.בוקר).endInitialTime,
    totalHoursAmount: 10,
    hoursSpendBySession: 1,
    groupScheduleChanges: [],
    generalHolidays: [],
    groupHolidays: [],
    groupHolidaysExc: []

};
export const GroupEditModalState = writable({...initialGroupForm});

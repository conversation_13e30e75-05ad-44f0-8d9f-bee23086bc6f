export interface SentenceDto {
    id: string;
    audioUrl: string | null;
    complaintsCount: number;
    createdAt: string;
    createdBy: string | null;
    level: number;
    sex: 'm' | 'f';
    updatedAt: string;
    updateBy: string | null;
    value: string;
    translations: TranslationDto[];
    isNew: boolean;
    createdByUser: CreatedByUser,
    updatedByUser: UpdatedByUser,
    isFavorite: boolean;
}


interface CreatedByUser {
    firstname: string,
    lastname: string
}


interface UpdatedByUser {
    firstname: string,
    lastname: string;
}


export interface TranslationDto {
    createdAt: string;
    createdBy: string | null;
    id: string;
    lang: string;
    sentenceId: string;
    updatedAt: string;
    updatedBy: string | null;
    value: string;
}

export interface SentenceInTaskDto extends SentenceDto {
    audioRecordedButNotYetSaved: boolean;
    displayAsText: 'text' | 'audio';
    index: number;
}


export interface GenerateTaskSentenceDto {
    count: number,
    to: Date,
    from: Date,
    onlyFav: boolean,
    onlyAudio: boolean,
    selectedSentences: string[],
    lang: string,
    groupId: string
}

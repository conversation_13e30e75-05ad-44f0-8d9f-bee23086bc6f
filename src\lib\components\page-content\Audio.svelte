<script lang="ts">
    import {
        IconCloudUpload,
        IconPaperclip,
        IconPlayerRecordFilled,
        IconPlayerStopFilled,
        IconRepeat
    } from "@tabler/icons-svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import LoadingSpinner2 from "$components/common/LoadingSpinner2.svelte";
    import {getCurrentDateTime} from "$lib/common/utils";
    import {encodeMp3, loadToFfmpeg} from "$lib/common/ffmpeg-helpers";
    import {loadFfmpeg} from "$lib/state/ffmpeg-state";
    import {createEventDispatcher, onMount} from "svelte";
    import {IncentiveApiClient} from "$lib/core/api-clients/incentive-api-client";
    import {ContentApiClient} from "$lib/core/api-clients/content-api.cient";
    import {page} from "$app/stores";

    onMount(async () => {
        try {
            await loadFfmpeg();
        } catch (error) {
            console.error('Failed to load ffmpeg in Audio component:', error);
        }
    })

    export let id: string;

    export let isReadMode;

    export let content: string | null;

    $:isBlob = audioToPlay?.includes('blob');
    const composeAudioToPlay = (fileName) =>
        fileName
            ? `https://${$page.data.envs.VITE_S3_BUCKET}.s3.${$page.data.envs.VITE_S3_REGION}.amazonaws.com/${$page.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${fileName}`
            : undefined;

    let audioToPlay = content ? composeAudioToPlay(content) : '';

    $:console.log('Audio to play', audioToPlay)


    export let disabled = false;
    export let audioRecordedButNotYetSaved = false;


    let mediaRecorder: MediaRecorder | null = null;
    let audioBlobToSave: Blob;

    let time = 0;
    let isRunning = false;
    let isUploading = false;
    let interval;
    let stream;
    let input: HTMLInputElement;

    const dispatcher = createEventDispatcher();


    const startRecording = async () => {
        stream = await navigator.mediaDevices.getUserMedia({audio: true});
        mediaRecorder = new MediaRecorder(stream);
        mediaRecorder.addEventListener('dataavailable', async ({data}) => {
            const audioBlobToProcess = new File([data], `${id}_${getCurrentDateTime()}.webm`, {type: `audio/webm`}) as File;
            await loadToFfmpeg(audioBlobToProcess);
            const result = await encodeMp3();
            audioBlobToSave = new File([result], `${id}_${getCurrentDateTime()}.mp3`, {
                type: `audio/mp3`,
                lastModified: Date.now()
            }) as File;

            audioToPlay = URL.createObjectURL(audioBlobToSave);
        });
        content = '';
        mediaRecorder.start();
        isRunning = true;
        interval = setInterval(() => {
            time += 1;
        }, 1000);
    };

    const uploadAudio = async (): Promise<string> => {
        if (audioBlobToSave) {
            const formData = new FormData();
            formData.append('file', audioBlobToSave, `${id}_${getCurrentDateTime()}.mp3`);
            audioRecordedButNotYetSaved = false;
            isUploading = true;
            const response = await new ContentApiClient()
                .uploadAudio(formData)
                .finally(() => (isUploading = false));
            if (response) {
                return response;
            }
        }
    };

    const stopRecording = () => {
        if (mediaRecorder && isRunning) {
            mediaRecorder.stop();
            clearInterval(interval);
            time = 0;
            isRunning = false;
            audioRecordedButNotYetSaved = true;
        }
    };

    const fileToBlob = async (file: File) =>
        new Blob([new Uint8Array(await file.arrayBuffer())], {type: `audio/mp3`});

    const handleUploadFromFile = async (e: Event) => {
        const target = e.target as HTMLInputElement;

        if (target.files) {
            const newBlob = (await fileToBlob(target?.files[0])) as Blob;
            audioBlobToSave = new File([newBlob], `${id}_${getCurrentDateTime()}.mp3`, {
                type: newBlob.type,
                lastModified: Date.now()
            }) as File;
            content = URL.createObjectURL(audioBlobToSave);

            await uploadAudio().then((response) => {
                content = response;
                audioToPlay = composeAudioToPlay(content);
                dispatcher('audioUploaded');
            });
        }
    };

    const formatTime = (timeInSeconds: number) => {
        const minutes = Math.floor(timeInSeconds / 60)
            .toString()
            .padStart(2, '0');
        const seconds = (timeInSeconds % 60).toString().padStart(2, '0');
        return `${minutes}:${seconds}`;
    };

    const handleUploadFromRecord = async () => {
        await uploadAudio().then((response) => {
            content = response;
            audioToPlay = composeAudioToPlay(content);
            dispatcher('audioUploaded');
        });
    };

    const onClickPaperClip = () => {
        input.click();
    };

    const onRecordingCanceled = () => {
        content = '';
        audioToPlay = ''
        audioRecordedButNotYetSaved = false;
        dispatcher('audioCanceled');
    };

</script>

{#key audioToPlay}
    <div class="flex flex-col w-full">
        <div class="flex items-center gap-3">
            {#if !isRunning && !isBlob}
                {#if !isReadMode}
                    <BaseButton {disabled} on:click={startRecording}>
                        <IconPlayerRecordFilled/>
                    </BaseButton>
                {/if}
            {:else if !isRunning && isBlob}
                <BaseButton on:click={onRecordingCanceled} disabled={isUploading}>
                    <IconRepeat/>
                </BaseButton>
            {:else}
                <BaseButton on:click={stopRecording}>
                    <IconPlayerStopFilled/>
                </BaseButton>
            {/if}
            {#if isBlob}
                <BaseButton
                        className="pulse-ping-animation"
                        on:click={handleUploadFromRecord}
                        disabled={isUploading}
                >
                    <IconCloudUpload/>
                </BaseButton>
            {:else if !isBlob}
                <form>
                    <input
                            bind:this={input}
                            on:change={(e) => {
						handleUploadFromFile(e);
					}}
                            name="inputFile"
                            class="hidden"
                            type="file"
                            accept="audio/*"
                    />
                </form>
                {#if !isReadMode}
                    <BaseButton disabled={disabled} on:click={onClickPaperClip}>
                        <IconPaperclip/>
                    </BaseButton>
                {/if}
            {:else}
                <form>
                    <input
                            bind:this={input}
                            on:change={(e) => {
						handleUploadFromFile(e);
					}}
                            name="inputFile"
                            class="hidden"
                            type="file"
                            accept="audio/*"
                    />
                </form>
                <BaseButton {disabled} on:click={onClickPaperClip}>
                    <IconPaperclip/>
                </BaseButton>
            {/if}
            {#if isRunning}
                <div class="flex gap-2 text-base text-error items-center">
                    {formatTime(time)}
                    <div class="w-4 h-4 bg-red-600 rounded-full animate-pulse"></div>
                    <p>Recording...</p>
                </div>
            {:else if isUploading}
                <LoadingSpinner2/>
            {:else}
                <audio class="w-full " controls src={audioToPlay}></audio>
            {/if}
        </div>
    </div>
{/key}

<style>
    audio {
        height: 2.3em;
    }
</style>
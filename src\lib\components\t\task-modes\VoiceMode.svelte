<script lang="ts">
	import type { TaskResultDto } from '$common/models/dtos/task.dto';
	import BaseButton from '$components/common/BaseButton.svelte';
	import { createEventDispatcher } from 'svelte';
	import { initialDate } from '$lib/state/task-current-completion.state';
	import { checkTranslationResponse, isInitialDate } from '$lib/common/task-helpers';
	import { t } from '$lib/i18n/config';
	import SentenceWithAudio from '../SentenceWithAudio.svelte';
	import { slide } from 'svelte/transition';

	const dispatcher = createEventDispatcher();

	export let model: TaskResultDto;
	export let hintsEnabled: boolean;
	export let maxScore = 100;
	export let disabled = false;

	$: completions = model?.completions;
	$: revealMode = !isInitialDate(model.finishedAt, initialDate);

	const check = () => {
		checkTranslationResponse(model, maxScore);
		dispatcher('submitTask');
	};

	let open = false;

	const recordReplace = '<span style="border: 1px solid #888; border-radius: 4px; display:inline-block; vertical-align:middle;"><svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-player-record"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" /></svg></span>';
	const stopReplace = '<span style="border: 1px solid #888; border-radius: 4px; display:inline-block; vertical-align:middle;"><svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-player-stop"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 5m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z" /></svg></span>';
	const transcribeReplace = '<span style="border: 1px solid #888; border-radius: 4px; display:inline-block; vertical-align:middle;"><svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-text-grammar"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M14 9a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" /><path d="M4 12v-5a3 3 0 1 1 6 0v5" /><path d="M4 9h6" /><path d="M20 6v6" /><path d="M4 16h12" /><path d="M4 20h6" /><path d="M14 20l2 2l5 -5" /></svg></span>';
	const againReplace = '<span style="border: 1px solid #888; border-radius: 4px; display:inline-block; vertical-align:middle;"><svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-repeat"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 12v-3a3 3 0 0 1 3 -3h13m-3 -3l3 3l-3 3" /><path d="M20 12v3a3 3 0 0 1 -3 3h-13m3 3l-3 -3l3 -3" /></svg></span>';

</script>

<div class="border rounded">
    <button class="w-full text-left p-2 bg-gray-100" on:click={() => open = !open}>
        {open ? '▼' : '▶'} {$t('t.voiceInstruction')}
    </button>
    {#if open}
        <div class="p-2" transition:slide>
            {@html $t('t.voiceInstructionContent')
				.replace('{Record}', recordReplace)
				.replace('{Stop}', stopReplace)
				.replace('{Transribe}', transcribeReplace)
				.replace('{Again}', againReplace)
				}
        </div>
    {/if}
</div>

{#each completions as sentence, i}
		<SentenceWithAudio
			index={i + 1}
			{revealMode}
			{disabled}
			hintEnabled={hintsEnabled}
			bind:sentence
			on:inputStarted|once={() => {
				if (i === 0) model.startedAt = new Date();
			}}
		/>
{/each}

{#if !revealMode}
	<BaseButton on:click={check}>{$t('t.button')}</BaseButton>
{/if}

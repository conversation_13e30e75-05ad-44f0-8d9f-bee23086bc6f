import {FFmpeg} from '@ffmpeg/ffmpeg'
import {fetchFile, toBlobURL} from '@ffmpeg/util'
import { base } from '$app/paths'
const AUDIO_INPUT = 'stinky_sound';
const AUDIO_OUTPUT = 'output.mp3';

let ffmpeg: FFmpeg;
let isLoaded = false;

export const loadFfmpeg = async () => {
    if (isLoaded && ffmpeg) {
        return; // Already loaded
    }

    try {
        ffmpeg = new FFmpeg();
        const baseURL = `${base}/ffmpeg`

        await ffmpeg.load({
            coreURL: await toBlobURL(`${baseURL}/ffmpeg-core.js`, 'text/javascript'),
            wasmURL: await toBlobURL(`${baseURL}/ffmpeg-core.wasm`, 'application/wasm'),
        });

        isLoaded = true;
        console.log('FFmpeg loaded successfully');
    } catch (error) {
        console.error('Failed to load FFmpeg:', error);
        isLoaded = false;
        throw new Error(`FFmpeg loading failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}

export const loadToFfmpeg = async (file: File) => {
    if (!isLoaded || !ffmpeg) {
        throw new Error('FFmpeg is not loaded');
    }
    await ffmpeg.writeFile(AUDIO_INPUT, await fetchFile(file));
};

export const encodeMp3 = async () => {
    if (!isLoaded || !ffmpeg) {
        throw new Error('FFmpeg is not loaded');
    }

    try {
        await ffmpeg.exec(['-i', AUDIO_INPUT, '-vn', '-c:a', 'libmp3lame', '-q:a', '4', AUDIO_OUTPUT]);
        return await ffmpeg.readFile(AUDIO_OUTPUT);
    } catch (e) {
        console.error('Error encoding MP3:', e);
        throw new Error(`MP3 encoding failed: ${e instanceof Error ? e.message : 'Unknown error'}`);
    }
};

export const encodeWav = async () => {
    if (!isLoaded || !ffmpeg) {
        throw new Error('FFmpeg is not loaded');
    }

    try {
        console.log('Encoding to wav');
        await ffmpeg.exec(['-i', AUDIO_INPUT, '-vn', '-c:a', 'pcm_s16le', '-ar', '44100', '-ac', '2', 'output.wav']);
        return await ffmpeg.readFile('output.wav');
    } catch (e) {
        console.error('Error encoding WAV:', e);
        throw new Error(`WAV encoding failed: ${e instanceof Error ? e.message : 'Unknown error'}`);
    }
}

export const isFfmpegLoaded = () => {
    return isLoaded && !!ffmpeg;
}

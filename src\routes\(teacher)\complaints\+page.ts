import type {Load} from "@sveltejs/kit";
import {ComplaintApiClient} from "$lib/core/api-clients/complaint-api.client";

export const ssr = false;
export const load: Load = async ({depends}) => {
    depends('load:complaints');

    try {
        return {
            complaints: new ComplaintApiClient().getComplaints(),
            statisticComplaints: new ComplaintApiClient().getStatisticComplaints(),
            updaters:new ComplaintApiClient().getUpdaters()
        }
    } catch (error) {
        return error;
    }
};

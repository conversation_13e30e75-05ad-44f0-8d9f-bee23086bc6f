import {db} from "$api/core/service-clients/db";
import {mapper} from "$common/core/mapper";
import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";


export const getGroupHolidaysExceptions = async (groupId: string) => {
    const data = await db.groupHolidaysExceptions.findMany({
        where: {
            groupId
        },
        include:{
            group:true,
            generalHoliday:true
        }
    });

    return data.map((ghe) => mapper<GroupHolidayExcDto, unknown>(ghe))
}


export const deleteGroupHolidayExc = async (groupHolidayExcId: string) => {
    const data = await db.groupHolidaysExceptions.delete({
        where: {
            id: groupHolidayExcId
        }
    });

    return mapper<GroupHolidayExcDto, unknown>(data);
}


export const createGroupHolidayExc = async (dto:GroupHolidayExcDto)=>{
    const data = await db.groupHolidaysExceptions.create({
        data:dto
    })

    return mapper<GroupHolidayExcDto, unknown>(data);
}
import { writable } from 'svelte/store';
import { loadFfmpeg as loadFfmpegCore, isFfmpegLoaded } from '$lib/common/ffmpeg-helpers';

export interface FfmpegState {
    isLoading: boolean;
    isLoaded: boolean;
    hasError: boolean;
    errorMessage: string;
}

const initialState: FfmpegState = {
    isLoading: false,
    isLoaded: false,
    hasError: false,
    errorMessage: ''
};

export const ffmpegState = writable<FfmpegState>(initialState);

let loadingPromise: Promise<void> | null = null;

/**
 * Global function to load ffmpeg once and share the state across all components
 * This prevents multiple loading attempts and reduces memory usage
 */
export const loadFfmpeg = async (): Promise<void> => {
    // If already loaded, return immediately
    if (isFfmpegLoaded()) {
        ffmpegState.update(state => ({ ...state, isLoaded: true, isLoading: false, hasError: false }));
        return;
    }

    // If already loading, return the existing promise
    if (loadingPromise) {
        return loadingPromise;
    }

    // Start loading
    ffmpegState.update(state => ({ ...state, isLoading: true, hasError: false, errorMessage: '' }));

    loadingPromise = (async () => {
        try {
            await loadFfmpegCore();
            ffmpegState.update(state => ({ 
                ...state, 
                isLoaded: true, 
                isLoading: false, 
                hasError: false,
                errorMessage: ''
            }));
            console.log('FFmpeg loaded successfully via global store');
        } catch (error) {
            const errorMessage = 'Failed to load audio processing. Please refresh the page and try again.';
            console.error('Failed to load ffmpeg via global store:', error);
            ffmpegState.update(state => ({ 
                ...state, 
                isLoaded: false, 
                isLoading: false, 
                hasError: true,
                errorMessage
            }));
            throw error;
        } finally {
            loadingPromise = null;
        }
    })();

    return loadingPromise;
};

/**
 * Reset the ffmpeg state (useful for testing or error recovery)
 */
export const resetFfmpegState = () => {
    ffmpegState.set(initialState);
    loadingPromise = null;
};

/**
 * Get current ffmpeg state synchronously
 */
export const getFfmpegState = (): FfmpegState => {
    let currentState: FfmpegState = initialState;
    ffmpegState.subscribe(state => currentState = state)();
    return currentState;
};

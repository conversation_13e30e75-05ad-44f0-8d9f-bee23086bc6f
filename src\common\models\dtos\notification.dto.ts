export interface CampaignDto {
	id: string;
	type: 'group' | 'student';
	title: string;
	message: string;
	recipientId: string;
	createdAt: Date | undefined;
	createdBy: string;
	author: string | undefined;
	groupLang: 'ru' | 'en';
}

export interface ShortCampaignDto {
	id: string;
	message: string;
	createdAt: Date;
	author: string;
	recipientName: string;
}

export interface CampaignByRecipientIdDto {
	createdAt: Date;
	message: string;
	author: string;
}

export interface NotificationDto {
	id: string;
	campaignId: string;
	userId: string;
	isRead: string;
	readAt?: Date;
}
export interface NotificationByStudentIdDto {
	id: string;
	type: 'group' | 'student';
	message: string;
	createdAt: Date;
	recipientName: string;
	isRead: boolean;
	author: string;
	title: string;
}

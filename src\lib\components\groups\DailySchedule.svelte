<script lang="ts">
	export let Su: 0 | 1;
	export let Mo: 0 | 1;
	export let Tu: 0 | 1;
	export let We: 0 | 1;
	export let Th: 0 | 1;
	export let handleDayChange: (e: Event) => void;
</script>

<div class="flex justify-center items-center gap-2">
	<div class="flex flex-col justify-center items-center">
		<label for="default-checkbox" class=" text-sm font-medium text-gray-900 dark:text-gray-300"
			>א</label
		>
		<input
			id="default-checkbox"
			type="checkbox"
			name="Su"
			bind:value={Su}
			checked={!!Su}
			on:change={(e) => {
				handleDayChange(e);
			}}
			class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
		/>
	</div>
	<div class="flex flex-col justify-center items-center">
		<label for="default-checkbox" class=" text-sm font-medium text-gray-900 dark:text-gray-300">
			ב
		</label>
		<input
			id="default-checkbox"
			type="checkbox"
			name="Mo"
			bind:value={Mo}
			checked={!!Mo}
			on:change={(e) => {
				handleDayChange(e);
			}}
			class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
		/>
	</div>
	<div class="flex flex-col justify-center items-center">
		<label for="default-checkbox" class=" text-sm font-medium text-gray-900 dark:text-gray-300">
			ג
		</label>
		<input
			id="default-checkbox"
			type="checkbox"
			name="Tu"
			bind:value={Tu}
			checked={!!Tu}
			on:change={(e) => {
				handleDayChange(e);
			}}
			class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
		/>
	</div>
	<div class="flex flex-col justify-center items-center">
		<label for="default-checkbox" class=" text-sm font-medium text-gray-900 dark:text-gray-300">
			ד
		</label>
		<input
			id="default-checkbox"
			type="checkbox"
			name="We"
			checked={!!We}
			bind:value={We}
			on:change={(e) => {
				handleDayChange(e);
			}}
			class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
		/>
	</div>
	<div class="flex flex-col justify-center items-center">
		<label for="default-checkbox" class=" text-sm font-medium text-gray-900 dark:text-gray-300">
			ה
		</label>
		<input
			id="default-checkbox"
			type="checkbox"
			name="Th"
			bind:value={Th}
			checked={!!Th}
			on:change={(e) => {
				handleDayChange(e);
			}}
			class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
		/>
	</div>
</div>

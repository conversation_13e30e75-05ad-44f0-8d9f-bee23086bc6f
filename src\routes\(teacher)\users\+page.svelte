<script lang="ts">
    import UsersTable from '$components/users/UsersTable.svelte';
    import type {ActionData, PageData} from './$types';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {IconPlus} from '@tabler/icons-svelte';
    import {t} from '$lib/i18n/config';
    import UserFilter from '$components/users/UserFilter.svelte';
    import {initialUserForm, UserEditModalState} from '$lib/state/user-edit-state';
    import CountSpan from '$components/common/CountSpan.svelte';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import InfiniteScrollContainer from '$components/common/InfiniteScrollContainer.svelte';
    import {initialUserFilterState, UserFilterState} from '$lib/state/user-filter-state';
    import {invalidate} from '$app/navigation';
    import {initialUserPaging, pageSize, UserPagingState} from '$lib/state/user-paging-state';
    import {onDestroy} from 'svelte';
    import {loadingWrap} from '$lib/common/utils';
    import _ from "lodash";
    import {Permission} from "$common/models/enums";
    import {page} from "$app/stores";

    export let data: PageData;

    export let form: ActionData;
    let firstLoad = true;
    const modalStore = getModalStore();


    $: users = data.data;
    $: errors = form?.errors;
    $: count = data.count;


    const unsubscribeFilter = UserFilterState.subscribe(async () => {
        if (!firstLoad) {
            $UserPagingState = _.cloneDeep(initialUserPaging)
            await loadingWrap(async () => {
                await invalidate('load:users');
            });
        }
        firstLoad = false;
    });

    const loadMore = async () => {
        if (count > users.length) {
            UserPagingState.set({take: pageSize + users.length, skip: 0});
            await loadingWrap(async () => {
                await invalidate('load:users');
            });
        }
    };

    const handleModalFromTable = (event) => {
        openModal(event.detail.action, event.detail.row);
    };

    const openModal = (action: 'update' | 'create', data?) => {
        if (action === 'create') {
            UserEditModalState.set({...initialUserForm, action})
        } else {
            UserEditModalState.set({...data, action})
        }
        modalStore.trigger({type: 'component', component: 'modalUsers'});
    };

    onDestroy(() => {
        unsubscribeFilter();
        $UserFilterState = _.cloneDeep(initialUserFilterState)
    });
</script>

<div class="h-[calc(100vh-85px)] overflow-hidden flex flex-col px-6 ">
    <div class="mt-3">
        <h1 class="title mb-1 font-medium text-xl">
            {$t('users.title')}
            <CountSpan bind:count/>
        </h1>
    </div>

    <div
            class="card mt-6 p-5 w-full text-token flex justify-between items-center variant-glass-primary"
    >
        <div class="w-1/2">
            <UserFilter/>
        </div>
        <div class="mt-0">
            <div dir="ltr" class="w-full">
                <BaseButton on:click={() => openModal('create')} size="md">
                    {$t('users.addUserButton')}
                    <IconPlus/>
                </BaseButton>
            </div>
        </div>
    </div>

    <div class="mt-3 overflow-y-auto overflow-x-auto ">
        <UsersTable bind:errors bind:users on:triggerModal={handleModalFromTable} loadMoreFunc={loadMore}/>
    </div>

</div>

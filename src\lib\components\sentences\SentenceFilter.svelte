<script lang="ts">
	import BaseInput from '../common/BaseInput.svelte';
	import BaseSelect from '../common/BaseSelect.svelte';
	import {t} from '$lib/i18n/config';
	import {LevelFilter, LanguageFilter, AudioFilter} from '$common/models/enums';
	import BaseSwitch from '../common/BaseSwitch.svelte';
	import {mapEnumToOptions, mapEnumToOptionsWithTranslations} from '$lib/common/utils';
	import {SentenceFilterState} from '$lib/state/sentence-filter-state';
	import {get} from 'svelte/store';
	import _ from 'lodash';
	import type {UserDto} from "$common/models/dtos/user.dto";
	import {
		IconStarFilled,
	} from '@tabler/icons-svelte';

	export let users: UserDto[]

	export let languageDisabled = false;

	let userOptions = users?.map((u) => {
		return {
			value: u.id,
			displayValue: `${u.firstname} ${u.lastname}`
		}
	})
	let inputValue = $SentenceFilterState.search;

	const onInput = _.debounce(
		() => SentenceFilterState.set({...get(SentenceFilterState), search: inputValue}),
		1000
	);
</script>

<div class="flex flex-col ">
    <div class="flex flex-row gap-10 items-end  w-full">
        <div class="w-72">
            <BaseInput
                    name="search"
                    bind:value={inputValue}
                    on:input={onInput}
                    title={$t('sentences.filters.input')}
            />
        </div>

        <div class="flex w-full justify-around items-end">
            <div class="w-[10%] text-end">
                <BaseSwitch
                        bind:checked={$SentenceFilterState.isStrict}
                        name="isStrict"
                        title={$t('sentences.filters.strict')}
                />
            </div>


            <div class="w-[10%]">
                <BaseSelect
                        disabled={languageDisabled}
                        name="lang"
                        title={$t('sentences.filters.lang')}
                        options={mapEnumToOptions(LanguageFilter, true)}
                        bind:value={$SentenceFilterState.lang}
                />
            </div>

            <div class="w-[10%]">
                <BaseSelect
                        name="level"
                        title={$t('sentences.filters.level')}
                        options={mapEnumToOptions(LevelFilter, true)}
                        bind:value={$SentenceFilterState.level}
                />
            </div>

            <div class="w-[10%]">
                <BaseSelect
                        name="audio"
                        title={$t('sentences.filters.audio')}
                        options={mapEnumToOptionsWithTranslations(AudioFilter, 'sentences.filterAudio', $t, true)}
                        bind:value={$SentenceFilterState.audio}
                />
            </div>


            <div class="w-2/12">
                <BaseSelect
                        name="updatedBy"
                        title={$t('sentences.filters.updatedBy')}
                        options={[{value:'',displayValue:'none'},...userOptions]}
                        bind:value={$SentenceFilterState.updatedBy}
                />
            </div>

            <BaseSwitch
                    bind:checked={$SentenceFilterState.isFavorite}
                    name="onlyFavorite">
                <IconStarFilled class="text-warning-600"/>
            </BaseSwitch>
        </div>
    </div>
</div>

import {db} from "$api/core/service-clients/db";
import {mapper} from "$common/core/mapper";
import type {AboutContentsDto} from "$common/models/dtos/AboutContents.dto";
import zlib from "zlib";


export const getAboutContents = async () => {

    // deflate
    // let moveCount = await db.student_results.count({where: {resultsDeflate: null}});
    // while (moveCount !== 0) {
    //     const moveListIds = await db.student_results.findMany({
    //             where: {
    //                 resultsDeflate: null,
    //             },
    //             select: {
    //                 id: true,
    //             },
    //             orderBy: {
    //                 task: {
    //                     createdAt: 'desc'
    //                 }
    //             },
    //             take: 100
    //         });
    //
    //     for (let i = 0; i < 100; i++) {
    //
    //         const move = await db.student_results.findUnique({
    //             where: {
    //                 id: moveListIds[i].id,
    //             },
    //             select: {
    //                 id: true,
    //                 taskId: true,
    //                 studentId: true,
    //                 results: true,
    //                 resultsDeflate: true,
    //                 currentScore: true,
    //                 currentScoreAbsolute: true,
    //                 name: true,
    //                 whatsapp: true,
    //                 taskReleaseDate: true,
    //             }
    //         });
    //
    //         const deflatedResults = zlib.deflateSync(JSON.stringify(move?.results)).toString('base64');
    //
    //         await db.student_results.upsert({
    //             where: {
    //                 id: moveListIds[i].id!
    //             },
    //             create: {
    //                 ...move!,
    //                 results: move?.results as never,
    //                 resultsDeflate: deflatedResults
    //             },
    //             update: {
    //                 ...move!,
    //                 results: move?.results as never,
    //                 resultsDeflate: deflatedResults
    //             }
    //         });
    //
    //         console.log('='.repeat(100))
    //         console.log(`${i} from 100`)
    //         console.log('='.repeat(100))
    //     }
    //
    //     moveCount = await db.student_results.count({where: {resultsDeflate: null}});
    // }


    const data = await db.aboutContent.findMany();
    return data.map((x) => mapper<AboutContentsDto, unknown>(x));
}


export const updateAboutContent = async (content: Omit<AboutContentsDto, 'lang'>) => {
    const data = await db.aboutContent.update({
        where: {
            id: content.id
        },
        data: {
            value: content.value
        }
    })
    return mapper<AboutContentsDto, unknown>(data)
}

{"title": "Ж<PERSON><PERSON><PERSON><PERSON>ы", "editButton": "Редактировать", "handledButton": "Обработано", "tabs": {"complaints": "Ж<PERSON><PERSON><PERSON><PERSON>ы", "statistic": "Статистика жалоб"}, "table": {"titles": {"sentence": "Предложение", "task": "Задание", "actions": "Действие", "complaints": "Количество жалоб", "description": "Описание жалобы", "latestUpdater": "Последнее обновление"}, "student": "Студент"}, "modal": {"title": "Пожаловаться на предложение", "placeholder": "Напишите свою жалобу...", "cancelButton": "Отмена", "confirmButton": "Отправить"}, "filter": {"handledComplaintFilter": {"none": "–", "handled": "Обработано", "notHandled": "Не обработано"}, "titleHandled": "Обработано", "titleUpdater": "Последнее обновление"}, "notifications": {"toComplain": {"success": "Жалоба успешно отправлена", "error": "Произошла ошибка!"}, "handled": {"success": "Ошибка успешно обработана", "error": "Произошла ошибка!"}}, "statistic": {"title": "Статистика жалоб", "filter": {"titleUpdate": "Последнее обновление"}, "table": {"teacherName": "Имя учителя", "numberOfComplaints": "Кол-во жалоб", "numberOfSentences": "Кол-во предложений", "numberOfSentencesHandled": "Кол-во обработанных предложений", "numberOfSentencesNotHandled": "Кол-во необработанных предложений", "sentences": "Предложения"}}}
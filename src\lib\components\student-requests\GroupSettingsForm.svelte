<script lang="ts">
    import {t} from '$lib/i18n/config.js';
    import SveltyPicker from 'svelty-picker';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import FormErrorMessage from '$components/common/FormErrorMessage.svelte';
    import {createEventDispatcher, onMount} from 'svelte';
    import {getDateString} from '$lib/common/utils';
    import {StudentAcceptModalState} from '$lib/state/student-accept-state';
    import {format} from "date-fns";

    export let formData;

    export let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;

    let learnStartDate = getDateString(formData.learnStartDate);

    let dateStartTasks = getDateString(
        formData.dateStartTasks ? formData.dateStartTasks : formData.learnStartDate
    );
    const dispatch = createEventDispatcher();
    export let groupOptions;

    onMount(() => {
        onChangeFieldValue();
    });

    const onChangeFieldValue = () => {
        dispatch('changeFieldValue', {
            learnStartDate: learnStartDate,
            dateStartTasks: dateStartTasks,
            groupId: formData.groupId
        });
    };
</script>

<hr class="mt-5"/>
<header class="text-xl font-bold m-5">{$t('students.requests.modal.groupTitle')}</header>
<div class="flex-1 min-w-[150px] mx-5">
    <p dir="ltr">
        {$t('students.requests.modal.levelSubtitle')}
        <span class="badge variant-filled-tertiary mx-1">{formData.groupLevel}</span>
        {$t('students.requests.modal.langSubtitle')}
        <span class="badge variant-filled-tertiary mx-1">{formData.groupLang}</span>
        {$t('students.requests.modal.groupStartSubtitle')}
        <span class="badge variant-filled-tertiary mx-1">
			{format(new Date(formData.groupStartDate), 'dd.MM.yyyy')}
        </span>
    </p>
</div>
<div class=" p-4 sm:flex flex-col gap-4 lg:grid lg:grid-cols-2 items-end lg:gap-4">
    <label for="groupId" class="label h-16 ">
        <BaseSelect
                class="w-10"
                name="groupId"
                title={$t('students.requests.modal.group')}
                options={groupOptions}
                bind:value={formData.groupId}
                on:change={onChangeFieldValue}
        />
        <FormErrorMessage {modalErrors} fieldName="groupId"/>
    </label>

    <div class="flex flex-row gap-5 items-end">
        <div>
			<span class="block input__title font-medium text-base mb-1">
				{$t('students.requests.modal.groupStart')}
			</span>
            <SveltyPicker
                    name="learnStartDate"
                    mode="date"
                    clearToggle={false}
                    weekStart={0}
                    initialDate={new Date(learnStartDate)}
                    format="yyyy-mm-dd"
                    bind:value={learnStartDate}
                    inputClasses="w-full input rounded h-10 p-2 space-y-1"
                    on:change={onChangeFieldValue}
            />
            <FormErrorMessage {modalErrors} fieldName="learnStartDate"/>
        </div>
        <div class="">
			<span class="block input__title font-medium text-base mb-1">
				{$t('students.requests.modal.tasksStart')}
			</span>
            <SveltyPicker
                    name="dateStartTasks"
                    mode="date"
                    clearToggle={false}
                    weekStart={0}
                    initialDate={dateStartTasks ? new Date(dateStartTasks) : null}
                    format="yyyy-mm-dd"
                    bind:value={dateStartTasks}
                    inputClasses="w-full input rounded h-10 p-2"
                    on:change={onChangeFieldValue}
            />
            <FormErrorMessage {modalErrors} fieldName="dateStartTasks"/>
        </div>
    </div>
</div>

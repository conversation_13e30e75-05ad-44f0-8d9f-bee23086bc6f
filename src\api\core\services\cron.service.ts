import {db} from '../service-clients/db';
import type {StudentDto} from "$common/models/dtos/student.dto";

export const updateCurrentGroupForStudent = async () => {
    let countGroupCleared: number;
    const transferredStudents = await switchGroupToStudents();
    const studentsWithYesterdayGrad = await getStudentsWithYesterdayGrad()
    const notTransferredAndNotFinishedStudentsId = studentsWithYesterdayGrad.filter(id => !transferredStudents.some(student => student.id === id));
    if (notTransferredAndNotFinishedStudentsId && notTransferredAndNotFinishedStudentsId.length > 0) {
        countGroupCleared = await clearCurrentGroupForStudents(notTransferredAndNotFinishedStudentsId)
    }
    return transferredStudents.length > 0 || countGroupCleared > 0 ? {success: true} : {success: false}
};


const clearCurrentGroupForStudents = async (id: string[]) => {
    const data = await db.students.updateMany({
        where: {
            id: {
                in: id
            }
        },
        data: {
            currentGroup: null
        }
    });
    return data.count || 0;
}


const getStudentsWithYesterdayGrad = async () => {
    const todayStart = new Date();
    const yesterdayStart = new Date();
    const yesterdayEnd = new Date();
    yesterdayStart.setDate(todayStart.getDate() - 1);
    yesterdayStart.setUTCHours(0, 0, 0, 0);
    yesterdayEnd.setDate(todayStart.getDate() - 1);
    yesterdayEnd.setUTCHours(23, 59, 59, 59)
    const studentGroup = await db.students_groups.findMany({
        where: {
            dateEndActual: {
                gte: yesterdayStart,
                lte: yesterdayEnd
            },
            student: {
                currentGroup: {
                    not: null
                }
            }
        },
        include: {
            student: {
                select: {
                    id: true
                }
            }
        }
    });
    return studentGroup.map((studentWithYesterdayGrad) => studentWithYesterdayGrad.student.id);
}


const switchGroupToStudents = async () => {
    const updatedStudents: StudentDto[] = [];
    const todayStart = new Date();
    const todayEnd = new Date();
    todayStart.setUTCHours(0, 0, 0, 0);
    todayEnd.setHours(23, 59, 59, 59);
    const studentGroup = await db.students_groups.findMany({
        where: {
            dateStartActual: {
                gte: todayStart,
                lte: todayEnd
            }
        },
        include: {student: {select: {id: true, currentGroup: true}}}
    });
    for (const studentGroupElement of studentGroup) {
        if (studentGroupElement.groupId !== studentGroupElement.student.currentGroup) {
            const updatedStudent = await db.students.update({
                where: {
                    id: studentGroupElement.student.id
                },
                data: {
                    currentGroup: studentGroupElement.groupId,
                    currentGroupStartDate: studentGroupElement.dateStartActual
                }
            });
            updatedStudent
                ? updatedStudents.push(updatedStudent)
                : null;
        }
    }
    return updatedStudents
}
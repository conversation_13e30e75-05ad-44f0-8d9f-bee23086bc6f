import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {getComplaints, handleComplaint} from "$api/core/services/complaint.service";
import {paramsToKeyValue} from "$api/core/utils";


export const PUT = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const {taskId, sentenceId} = await event.request.json();
        return await handleComplaint(taskId, sentenceId)
    });


export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {isHandled, take, skip,latestUpdater} = paramsToKeyValue(
            url.searchParams
        );
        return await getComplaints({
            isHandled: +isHandled,
            take: +take,
            skip: +skip,
            latestUpdater
        })
    });

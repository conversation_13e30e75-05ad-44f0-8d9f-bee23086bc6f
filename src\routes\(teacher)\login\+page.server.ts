import {superValidate} from 'sveltekit-superforms/server';
import {fail, redirect, type RequestEvent} from '@sveltejs/kit';
import {z} from 'zod';
import {loginForTeacher} from "$api/core/services/auth.service";
import {Routes} from "$common/core/routes";

const schemaLogin = z.object({
    tz: z.string()
        .min(6, {message: 'The tz must contain at least 6 characters'})
        .max(10, {message:'The tz must have a maximum of 10 characters'}),
    password: z.string().min(3, 'The password must contain at least 3 characters')
});

export const actions = {
    default: async (event: RequestEvent) => {
        const form = await superValidate(event, schemaLogin);

        if (!form.valid) return fail(400, {form});

        const result = await loginForTeacher(form.data.tz, form.data.password, event);

        if (result.error) return fail(401, {error: result.error, form});

        throw redirect(302, Routes.Main);
    }
};

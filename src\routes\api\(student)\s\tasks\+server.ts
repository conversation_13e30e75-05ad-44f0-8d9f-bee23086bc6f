import {wrapFunc} from '$api/core/misc/response-wrapper';
import {
    getStudentTaskById,
    getAllTasksByStudentId
} from '$api/core/services/task.service';
import type {RequestEvent} from '@sveltejs/kit';
import {paramsToKeyValue} from "$api/core/utils";


export const GET = async ({locals, url}: RequestEvent): Promise<Response> =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);
        if (id) {
            return await getStudentTaskById(id, locals.user.id);
        }
        return await getAllTasksByStudentId(locals.user.id);
    });


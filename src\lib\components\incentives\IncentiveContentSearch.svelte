<script lang="ts">
	import BaseInput from '$components/common/BaseInput.svelte';
	import { IncentiveState } from '$lib/state/incentive-state';
	import _ from 'lodash';
	import { t } from '$lib/i18n/config';

	let search;
	const onInput = _.debounce(() => ($IncentiveState.search = search), 1000);
</script>

<div>
	<BaseInput
		on:input={onInput}
		title={$t('incentiveContent.placeHolderSearch')}
		name="search"
		bind:value={search}
	/>
</div>

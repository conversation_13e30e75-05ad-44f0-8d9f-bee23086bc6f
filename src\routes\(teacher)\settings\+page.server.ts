import type {Actions} from "@sveltejs/kit";
import {parseFormData} from "parse-nested-form-data";
import {validateEntityBySchema} from "$lib/common/utils";
import {exclusionCreationSchema} from "$lib/validation-schemes/exclusion";
import {fail} from "@sveltejs/kit";
import {createExclusions} from "$api/core/services/exclusions.service";
import {mapper} from "$common/core/mapper";
import type {ExclusionsDto} from "$common/models/dtos/Exclusions.dto";


export const ssr = false;

export const actions: Actions = {
    createExclusion: async ({request}: { request: Request }) => {
        const exclusions = parseFormData(await request.formData());
        const {result, errors} = validateEntityBySchema(exclusions, exclusionCreationSchema);
        if (!result) return fail(400, {error: true, errors});
        return await createExclusions(mapper<ExclusionsDto, unknown>(exclusions))
    }
};
<script lang="ts">
    import {LevelFilter} from "$common/models/enums";
    import {
        IconCheck,
        IconMinus,
        IconPlus,
        IconMusic,
        IconMusicOff, IconStarFilled,
    } from "@tabler/icons-svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import type {SentenceDto} from "$common/models/dtos/sentence.dto";


    export let sentence: SentenceDto;

    export let findSentence: (id: string) => void;
    export let deleteSentenceFromState: (id: string) => void;
    export let addSentenceToState: (sentence: SentenceDto) => void;
    export let translation: string;
</script>

<div class="py-3 px-5 pr-7 mt-1">
    <div class="grid grid-cols-10 items-center">
        <div>
            {#if findSentence(sentence.id)}
                <BaseButton on:click={() => {deleteSentenceFromState(sentence.id)}} size="sm">
                    <IconMinus/>
                </BaseButton>
                <BaseButton className="text-green-500 bg-transparent cursor-default" size="sm">
                    <IconCheck/>
                </BaseButton>
            {:else}
                <BaseButton on:click={() => {addSentenceToState(sentence)}} size="sm">
                    <IconPlus/>
                </BaseButton>
            {/if}
        </div>
        <div class="col-start-2 col-end-6 pl-10 text-lg flex gap-1 items-center">
            {sentence.value}
            {#if sentence.isFavorite}
                <IconStarFilled class="text-warning-600"/>
            {/if}
        </div>
        <div class="col-start-6 col-end-10 ml-7 " dir="auto">{translation}</div>

        <div class="flex justify-between items-center">
            <div class="flex justify-center ">
                {#if sentence.audioUrl}
                    <IconMusic class="text-success-700" size="35"/>
                {:else}
                    <IconMusicOff class="text-error-700" size="35"/>
                {/if}
            </div>

            <div class="text-center items-center">
                {#each sentence.translations as translation}
                    <div class="bg-blue-100 w-10 text-blue-800 text-xs font-medium m-0.5 py-0.5 rounded dark:bg-gray-700 dark:text-blue-400 border border-blue-400">
                        {translation.lang + ' '}
                    </div>
                {/each}
            </div>
            <div class="text-center">
                <div class="bg-blue-100 w-10 text-blue-800 text-base font-medium py-0.5 rounded dark:bg-gray-700 dark:text-blue-400 border border-blue-400">
                    {LevelFilter[sentence.level]}
                </div>
            </div>
        </div>

    </div>
</div>
<hr>

export interface ShortStudentRequestDto {
	id: string;
	tz: string;
	email: string;
	firstname: string;
	lastname: string;
	dob: Date;
	groupStartDate: Date;
	learnStartDate: Date;
	phone: string;
	whatsapp: string;
	groupLevel: string;
	groupLang: string;
	isHandled: boolean;
	createdAt: Date;
	city: string;
	teudatOleUrl: string;
	photoUrl: string;
	rawRequest: string;
}

export interface StudentRequestDto
	extends Omit<ShortStudentRequestDto, 'groupLang' | 'groupLevel' | 'isHandled'> {
	groupId: string;
	comment: string;
	firstname: string;
	lastname: string;
	document1url: string;
	dateStartTasks: Date;
	isExistingStudent: boolean;
}

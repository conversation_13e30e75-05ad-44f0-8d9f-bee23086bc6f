import type {ComplaintDto, CreateComplaintDto} from "$common/models/dtos/complaint.dto";
import {db} from "$api/core/service-clients/db";
import {generateGuid} from "$common/core/utils";
import {mapper} from "$common/core/mapper";
import type {ComplaintFilterDto} from "$common/models/filters/complaint-filter.dto";
import type {TableDataDto} from "$common/models/dtos/table-data.dto";
import type {ComplaintsStatisticFilterDto} from "$common/models/filters/complaints-statistic-filter.dto";
import {ComplaintsStatisticFilterField} from "$common/models/enums";


export const createComplaint = async (complaint: CreateComplaintDto) => {
    const {taskId, comment, createdBy, sentenceId,} = complaint;
    const data = await db.complaints.create({
        data: {
            id: generateGuid(),
            taskId,
            comment,
            createdBy,
            sentenceId
        }
    })

    return mapper<ComplaintDto, unknown>(data)
}

export const handleComplaint = async (taskId: string, sentenceId: string) => {
    await db.complaints.updateMany({
        where: {
            taskId,
            sentenceId
        },
        data: {
            isHandled: true
        }
    });
    return {success: true}
}


export const getComplaints = async (filter: ComplaintFilterDto): Promise<TableDataDto<ComplaintDto>> => {
    const where = composeWhereClause(filter);
    const allComplaints = await db.complaints.findMany({where});
    const uniqueSentenceIds: Set<string> = new Set();
    const count = allComplaints.reduce((acc, obj) =>
        (uniqueSentenceIds.has(obj.sentenceId) ? acc : (uniqueSentenceIds.add(obj.sentenceId), acc + 1)), 0);


    const data = await db.complaints.findMany({
        take: filter.take,
        skip: filter.skip,
        where,
        include: {
            task_sentences: {
                select: {
                    sentence: {
                        include: {
                            translations: true,
                            createdByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true,
                                },
                            },
                            updatedByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            },
                        }
                    },
                    task: {
                        select: {
                            lang: true
                        }
                    }
                }
            },
        }
    })

    const dtos = data.map((complaint) => mapper<ComplaintDto, unknown>(complaint));

    return {
        data: dtos,
        count
    }
}

export const getUpdaters = async () => {
    return await db.complaints.findMany({
        where: {
            task_sentences: {
                sentence: {
                    updatedBy: {
                        not: null
                    }
                },
            },
        },
        select: {
            task_sentences: {
                select: {
                    sentence: {
                        select: {
                            updatedByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            }
                        }
                    }
                }
            }
        },
    })
}

export const getStatisticsComplaints = async (filter: ComplaintsStatisticFilterDto) => {

    const data = await db.complaints.findMany({
        where: {
            task_sentences: {
                sentence: {
                    updatedByUser: {
                        id: filter.latestUpdater || undefined
                    },
                    updatedBy: {
                        not: null
                    }
                }
            }
        },
        include: {
            task_sentences: {
                select: {
                    sentence: {
                        include: {
                            translations: true,
                            createdByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true,
                                },
                            },
                            updatedByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            },
                        }
                    },
                    task: {
                        select: {
                            lang: true
                        }
                    }
                }
            },
        }
    });

    const reducedElements = data?.reduce((acc, item) => {
        const teacherName = `${item?.task_sentences?.sentence?.updatedByUser?.firstname} ${item?.task_sentences?.sentence?.updatedByUser?.lastname}`
        const teacherId = item?.task_sentences?.sentence?.updatedByUser?.id;
        const existItem = acc.find((el) => el.teacherName === teacherName)
        if (existItem) {
            existItem.numberOfComplaints += 1;
            const existSentence = existItem.sentences.find((s) => s.sentenceId === item.sentenceId);
            if (!existSentence) {
                existItem.sentences.push({
                    sentenceId: item.sentenceId,
                    taskId: item.taskId,
                    isHandled: item.isHandled,
                    sentenceValue: item.task_sentences.sentence.value
                })
                existItem.numberOfSentences += 1;
                item.isHandled ? existItem.numberOfSentencesHandled += 1 : existItem.numberOfSentencesNotHandled += 1
            }
        } else {
            acc.push({
                id: generateGuid(),
                teacherName: teacherName,
                teacherId,
                numberOfComplaints: 1,
                numberOfSentencesHandled: item.isHandled ? 1 : 0,
                numberOfSentencesNotHandled: item.isHandled ? 0 : 1,
                numberOfSentences: 1,
                sentences: [{
                    sentenceId: item.sentenceId,
                    taskId: item.taskId,
                    isHandled: item.isHandled,
                    sentenceValue: item.task_sentences.sentence.value
                }]
            })
        }
        return acc;
    }, [] as {
        id: string,
        teacherName: string,
        teacherId: string | undefined,
        numberOfComplaints: number,
        numberOfSentencesHandled: number,
        numberOfSentencesNotHandled: number,
        numberOfSentences: number,
        sentences: { sentenceId: string, taskId: string, isHandled: boolean, sentenceValue: string }[]
    }[]);

    switch (filter.sortBy) {
        case ComplaintsStatisticFilterField.complaints:
            return reducedElements.sort((a, b) => filter.sortDir === 'asc' ?
                b.numberOfComplaints - a.numberOfComplaints :
                a.numberOfComplaints - b.numberOfComplaints)
        case ComplaintsStatisticFilterField.sentences:
            return reducedElements.sort((a, b) => filter.sortDir === 'asc' ?
                b.numberOfSentences - a.numberOfSentences :
                a.numberOfSentences - b.numberOfSentences
            )
        case ComplaintsStatisticFilterField.handled:
            return reducedElements.sort((a, b) => filter.sortDir === 'asc' ?
                b.numberOfSentencesHandled - a.numberOfSentencesHandled :
                a.numberOfSentencesHandled - b.numberOfSentencesHandled
            )
        case ComplaintsStatisticFilterField.nothandled:
            return reducedElements.sort((a, b) => filter.sortDir === 'asc' ?
                b.numberOfSentencesNotHandled - a.numberOfSentencesNotHandled :
                a.numberOfSentencesNotHandled - b.numberOfSentencesNotHandled
            );
        default:
            return reducedElements;
    }
}

export const getTheNumberComplaintsByUserId = async (userId: string) => {
    return await db.complaints.count({
        where: {
            isHandled: false,
            task_sentences: {
                sentence: {
                    updatedBy: userId
                }
            }
        }
    })


}

const composeWhereClause = (filter: ComplaintFilterDto): Record<string, any> => {
    const whereClause: Record<string, any> = {};

    if (filter.isHandled > -1) {
        whereClause.isHandled = filter.isHandled === 1
    }


    if (filter.latestUpdater && filter.latestUpdater.length > 0) {
        whereClause.task_sentences = {
            sentence: {
                updatedBy: filter.latestUpdater
            }
        }
    }

    return whereClause;
}

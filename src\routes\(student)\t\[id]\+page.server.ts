import {parseFormData} from 'parse-nested-form-data';
import type {Actions} from "@sveltejs/kit";
import type { CreateComplaintDto} from "$common/models/dtos/complaint.dto";
import {mapper} from "$common/core/mapper";
import {createComplaint} from "$api/core/services/complaint.service";

export const ssr = false;

export const actions: Actions = {
    createComplaint: async ({request}: { request: Request }) => {
        const complaint = parseFormData(await request.formData());

        const dto = mapper<CreateComplaintDto, any>({
            ...complaint
        })

        return await createComplaint(dto);
    }
};

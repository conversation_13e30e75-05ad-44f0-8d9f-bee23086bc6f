import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";

export class GroupScheduleApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getGroupScheduleChangesByGroupId = async (groupId: string) => {
        return await this.getDataOrThrow(`/api/groupScheduleChanges?groupId=${groupId}`)
    }


    public createGroupScheduleChanges = async (dto: GroupScheduleDto) => {
        return await this.postDataOrThrow('/api/groupScheduleChanges', dto)
    }

    public deleteGroupScheduleChange = async (id: string) => {
        return await this.deleteOrThrow(`/api/groupScheduleChanges?id=${id}`)
    }


}
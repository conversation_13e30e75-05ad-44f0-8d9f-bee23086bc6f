import i18n from 'sveltekit-i18n';
import lang from './lang.json';

/** @type {import('sveltekit-i18n').Config} */
const config = {
    translations: {
        en: {lang},
        ru: {lang},
        he: {lang}
    },
    loaders: [
        {
            locale: 'en',
            key: 'auth',
            routes: ['/'], // you can use regexes as well!
            loader: async () => (await import('./en/auth.json')).default
        },
        {
            locale: 'ru',
            key: 'auth',
            routes: ['/'], // you can use regexes as well!
            loader: async () => (await import('./ru/auth.json')).default
        },
        {
            locale: 'he',
            key: 'auth',
            routes: ['/'], // you can use regexes as well!
            loader: async () => (await import('./he/auth.json')).default
        },
        {
            locale: 'en',
            key: 'common',
            loader: async () => (await import('./en/common.json')).default
        },
        {
            locale: 'ru',
            key: 'common',
            loader: async () => (await import('./ru/common.json')).default
        },
        {
            locale: 'he',
            key: 'common',
            loader: async () => (await import('./he/common.json')).default
        },
        {
            locale: 'en',
            key: 'sentences',
            loader: async () => (await import('./en/sentences.json')).default
        },
        {
            locale: 'ru',
            key: 'sentences',
            loader: async () => (await import('./ru/sentences.json')).default
        },
        {
            locale: 'he',
            key: 'sentences',
            loader: async () => (await import('./he/sentences.json')).default
        },
        {
            locale: 'en',
            key: 'groups',
            loader: async () => (await import('./en/groups.json')).default
        },
        {
            locale: 'ru',
            key: 'groups',
            loader: async () => (await import('./ru/groups.json')).default
        },
        {
            locale: 'he',
            key: 'groups',
            loader: async () => (await import('./he/groups.json')).default
        },
        {
            locale: 'en',
            key: 'users',
            loader: async () => (await import('./en/users.json')).default
        },
        {
            locale: 'ru',
            key: 'users',
            loader: async () => (await import('./ru/users.json')).default
        },
        {
            locale: 'he',
            key: 'users',
            loader: async () => (await import('./he/users.json')).default
        },
        {
            locale: 'en',
            key: 'students',
            loader: async () => (await import('./en/students.json')).default
        },
        {
            locale: 'ru',
            key: 'students',
            loader: async () => (await import('./ru/students.json')).default
        },
        {
            locale: 'he',
            key: 'students',
            loader: async () => (await import('./he/students.json')).default
        },
        {
            locale: 'en',
            key: 'campaigns',
            loader: async () => (await import('./en/campaigns.json')).default
        },
        {
            locale: 'ru',
            key: 'campaigns',
            loader: async () => (await import('./ru/campaigns.json')).default
        },
        {
            locale: 'he',
            key: 'campaigns',
            loader: async () => (await import('./he/campaigns.json')).default
        },
        {
            locale: 'en',
            key: 'tasks',
            loader: async () => (await import('./en/tasks.json')).default
        },
        {
            locale: 'ru',
            key: 'tasks',
            loader: async () => (await import('./ru/tasks.json')).default
        },
        {
            locale: 'he',
            key: 'tasks',
            loader: async () => (await import('./he/tasks.json')).default
        },
        {
            locale: 'en',
            key: 'sign-in',
            loader: async () => (await import('./en/sign-in.json')).default
        },
        {
            locale: 'ru',
            key: 'sign-in',
            loader: async () => (await import('./ru/sign-in.json')).default
        },
        {
            locale: 'he',
            key: 'sign-in',
            loader: async () => (await import('./he/sign-in.json')).default
        },
        {
            locale: 'en',
            key: 'studentTasks',
            loader: async () => (await import('./en/studentTasks.json')).default
        },
        {
            locale: 'ru',
            key: 'studentTasks',
            loader: async () => (await import('./ru/studentTasks.json')).default
        },
        {
            locale: 'he',
            key: 'studentTasks',
            loader: async () => (await import('./he/studentTasks.json')).default
        },

        {
            locale: 'en',
            key: 'notifications',
            loader: async () => (await import('./en/notifications.json')).default
        },
        {
            locale: 'ru',
            key: 'notifications',
            loader: async () => (await import('./ru/notifications.json')).default
        },
        {
            locale: 'he',
            key: 'notifications',
            loader: async () => (await import('./he/notifications.json')).default
        },
        {
            locale: 'en',
            key: 'incentiveContent',
            loader: async () => (await import('./en/incentiveContent.json')).default
        },
        {
            locale: 'ru',
            key: 'incentiveContent',
            loader: async () => (await import('./ru/incentiveContent.json')).default
        },
        {
            locale: 'he',
            key: 'incentiveContent',
            loader: async () => (await import('./he/incentiveContent.json')).default
        },
        {
            locale: 'en',
            key: 't',
            loader: async () => (await import('./en/t.json')).default
        },
        {
            locale: 'ru',
            key: 't',
            loader: async () => (await import('./ru/t.json')).default
        },
        {
            locale: 'he',
            key: 't',
            loader: async () => (await import('./he/t.json')).default
        },
        {
            locale: 'en',
            key: 'home',
            loader: async () => (await import('./en/home.json')).default
        },
        {
            locale: 'ru',
            key: 'home',
            loader: async () => (await import('./ru/home.json')).default
        },
        {
            locale: 'he',
            key: 'home',
            loader: async () => (await import('./he/home.json')).default
        },
        {
            locale: 'en',
            key: 'complaints',
            loader: async () => (await import('./en/complaints.json')).default
        },
        {
            locale: 'ru',
            key: 'complaints',
            loader: async () => (await import('./ru/complaints.json')).default
        },
        {
            locale: 'he',
            key: 'complaints',
            loader: async () => (await import('./he/complaints.json')).default
        },
        {
            locale: 'en',
            key: 'settings',
            loader: async () => (await import('./en/settings.json')).default
        },
        {
            locale: 'ru',
            key: 'settings',
            loader: async () => (await import('./ru/settings.json')).default
        },
        {
            locale: 'he',
            key: 'settings',
            loader: async () => (await import('./he/settings.json')).default
        },
        {
            locale: 'en',
            key: 'about',
            loader: async () => (await import('./en/about.json')).default
        },
        {
            locale: 'ru',
            key: 'about',
            loader: async () => (await import('./ru/about.json')).default
        },
        {
            locale: 'he',
            key: 'about',
            loader: async () => (await import('./he/about.json')).default
        },
        {
            locale: 'en',
            key: 'editor',
            loader: async () => (await import('./en/editor.json')).default
        },
        {
            locale: 'ru',
            key: 'editor',
            loader: async () => (await import('./ru/editor.json')).default
        },
        {
            locale: 'he',
            key: 'editor',
            loader: async () => (await import('./he/editor.json')).default
        }
    ]
};

export const {t, locale, locales, loading, loadTranslations} = new i18n(config);

loading.subscribe(($loading) => $loading);

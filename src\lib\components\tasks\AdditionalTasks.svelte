<script lang="ts">
    import BaseSwitch from '$components/common/BaseSwitch.svelte';
    import {
        IconClockHour9,
        IconCopy,
        IconEar,
        IconEyeOff,
        IconHeadphones,
		IconMicrophone,
    } from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {slide} from 'svelte/transition';
    import {t} from '$lib/i18n/config';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import TaskIncentiveContent from '$components/tasks/TaskIncentiveContent.svelte';
    import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
    import type {TaskIncentiveContentWithContentDto} from '$common/models/dtos/task-incentive-content-with-content.dto';
    import {page} from '$app/stores';
    import {Routes} from '$common/core/routes';
    import NotificationState from '$lib/state/notification-state';
    import {NotificationType, TaskMode, TaskSpeedMode} from '$common/models/enums';
    import BaseInputNumber from "$components/common/BaseInputNumber.svelte";
    import {onMount} from "svelte";
    import BaseSelect from "$components/common/BaseSelect.svelte";
    import {mapEnumToOptionsWithTranslations} from "$lib/common/utils";
	import type { AdditionalTaskDto } from '$common/models/dtos/task.dto';

    export let incentiveContent: IncentiveContentDto[];
    export let taskIncentiveContent: TaskIncentiveContentWithContentDto[];

    onMount(() => {
        if ($CurrentEditableState.isPublic) {
            $CurrentEditableState.additionalTasks.listen.allowAnonymous = $CurrentEditableState.allowAnonymous;
            $CurrentEditableState.additionalTasks.bytime.allowAnonymous = $CurrentEditableState.allowAnonymous;
            $CurrentEditableState.additionalTasks.phantom.allowAnonymous = $CurrentEditableState.allowAnonymous;
            $CurrentEditableState.additionalTasks.audiodic.allowAnonymous = $CurrentEditableState.allowAnonymous;
            $CurrentEditableState.additionalTasks.voice.allowAnonymous = $CurrentEditableState.allowAnonymous;

            $CurrentEditableState.additionalTasks.listen.allowRetry = $CurrentEditableState.navigationInPublicTaskEnabled;
            $CurrentEditableState.additionalTasks.bytime.allowRetry = $CurrentEditableState.navigationInPublicTaskEnabled;
            $CurrentEditableState.additionalTasks.phantom.allowRetry = $CurrentEditableState.navigationInPublicTaskEnabled;
            $CurrentEditableState.additionalTasks.audiodic.allowRetry = $CurrentEditableState.navigationInPublicTaskEnabled;
            $CurrentEditableState.additionalTasks.voice.allowRetry = $CurrentEditableState.navigationInPublicTaskEnabled;
        }
    })


    const copyLink = async (mode: TaskMode) => {
        let currentUrl = $page.url.origin + ($CurrentEditableState.isPublic ? Routes.PublicT : Routes.T) + $page.params.id + '/?tm=' + mode;
        try {
            await navigator.clipboard.writeText(currentUrl);
            NotificationState.push({
                type: NotificationType.success,
                message: $t('tasks.notification.link.success')
            });
        } catch (error) {
            NotificationState.push({
                type: NotificationType.error,
                message: $t('tasks.notification.link.error')
            });
        }
    };
    const disableMode = (container: AdditionalTaskDto) => {
        container.enabled = false;
        CurrentEditableState.update(state => {
            return { ...state };
        });
        NotificationState.push({
            type: NotificationType.error,
            message: $t('tasks.additionalTask.noAudioError')
       }, 5)}       

    const checkAudioAndDisable = (container: AdditionalTaskDto) => {
        container.enabled && $CurrentEditableState.sentences.find(s => s.audioUrl === null) && 
            disableMode(container); 
    };
</script>

<div transition:slide|global={{ duration: 500 }} class="flex flex-col gap-10">
    <div class="grid grid-rows-5 card p-5 w-full text-token variant-glass-primary mt-10">
        <div class="grid grid-cols-6 card content-center">
            <div class="col-start-1 col-end-3 font-bold mx-10">{$t('tasks.additionalTask.tasks')}</div>
            <div class="font-bold">{$t('tasks.additionalTask.time')}</div>
            <div class="font-bold">{$t($CurrentEditableState.isPublic ? 'tasks.additionalTask.allowAnonymous' : 'tasks.additionalTask.delay')}</div>
            <div class="font-bold">{$t($CurrentEditableState.isPublic ? 'tasks.additionalTask.allowRetry' : 'tasks.additionalTask.points')}</div>
            <div class="font-bold">{$t('tasks.additionalTask.hint')}</div>
        </div>

        <!--{#if $CurrentEditableState.additionalTasks?.listen}-->
        <div class="grid items-center grid-cols-6 mt-4">
            <div class="flex gap-2 items-center col-start-1 col-end-3 font-bold">
                <BaseSwitch on:change={() => {
                    checkAudioAndDisable($CurrentEditableState.additionalTasks.listen);
                    }}
                        name="enabled"
                        bind:checked={$CurrentEditableState.additionalTasks.listen.enabled}
                />
                <p class="flex gap-1">{$t('tasks.additionalTask.listening')}
                    <IconHeadphones/>
                </p>
            </div>

            {#if $CurrentEditableState.additionalTasks?.listen.enabled}
                <div/>
                {#if $CurrentEditableState.isPublic}

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowAnonymous"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.listen.allowAnonymous}
                        />
                    </div>

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowRetry"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.listen.allowRetry}
                        />
                    </div>
                {:else}
                    <div class="w-1/4">
                        <BaseInputNumber name="redoLong"
                                         bind:value={$CurrentEditableState.additionalTasks.listen.delay}/>
                    </div>
                    <div class="w-1/4">
                        <BaseInputNumber name="redoPoints"
                                         bind:value={$CurrentEditableState.additionalTasks.listen.maxScore}/>

                    </div>
                {/if}
                <div class="flex items-center gap-2">
                    <BaseSwitch
                            name="hint"
                            class="sm"
                            bind:checked={$CurrentEditableState.additionalTasks.listen.hintEnabled}
                    />
                    <BaseButton size="md" className="mx-10" on:click={() => copyLink(TaskMode.listen)}>
                        <IconCopy size="16"/>
                    </BaseButton>
                </div>
            {/if}
        </div>
        <!--{/if}-->

        <!--{#if $CurrentEditableState.additionalTasks?.bytime}-->
        <div class="grid items-center grid-cols-6 mt-4">
            <div class="flex gap-2 items-center col-start-1 col-end-3 font-bold">
                <BaseSwitch
                        name="enabled"
                        bind:checked={$CurrentEditableState.additionalTasks.bytime.enabled}
                />
                <p class="flex gap-1">{$t('tasks.additionalTask.reDo')}
                    <IconClockHour9/>
                </p>
            </div>
            {#if $CurrentEditableState.additionalTasks?.bytime.enabled}
                <div class="w-1/4">
                    <BaseInputNumber name="redoTime"
                                     bind:value={$CurrentEditableState.additionalTasks.bytime.time}/>
                </div>
                {#if $CurrentEditableState.isPublic}

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowAnonymous"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.bytime.allowAnonymous}
                        />
                    </div>

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowRetry"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.bytime.allowRetry}
                        />
                    </div>
                {:else}
                    <div class="w-1/4">
                        <BaseInputNumber name="redoLong"
                                         bind:value={$CurrentEditableState.additionalTasks.bytime.delay}/>
                    </div>
                    <div class="w-1/4">
                        <BaseInputNumber name="redoPoints"
                                         bind:value={$CurrentEditableState.additionalTasks.bytime.maxScore}/>
                    </div>
                {/if}
                <div class="flex items-center gap-2">
                    <BaseSwitch
                            name="hint"
                            bind:checked={$CurrentEditableState.additionalTasks.bytime.hintEnabled}
                    />
                    <BaseButton size="md" className="mx-10" on:click={() => copyLink(TaskMode.bytime)}>
                        <IconCopy size="16"/>
                    </BaseButton>
                </div>
            {/if}
        </div>
        <!--{/if}-->

        <!--{#if $CurrentEditableState.additionalTasks?.phantom}-->
        <div class="grid items-center grid-cols-6 mt-4">
            <div class="flex gap-2 items-center col-start-1 col-end-3 font-bold">
                <BaseSwitch
                        name="enabled"
                        bind:checked={$CurrentEditableState.additionalTasks.phantom.enabled}
                />
                <p class="flex gap-1">{$t('tasks.additionalTask.phantomDictation')}
                    <IconEyeOff/>
                </p>
            </div>

            {#if $CurrentEditableState.additionalTasks?.phantom.enabled}
                <div class="w-1/2">
                    <BaseSelect name="phantomDictationTime"
                                bind:value={$CurrentEditableState.additionalTasks.phantom.speedMode}
                                options={mapEnumToOptionsWithTranslations(TaskSpeedMode, 'tasks.additionalTask', $t)}/>
                    <!--                    <BaseInputNumber name="phantomDictationTime"-->
                    <!--                                     bind:value={$CurrentEditableState.additionalTasks.phantom.time}/>-->
                </div>
                {#if $CurrentEditableState.isPublic}

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowAnonymous"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.phantom.allowAnonymous}
                        />
                    </div>

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowRetry"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.phantom.allowRetry}
                        />
                    </div>
                {:else}
                    <div class="w-1/4">
                        <BaseInputNumber name="redoLong"
                                         bind:value={$CurrentEditableState.additionalTasks.phantom.delay}
                        />
                    </div>
                    <div class="w-1/4">
                        <BaseInputNumber name="redoPoints"
                                         bind:value={$CurrentEditableState.additionalTasks.phantom.maxScore}/>
                    </div>
                {/if}
                <div class="flex items-center gap-2">
                    <BaseSwitch
                            name="hint"
                            bind:checked={$CurrentEditableState.additionalTasks.phantom.hintEnabled}
                    />
                    <BaseButton size="md" className="mx-10" on:click={() => copyLink(TaskMode.phantom)}>
                        <IconCopy size="16"/>
                    </BaseButton>
                </div>
            {/if}
        </div>
        <!--{/if}-->

        <!--{#if $CurrentEditableState.additionalTasks?.audiodic}-->
        <div class="grid items-center grid-cols-6 mt-4">
            <div class="flex gap-2 items-center col-start-1 col-end-3 font-bold">
                <BaseSwitch on:change={() => {
                    checkAudioAndDisable($CurrentEditableState.additionalTasks.audiodic);        
                }}
                        name="enabled"
                        bind:checked={$CurrentEditableState.additionalTasks.audiodic.enabled}
                />
                <p class="flex gap-1">{$t('tasks.additionalTask.audioDictation')}
                    <IconEar/>
                </p>
            </div>

            {#if $CurrentEditableState.additionalTasks?.audiodic.enabled}
                <div/>
                {#if $CurrentEditableState.isPublic}

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowAnonymous"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.audiodic.allowAnonymous}
                        />
                    </div>

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowRetry"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.audiodic.allowRetry}
                        />
                    </div>
                {:else}
                    <div class="w-1/4">
                        <BaseInputNumber name="redoLong"
                                         bind:value={$CurrentEditableState.additionalTasks.audiodic.delay}/>
                    </div>
                    <div class="w-1/4">
                        <BaseInputNumber name="redoPoints"
                                         bind:value={$CurrentEditableState.additionalTasks.audiodic.maxScore}/>

                    </div>
                {/if}
                <div class="flex items-center gap-2">
                    <BaseSwitch
                            name="hint"
                            bind:checked={$CurrentEditableState.additionalTasks.audiodic.hintEnabled}
                    />
                    <BaseButton size="md" className="mx-10" on:click={() => copyLink(TaskMode.audiodic)}>
                        <IconCopy size="16"/>
                    </BaseButton>
                </div>
            {/if}
        </div>
        <!--{/if}-->

        <!--{#if $CurrentEditableState.additionalTasks?.voice}-->
        <div class="grid items-center grid-cols-6 mt-4">
            <div class="flex gap-2 items-center col-start-1 col-end-3 font-bold">
                <BaseSwitch on:change={() => {
                        checkAudioAndDisable($CurrentEditableState.additionalTasks.voice);        
                }}
                        name="enabled"
                        bind:checked={$CurrentEditableState.additionalTasks.voice.enabled}
                />
                <p class="flex gap-1">{$t('tasks.additionalTask.voice')}
                    <IconMicrophone/>
                </p>
            </div>

            {#if $CurrentEditableState.additionalTasks?.voice.enabled}
                <div/>
                {#if $CurrentEditableState.isPublic}

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowAnonymous"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.voice.allowAnonymous}
                        />
                    </div>

                    <div class="w-1/4">
                        <BaseSwitch
                                name="allowRetry"
                                class="sm"
                                bind:checked={$CurrentEditableState.additionalTasks.voice.allowRetry}
                        />
                    </div>
                {:else}
                    <div class="w-1/4">
                        <BaseInputNumber name="redoLong"
                                         bind:value={$CurrentEditableState.additionalTasks.voice.delay}/>
                    </div>
                    <div class="w-1/4">
                        <BaseInputNumber name="redoPoints"
                                         bind:value={$CurrentEditableState.additionalTasks.voice.maxScore}/>

                    </div>
                {/if}
                <div class="flex items-center gap-2">
                    <BaseSwitch
                            name="hint"
                            bind:checked={$CurrentEditableState.additionalTasks.voice.hintEnabled}
                    />
                    <BaseButton size="md" className="mx-10" on:click={() => copyLink(TaskMode.voice)}>
                        <IconCopy size="16"/>
                    </BaseButton>
                </div>
            {/if}
        </div>
        <!--{/if}-->
    </div>

    <TaskIncentiveContent {incentiveContent} {taskIncentiveContent}/>
</div>



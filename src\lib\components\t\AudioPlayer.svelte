<script lang="ts">
    import {createEventDispatcher, onMount} from 'svelte';
    import {
        artist,
        index,
        isLoaded,
        isPlay,
        playMode,
        source,
        title
    } from "$lib/state/audio-player.state";
    import SongBar from "$components/t/SongBar.svelte";
    import _ from "lodash";
    import He from "$components/common/He.svelte";
    import {
        IconPlayerTrackNextFilled,
        IconPlayerPauseFilled,
        IconPlayerPlayFilled,
        IconPlayerTrackPrevFilled,
        IconVolume,
        IconVolumeOff,
        IconRepeatOnce,
        IconRepeat,
        IconArrowsShuffle
    } from '@tabler/icons-svelte';

    const dispacth = createEventDispatcher();
    export let tracks;
    export let finishings;
    $: if (_.every(finishings, x => !!x)) dispacth('allDone');
    let time = 0,
        muted = false,
        volume = 1,
        currentVolume,
        volumeSlider,
        ended,
        slider,
        duration,
        audio,
        playbackRate = 1;
    const playNextAfterEnded = async (index, audio) => {
        if (audio) {


            title.set(tracks[index]?.title);
            artist.set(tracks[index]?.artist);
            isLoaded.set(false);
            await source.set(tracks[index]?.filename);
            audio.playbackRate = playbackRate;
            (await audio.paused) ? await audio.play() : await audio.pause();
            (await audio.paused) ? isPlay.set(false) : isPlay.set(true);
        }
    };
    const showError = () => {
        alert('choose an audio');
    }
    const PLAY_MODE = ['normal', 'single', 'random'];
    const formatDuration = (seconds) => {
        if (_.isNaN(seconds) || !isFinite(seconds) || !_.isNumber(seconds)) return '...';
        let minutes = (Math.floor(seconds / 60)).toString();
        seconds = Math.floor(seconds % 60);
        if (minutes < 10) minutes = '0' + minutes;
        if (seconds < 10) seconds = '0' + seconds;
        return `${minutes}:${seconds}`;
    };
    onMount(async () => {
        let curIndex = 0
        title.set(tracks[curIndex]?.title);
        artist.set(tracks[curIndex]?.artist);
        isLoaded.set(false);
        await source.set(tracks[curIndex]?.filename);
        audio.onended = async () => {
            dispacth('listened');
            isPlay.set(false);
            time = 0;
            finishings[$index] = true;
            let lastSong = tracks.length - 1;
            if ($playMode === PLAY_MODE[0]) { // normal
                let nextSong = $index + 1;
                index.set(nextSong);
                if ($index > lastSong) {
                    index.set(0);
                    // playNextAfterEnded(0, audio);
                } else {
                    playNextAfterEnded(nextSong, audio);
                }
            } else if ($playMode === PLAY_MODE[2]) { // random
                const randomSong = tracks[Math.floor(Math.random() * tracks.length)];
                const randomIndex = tracks.indexOf(randomSong);
                index.set(randomIndex);
                playNextAfterEnded($index, audio);
            } else {
                playNextAfterEnded($index, audio);
            }
        };
    });
    const playAudio = async () => {
        if ($source) {
            audio.paused ? await audio.play() : await audio.pause();
            audio.paused ? isPlay.set(false) : isPlay.set(true);
        } else {
            showError();
        }
    };
    const seek = () => (time = slider.value);
    const seekVolume = () => (audio.volume = volumeSlider.value);
    const muteVolume = () => {
        muted = !muted;
        if (muted) {
            currentVolume = volumeSlider.value;
            volume = 0;
            volumeSlider.value = 0;
        } else {
            volumeSlider.value = currentVolume;
        }
    };
    const changeSong = async ({song}, i) => {
        index.set(i);
        title.set(song?.title);
        artist.set(song?.artist);
        await source.set(song?.filename);
        playAudio();
    };
    const lastSong = tracks.length - 1;
    const prevSong = () => {
        if ($source) {
            index.set($index - 1);
            if ($index < 0) {
                index.set(lastSong);
            }
            playNextAfterEnded($index, audio);
        } else {
            showError();
        }
    };
    const nextSong = () => {
        if ($source) {
            index.set($index + 1);
            if ($index > lastSong) {
                index.set(0);
            }
            playNextAfterEnded($index, audio);
        } else {
            showError();
        }
    };
    const changeMode = () => {
        if ($playMode === PLAY_MODE[0]) {
            playMode.set(PLAY_MODE[1]);
        } else if ($playMode === PLAY_MODE[1]) {
            playMode.set(PLAY_MODE[2]);
        } else {
            playMode.set(PLAY_MODE[0]);
        }
    };
    const x05 = () => {
        audio.playbackRate = 0.5;
        playbackRate = 0.5;
    }
    const x1 = () => {
        audio.playbackRate = 1;
        playbackRate = 1;
    }
    const x15 = () => {
        audio.playbackRate = 1.5;
        playbackRate = 1.5;
    }
</script>

<div>
    <div class="card p-4 rounded">
        <div class="flex justify-center">
            <p class="text-gray-400 text-center mb-1 text-lg font-semibold p-1 w-fit rounded">{$artist}</p>
        </div>
        <div class="flex justify-center">
            <h1 class="text-center mb-5 text-2xl font-semibold px-3 py-2 outline w-fit rounded">
                <He>{$title}</He>
            </h1>
        </div>

        <input
                type="range"
                on:input={seek}
                bind:this={slider}
                value={ended ? 0 : time}
                max={duration}
                class="h-2 w-full mt-5"
        />
        <div class="flex items-center justify-between mt-3 w-full mb-3">
            <p class="items-start min-w-[44px]">{formatDuration(time)}</p>
            {#if $source}
                <button class="flex text-center mx-auto p-2 !bg-primary-50-900-token rounded-xl cursor-pointer btn"
                        type="button" on:click={changeMode}>
                    {#if $playMode === PLAY_MODE[0]}
                        <IconRepeat/>
                    {:else if $playMode === PLAY_MODE[1]}
                        <IconRepeatOnce/>
                    {:else if $playMode === PLAY_MODE[2]}
                        <IconArrowsShuffle/>
                    {/if}
                </button>
            {/if}

            <p class="text-end min-w-[44px]">{formatDuration(duration)}</p>
        </div>


        <div class="flex justify-evenly items-center mt-10 w-full mb-1">
            <div class="flex p-2 !bg-primary-50-900-token rounded-xl cursor-pointer btn">
                <button type="button" on:click={prevSong}>
                    <IconPlayerTrackPrevFilled/>
                </button>
            </div>
            <div class="flex p-4 bg-primary-50-900-token rounded-xl cursor-pointer btn">
                <button type="button" on:click={playAudio}>
                    {#if !$isPlay || ended}
                        <IconPlayerPlayFilled/>
                    {:else}
                        <IconPlayerPauseFilled/>
                    {/if}
                </button>
            </div>
            <div class="flex p-2 !bg-primary-50-900-token rounded-xl cursor-pointer btn">
                <button type="button" on:click={nextSong}>
                    <IconPlayerTrackNextFilled/>
                </button>
            </div>
        </div>

        <div class="flex justify-between">
            <div class="flex flex-col items-center mt-12 mb-4">
                <button class="mb-3 p-2 !bg-primary-50-900-token rounded-xl cursor-pointer btn" type="button"
                        on:click={muteVolume}>
                    {#if muted}
                        <IconVolumeOff/>
                    {:else}
                        <IconVolume/>
                    {/if}
                </button>
                <input
                        type="range"
                        class="w-32 h-1"
                        min="0"
                        on:input={seekVolume}
                        bind:this={volumeSlider}
                        value={volume}
                        max="1"
                        step=".001"
                />
            </div>

            <div class="flex p-4 w-full justify-end mt-10">
                <div class="w-fit flex">
                    <button class="p-2 rounded-tl-xl rounded-bl-xl !bg-primary-50-900-token cursor-pointer btn h-fit w-fit {playbackRate === 0.5 ? 'outline' : ''} z-50"
                            type="button" on:click={x05}>
                        x0.5
                    </button>
                    <span class="divider-vertical h-5"></span>
                    <button class="p-2 px-4 !bg-primary-50-900-token cursor-pointer btn h-fit w-fit {playbackRate === 1 ? 'outline' : ''} z-50"
                            type="button" on:click={x1}>
                        x1
                    </button>
                    <span class="divider-vertical h-5"></span>
                    <button class="p-2 !bg-primary-50-900-token rounded-bl-xr rounded-tr-xl cursor-pointer btn h-fit {playbackRate === 1.5 ? 'outline' : ''} z-50"
                            type="button" on:click={x15}>
                        x1.5
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card-playlist">

        <div>
            {#each tracks as song, i}
                <SongBar on:click={() => changeSong({ song }, i)} {song} finished={finishings[i]}/>
            {/each}
        </div>
    </div>
</div>

<audio
        bind:duration
        bind:ended
        bind:muted
        bind:currentTime={time}
        bind:this={audio}
        on:loadeddata={() => isLoaded.set(true)}
        src={$source}>
</audio>
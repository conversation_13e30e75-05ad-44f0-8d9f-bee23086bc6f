{"title": "Группы", "new": "Создать", "table": {"head": {"name": "Название", "dateStart": "Дата начала", "language": "Язык", "level": "Уровень", "days": "Расписание", "hoursSchedule": "Смена", "whatsapp": "Вотсап ссылка", "comment": "Комментарий", "isActive": "Активность", "studentsCount": "Количество студентов", "statistics": "Статистика"}}, "filters": {"title": {"groupName": "Название группы", "active": "Активность", "type": "Тип", "lang": "Язык", "level": "Уровень", "hoursSchedule": "Смена"}, "values": {"hoursSchedule": {"morning": "Утро", "day": "День", "evening": "Вечер"}, "isActive": {"active": "Активный", "inActive": "Неактивный"}, "type": {"public": "sandbox", "ragil": "Обычная"}}}, "modal": {"title": {"create": "Создание группы", "update": "Обновление группы"}, "buttons": {"submit": "Создать", "cancel": "Отмена"}, "formFields": {"titles": {"hoursSchedule": "Смена", "startTime": "Начало", "endTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "capacity": "Кол-во часов обучения", "dateEnd": "Конечная дата", "dateStart": "Начальная дата", "lang": "Язык", "level": "Уровень", "sandbox": "sandbox", "whatsAppLink": "WhatsApp ссылка", "comment": "Коментарий", "name": "Имя", "active": "Активность", "totalHoursAmount": "Общее количество часов", "hoursSpendBySession": "Часы на сеанс"}, "values": {"hoursSchedule": {"morning": "Утро", "day": "День", "evening": "Вечер"}}}, "formFieldsErrors": {"daysSchedule": "Выберите хотя бы один день", "timeStart": "Время начала не должно быть пустым", "timeEnd": "Время окончания не должно быть пустым", "dateStart": "Дата начала не должна быть пустой ", "dateEnd": "Дата конца не должна быть пустой", "timeStartMoreThenTimeEnd": "Время начала не может быть больше времени окончания", "dateStartMoreThenDateEnd": "Дата начала не может быть больше даты окончания", "totalHoursAmountMin": "Общее количество часов не может быть меньше 10.", "totalHoursAmountMax": "Общее количество часов не может превышать 1000.", "hoursSpendBySessionMin": "Ча<PERSON>ы, потраченные на сеанс, не могут быть меньше 1", "hoursSpendBySessionMax": "Продолжительность сеанса не может превышать 10 часов.", "totalHoursAmountInvalidType": "Общее количество часов должно быть числом!", "hoursSpendBySessionInvalidType": "Часы, потраченные на сеанс, должны быть числом!"}}, "scheduleTable": {"title": "Изменения расписания", "tableTitles": {"date": "Дата", "days": "<PERSON><PERSON>и", "hoursPerSession": "Время занятия", "action": "Действие"}, "buttons": {"edit": "Редактировать"}, "modal": {"title": {"create": "Создать расписание", "update": "Обновить расписание"}, "formFields": {"date": "Дата", "days": "<PERSON><PERSON>и", "hoursPerSession": "Время занятия"}, "buttons": {"create": "Создать", "update": "Обновить", "cancel": "Отмена"}}, "notifications": {"create": "Расписание успешно создано", "update": "Расписание успешно обновлено"}}, "tabsName": {"tasks": "Задания", "students": "Студенты", "settings": "Настройки", "history": "История группы"}, "groupSettings": {"stop": "Остановить группу", "modal": {"stop": "Отсановить группу", "buttons": {"cancel": "Отмена", "save": "Сохранить"}, "notifications": {"success": "Группа была успешно остановлена"}}, "holidayTable": {"title": "Выходные", "typeLocal": "Локальный", "typeGlobal": "Глобальный", "tableTitles": {"date": "Дата", "comment": "Комментарий", "type": "Тип", "action": "Действие"}, "buttons": {"delete": "Удалить", "save": "Сохранить"}}}, "infoGroup": {"hoursSchedule": {"morning": "Утро", "day": "День", "evening": "Вечер"}, "isActive": {"active": "Активна", "inActive": "Неактивна"}, "isPublic": {"public": "Песочница", "nonpublic": "Обычная"}, "hours": "<PERSON>а<PERSON>ы", "days": "<PERSON><PERSON>и", "progress": "Прогресс"}, "studentAdditionalTask": {"title": "Задания", "table": {"name": "Название", "comment": "Комментарий", "done": "Сделал через", "result": "Результат", "attempts": "Попытки", "actions": "Действия"}, "breadcrumb": {"students": "Студенты"}}, "groupHistory": {"table": {"name": "ФИО", "group": "Текущая группа", "regDate": "Дата регистрации", "start": "Дата начала", "end": "Дата конца", "taskStart": "Дата начала задания", "comment": "Комментарий", "lastTaskScore": "Последний результат", "averageTaskScore": "Средний результат", "lastTaskDelay": "Время выполнения последнего", "averageTaskDelay": "Среднее время выполнения", "whatsapp": "Whatsapp", "statistics": "Статистика", "action": "Действие"}}, "notifications": {"create": {"success": "Создание группы прошло успешно"}, "update": {"success": "Обновление группы прошло успешно"}}, "edit": "Редактировать группу", "groups": "Группы", "info": "Информация о группе", "comment": "Комментарий"}
import {type ClassValue, clsx} from 'clsx';
import {twMerge} from 'tailwind-merge';

import {
    compareAsc,
    eachDayOfInterval,
    format,
    getHours,
    getMinutes,
    isMonday,
    isSameDay,
    isSunday,
    isThursday,
    isTuesday,
    isValid,
    isWednesday,
    parse,
    parseISO,
    startOfDay
} from 'date-fns';
import {HoursScheduleFilter, LanguageFilter, LevelFilter} from '$common/models/enums';
import type {ZodSchema} from 'zod';
import type {ValidationError} from '../models/validation-error.model';
import {generateGuid} from '$common/core/utils';
import type {DaysSchedule, GroupDto} from '$common/models/dtos/group.dto';
import {LoadingState} from '$lib/state/loading-state';
import {toDate} from "date-fns-tz";
import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";
import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";
import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";

export const mapEnumToOptionsWithTranslations = (
    enumObject: any,
    translationPath: string,
    translationFn: (textForTranslation: string) => string,
    withEmpty?: boolean
) => {
    const enumDefault = Object.keys(enumObject)
        .filter((key) => isNaN(Number(key))) // Filter out numeric keys
        .map((key) => {
            const enumValue = enumObject[key];
            const translationKey = `${translationPath}.${key}`;
            const translatedValue = translationFn(translationKey);

            return {
                value: enumValue,
                displayValue: translatedValue
            };
        });
    return withEmpty ? [{value: -1, displayValue: 'none'}, ...enumDefault] : enumDefault;
};

export const mapEnumToOptions = (enumObject: any, withEmpty?: boolean) => {
    const enumDefault = Object.keys(enumObject)
        .filter((key) => isNaN(Number(key))) // Filter out numeric keys
        .map((key) => {
            return {
                value: enumObject[key],
                displayValue: key
            };
        });
    return withEmpty ? [{value: -1, displayValue: 'none'}, ...enumDefault] : enumDefault;
};

export const serializeNonPOJOs = (value: object) => {
    return JSON.parse(JSON.stringify(value));
};

export const getCurrentDateTime = () => {
    const currentDate = new Date();
    const day = String(currentDate.getDate()).padStart(2, '0');
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const year = String(currentDate.getFullYear()).slice(-2);
    const hours = String(currentDate.getHours()).padStart(2, '0');
    const minutes = String(currentDate.getMinutes()).padStart(2, '0');

    return day + month + year + '_' + hours + minutes;
};

export const getTimeFromDateString = (dateString: string, withSeconds = false) => {
    const pattern = withSeconds ? 'HH:mm:ss' : 'HH:mm'

    const fixedDateString = format(new Date(), 'yyyy-MM-dd') + dateString.substring(10, dateString.length);
    return format(new Date(fixedDateString), pattern, {timeZone: 'Asia/Jerusalem'});
};

export const formatToPickerDate = (date: Date) =>
    `${date.getFullYear()}-${
        date.getMonth() < 9 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
    }-${date.getDate() < 10 ? '0' + date.getDate() : date.getDate()}`;


export const parseDateString = (dateStr: string, format = 'dd.MM.yyyy') => {
    const parsed = parse(dateStr, format, new Date());
    return isValid(parsed)
        ? parsed
        : isValid(parseISO(dateStr))
            ? parseISO(dateStr)
            : parse(dateStr, 'PPPPpp', new Date());
};

export const getDateString = (dateString: string, formatDate = 'yyyy-MM-dd') => {
    if (!dateString) return null;
    return format(parseDateString(dateString), formatDate);
};

export const mergeDateTime = (dateString: string, timeString: string) => {
    const dateObj = new Date(dateString);
    const timeObj = new Date(timeString);

    const mergedDate = new Date(dateObj);

    mergedDate.setHours(timeObj.getHours());
    mergedDate.setMinutes(timeObj.getMinutes());
    mergedDate.setSeconds(0);

    return toDate(mergedDate, {timeZone: 'Asia/Jerusalem'});
};

export const durationToMinutesToHuman = (d: Duration) =>
    d
        ? `${d.days && d.days > 0 ? d.days + 'd ' : ''}${d.hours && d.hours > 0 ? d.hours + 'h ' : ''}${
            d.minutes && d.minutes > 0 ? d.minutes + 'm ' : ''
        }`
        : '';

export const durationToSecondsToHuman = (d: Duration) =>
    d
        ? `${d.days && d.days > 0 ? d.days + 'd ' : ''}${d.hours && d.hours > 0 ? d.hours + 'h ' : ''}${
            d.minutes && d.minutes > 0 ? d.minutes + 'm ' : ''}${
            d.seconds && d.seconds > 0 ? d.seconds + 's ' : ''}`
        : '';

export const durationToCountdown = (d: Duration) => {
    if (d) {
        const formattedHours = d.hours && d.hours > 0 ? (d.hours < 10 ? '0' + d.hours : d.hours) : '00';
        const formattedMinutes =
            d.minutes && d.minutes > 0 ? (d.minutes < 10 ? '0' + d.minutes : d.minutes) : '00';
        const formattedSeconds =
            d.seconds && d.seconds > 0 ? (d.seconds < 10 ? '0' + d.seconds : d.seconds) : '00';
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    }

    return '';
};

// TODO: rename to handleScrollObserver
export const handleScroll = (funcToLoadData: () => void) => {
    return new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (
                entry.isIntersecting &&
                document.getElementById('page')!.scrollHeight > window.innerHeight
            ) {
                funcToLoadData();
            }
        });
    });
};

export const validateEntityBySchema = (entity: object, schema: ZodSchema) => {
    const validationResult = schema.safeParse(entity);

    let errors: ValidationError[] = [];
    const result = validationResult.success;
    if (!validationResult.success) {
        errors = validationResult.error.errors.map((error) => {
            return {
                field: error.path[0],
                message: error.message
            };
        });
    }

    return {result, errors};
};

export const DbFormatStringToDaysSchedule = (str: string) => {
    const regex = /^[01]{5}$/;
    const days = ['א', 'ב', 'ג', 'ד', 'ה'];
    const result = [];

    if (regex.test(str)) {
        for (let i = 0; i < str.length; i++) {
            if (str.charAt(i) === '1') {
                result.push(days[i]);
            }
        }

        return result.join(',');
    } else {
        return '';
    }
};

export const DbFormatStringToDaysScheduleObject = (dbFormatDaysSchedule: string) => {
    const days = ['Su', 'Mo', 'Tu', 'We', 'Th'];
    const daysSchedule: DaysSchedule = {Su: 0, Mo: 0, Tu: 0, We: 0, Th: 0};

    for (let i = 0; i < days.length; i++) {
        const day = days[i];
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        daysSchedule[day] = parseInt(dbFormatDaysSchedule[i]);
    }

    return daysSchedule as DaysSchedule;
};

export const daysScheduleToFormName = (daysSchedule: DaysSchedule) => {
    let daysScheduleFormName = '';

    for (const day in daysSchedule) {
        if (daysSchedule[day] === 1) {
            daysScheduleFormName += 1;
        } else {
            daysScheduleFormName += 0;
        }
    }

    return daysScheduleFormName === '00000' ? '' : daysScheduleFormName;
};

export const daysScheduleToDbFormatString = (daysSchedule: DaysSchedule) => {
    let activeDays = '';

    for (const day in daysSchedule) {
        if (daysSchedule[day] === 1) {
            activeDays += 1;
        } else {
            activeDays += 0;
        }
    }

    return activeDays;
};

const isScheduleCorrect = (schedule: {
    startDate: string | Date | undefined | null,
    daysOfWeek: string,
    hoursPerSession: number
}[]) => {
    return !(schedule.length < 1 ||
        !schedule.every((s) => s.startDate) ||
        schedule.some((s) => s.daysOfWeek === '00000') ||
        schedule.some((s) => s.hoursPerSession < 1) ||
        schedule.some((s) => s.hoursPerSession > 10)
    );

}


const isTheDayOfScheduleChange = (currentDate: Date, dayOfScheduleChange: Date) => {
    return isSameDay(currentDate, dayOfScheduleChange)
}

export const isWeekend = (currentDate: Date, globalHoliday?: (Date | string)[], localHoliday?: (Date | string)[], groupHolidayExceptions?: (Date | string)[] | undefined) => {
    const isGlobalHoliday = globalHoliday?.some(date => isSameDay(new Date(currentDate), new Date(date)));
    const isExceptions = groupHolidayExceptions?.some(date => isSameDay(new Date(currentDate), new Date(date)));
    const isLocalHoliday = localHoliday?.some(date => isSameDay(new Date(currentDate), new Date(date)));

    return (isGlobalHoliday || isLocalHoliday) && !isExceptions
}

export const isItStudyDay = (currenDay: Date, daysOfWeekArray: number[]) => {
    return daysOfWeekArray[currenDay.getDay()] === 1
}


export const calculateGroupEndDay = (
    schedule: GroupScheduleDto[],
    totalHours: number,
    holidays?: {
        globalH?: GeneralHolidayDto[],
        localH?: GroupHolidayDto[],
        groupHExceptions?: GroupHolidayExcDto[]
    }
) => {
    return getStudyStatisticsBySchedule(schedule, totalHours, holidays?.globalH, holidays?.localH, holidays?.groupHExceptions)?.endDate;
}

export const calculateGroupRemainingDays = (
    currentGroupStudyDays: number | null,
    endDate: Date,
    schedule: GroupScheduleDto[],
    totalHours: number,
    holidays?: {
        globalH?: GeneralHolidayDto[],
        localH?: GroupHolidayDto[],
        groupHExceptions?: GroupHolidayExcDto[]
    }
) => {
    const isEqualsDays = isSameDay(new Date(), new Date(endDate))
    const compareDate = compareAsc(new Date(), new Date(endDate));
    if (compareDate === -1 || isEqualsDays) {
        const totalStudyDays = getStudyStatisticsBySchedule(schedule, totalHours, holidays?.globalH, holidays?.localH, holidays?.groupHExceptions)?.totalStudyDays;
        return totalStudyDays && currentGroupStudyDays !== null ? totalStudyDays - currentGroupStudyDays : 0
    } else {
        return 0
    }
}

export const getCurrentGroupStudyHours = (
    endDate: Date,
    schedule: GroupScheduleDto[],
    totalHours: number,
    holidays?: {
        globalH?: GeneralHolidayDto[],
        localH?: GroupHolidayDto[],
        groupHExceptions?: GroupHolidayExcDto[]
    }) => {
    const isEqualsDays = isSameDay(new Date(), new Date(endDate))
    const compareDate = compareAsc(new Date(), new Date(endDate));
    if (compareDate === -1 || isEqualsDays) {
        return getStudyStatisticsBySchedule(schedule, totalHours, holidays?.globalH, holidays?.localH, holidays?.groupHExceptions)?.studyHours;
    } else {
        return totalHours
    }
}


export const calculateStudentStudyHoursAndDays = (data: {
    from: Date,
    to: Date,
    schedule: GroupScheduleDto[],
    totalHoursAmount: number,
    groupHolidays: GroupHolidayDto[],
    groupHolidaysExceptions: GroupHolidayExcDto[]
}[], generalHolidays: GeneralHolidayDto[]) => {
    let totalHours = 0;
    let totalDays = 0;
    if (data.length === 0) return {totalHours, totalDays}

    data.forEach((d, index) => {
        let to = d.to;
        let currentIndex = 0;
        let currentDay = new Date(d.from);

        // debugger;

        const scheduleArr = d.schedule?.map((gsch) => ({
            startDate: gsch.dateStart,
            daysOfWeek: gsch.daysSchedule,
            hoursPerSession: gsch.hoursPerSession,
            timeEnd: gsch?.group?.timeEnd
        }))
        if (!isScheduleCorrect(scheduleArr) || !d.totalHoursAmount || d.totalHoursAmount > 1000) return null;

        let daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
        let hoursPerSession = scheduleArr[currentIndex]?.hoursPerSession;
        let timeEnd = scheduleArr[currentIndex]?.timeEnd;
        const globalHoliday = generalHolidays.map((gh) => gh.date);
        const localHoliday = d.groupHolidays?.map((lh) => lh.date);
        const groupHolidayExceptions = d.groupHolidaysExceptions?.map((el) => el.generalHoliday?.date);


        if (!to) {
            to = new Date()
        }

        const compareDate = compareAsc(new Date(to), new Date(currentDay));


        if (compareDate !== -1) {
            while (!isSameDay(new Date(to), new Date(currentDay))) {
                if (currentIndex + 1 < scheduleArr.length && isTheDayOfScheduleChange(new Date(currentDay), new Date(scheduleArr[currentIndex + 1]?.startDate))) {
                    currentIndex += 1;
                    currentDay = new Date(scheduleArr[currentIndex]?.startDate);
                    daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
                    hoursPerSession = scheduleArr[currentIndex]?.hoursPerSession
                }
                if (isWeekend(new Date(currentDay), globalHoliday, localHoliday, groupHolidayExceptions)) {
                    currentDay.setDate(currentDay.getDate() + 1);
                    continue;
                }
                if (isItStudyDay(new Date(currentDay), daysOfWeekArray)) {
                    totalHours += hoursPerSession;
                    totalDays += 1;
                }
                currentDay.setDate(currentDay.getDate() + 1);
            }
            if (isSameDay(new Date(), new Date(currentDay))) {
                if (!isEndTimeGreaterCurrentTime(timeEnd)) {
                    totalHours += hoursPerSession;
                    totalDays += 1;
                }
            }
        }
    });

    return {
        totalHours,
        totalDays
    }
}


export const getCurrentGroupStudyDays = (
    endDate: Date,
    schedule: GroupScheduleDto[],
    totalHours: number,
    holidays?: {
        globalH?: GeneralHolidayDto[],
        localH?: GroupHolidayDto[],
        groupHExceptions?: GroupHolidayExcDto[]
    }
) => {

    const scheduleArr = schedule?.map((gsch) => ({
        startDate: gsch.dateStart,
        daysOfWeek: gsch.daysSchedule,
        hoursPerSession: gsch.hoursPerSession,
        timeEnd: gsch?.group?.timeEnd
    }))
    if (!isScheduleCorrect(scheduleArr) || !totalHours || totalHours > 1000) return null;
    const globalHoliday = holidays?.globalH?.map((gh) => gh.date);
    const localHoliday = holidays?.localH?.map((lh) => lh.date);
    const groupHolidayExceptions = holidays?.groupHExceptions?.map((el) => el.generalHoliday?.date)
    let currentIndex = 0;
    let currentDay = new Date(scheduleArr[currentIndex]?.startDate);
    let daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
    let timeEnd = scheduleArr[currentIndex]?.timeEnd;


    const isTodayBiggerStartDay = compareAsc(startOfDay(new Date()), startOfDay(new Date(currentDay)));


    if (isTodayBiggerStartDay < 0) {
        return 0;
    }


    const compareDate = compareAsc(new Date(), new Date(endDate));
    let studyDaysOnThisTime = 0;

    if (compareDate < 0 || isSameDay(new Date(), new Date(endDate))) {
        while (!isSameDay(new Date(currentDay), new Date())) {
            if (currentIndex + 1 < schedule.length && isTheDayOfScheduleChange(new Date(currentDay), new Date(scheduleArr[currentIndex + 1]?.startDate))) {
                currentIndex += 1;
                currentDay = new Date(scheduleArr[currentIndex]?.startDate);
                daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
            }
            if (isWeekend(new Date(currentDay), globalHoliday, localHoliday, groupHolidayExceptions)) {
                currentDay.setDate(currentDay.getDate() + 1);
                continue;
            }
            if (isItStudyDay(new Date(currentDay), daysOfWeekArray)) {
                studyDaysOnThisTime += 1;
            }
            currentDay.setDate(currentDay.getDate() + 1);
        }


        if (!isEndTimeGreaterCurrentTime(timeEnd)) {
            studyDaysOnThisTime += 1;
        }
    } else {
        while (!isSameDay(new Date(currentDay), new Date(endDate))) {
            if (currentIndex + 1 < schedule.length && isTheDayOfScheduleChange(new Date(currentDay), new Date(scheduleArr[currentIndex + 1].startDate))) {
                currentIndex += 1;
                currentDay = new Date(scheduleArr[currentIndex]?.startDate);
                daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
            }
            if (isWeekend(new Date(currentDay), globalHoliday, localHoliday, groupHolidayExceptions)) {
                currentDay.setDate(currentDay.getDate() + 1);
                continue;
            }
            if (isItStudyDay(new Date(currentDay), daysOfWeekArray)) {
                studyDaysOnThisTime += 1;
            }
            currentDay.setDate(currentDay.getDate() + 1);
        }
        studyDaysOnThisTime += 1;
    }

    return studyDaysOnThisTime;
}


const getStudyStatisticsBySchedule = (
    schedule: GroupScheduleDto[],
    totalHours: number,
    globalH?: GeneralHolidayDto[],
    localH?: GroupHolidayDto[],
    groupHExceptions?: GroupHolidayExcDto[]) => {

    const scheduleArr = schedule?.map((gsch) => ({
        startDate: gsch.dateStart,
        daysOfWeek: gsch.daysSchedule,
        hoursPerSession: gsch.hoursPerSession,
        timeEnd: gsch?.group?.timeEnd
    }));

    if (!isScheduleCorrect(scheduleArr) || !totalHours || totalHours > 1000) return null;
    const globalHoliday = globalH?.map((gh) => gh.date);
    const localHoliday = localH?.map((lh) => lh.date);
    const groupHolidayExceptions = groupHExceptions?.map((el) => el.generalHoliday?.date)

    let currentIndex = 0;
    let currentDay = new Date(scheduleArr[currentIndex]?.startDate);
    let daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
    let hoursPerSession = scheduleArr[currentIndex]?.hoursPerSession;
    let timeEnd = scheduleArr[currentIndex]?.timeEnd;


    let studyHoursOnThisTime = 0;
    let hoursAccumulated = 0;
    let totalStudyDays = 0;


    while (hoursAccumulated < totalHours) {
        if (isSameDay(new Date(currentDay), new Date())) {
            studyHoursOnThisTime = hoursAccumulated;
            if (!isEndTimeGreaterCurrentTime(timeEnd)) {
                studyHoursOnThisTime += hoursPerSession;
            }
        }
        if (currentIndex + 1 < scheduleArr.length && isTheDayOfScheduleChange(new Date(currentDay), new Date(scheduleArr[currentIndex + 1].startDate))) {
            currentIndex += 1;
            currentDay = new Date(scheduleArr[currentIndex]?.startDate);
            daysOfWeekArray = scheduleArr[currentIndex]?.daysOfWeek.split('').map(Number);
            hoursPerSession = scheduleArr[currentIndex]?.hoursPerSession
        }

        if (isWeekend(new Date(currentDay), globalHoliday, localHoliday, groupHolidayExceptions)) {
            currentDay.setDate(currentDay.getDate() + 1);
            continue;
        }

        if (isItStudyDay(new Date(currentDay), daysOfWeekArray)) {
            hoursAccumulated += hoursPerSession;
            totalStudyDays += 1;
        }
        currentDay.setDate(currentDay.getDate() + 1);
    }


    return {
        endDate: new Date(currentDay.setDate(currentDay.getDate() - 1)),
        studyHours: studyHoursOnThisTime,
        totalStudyDays: totalStudyDays
    }
}


const isEndTimeGreaterCurrentTime = (timeEndGroup: Date) => {
    const hours1 = getHours(new Date(timeEndGroup));
    const minutes1 = getMinutes(new Date(timeEndGroup));
    const hours2 = getHours(new Date());
    const minutes2 = getMinutes(new Date());

    const comparisonResult = compareAsc(new Date(0, 0, 0, hours1, minutes1), new Date(0, 0, 0, hours2, minutes2));

    return comparisonResult === 1 || comparisonResult === 0;
}


export const calculationGroupStudyingTime = (
    daysSchedule: DaysSchedule,
    timeStart: string,
    timeEnd: string,
    startDate: string,
    endDate: string
) => {
    if (!daysSchedule || !timeStart || !timeEnd || !startDate || !endDate) return '';
    const formattedDate = getDateString(startDate);
    const startDateTime = new Date(`${formattedDate}T${timeStart}:00`);
    const endDateTime = new Date(`${formattedDate}T${timeEnd}:00`);
    if (startDateTime > endDateTime) return '';
    const workHours = (endDateTime - startDateTime) / (1000 * 60 * 60);
    const dateRange = eachDayOfInterval({
        start: parseDateString(startDate),
        end: parseDateString(endDate)
    });

    const workingDaysCount = dateRange.reduce((count, date) => {
        if (
            (daysSchedule.Mo && isMonday(date)) ||
            (daysSchedule.Tu && isTuesday(date)) ||
            (daysSchedule.We && isWednesday(date)) ||
            (daysSchedule.Th && isThursday(date)) ||
            (daysSchedule.Su && isSunday(date))
        ) {
            return count + 1;
        }
        return count;
    }, 0);

    const totalHours = workingDaysCount * workHours;
    const hours = Math.floor(totalHours);
    const minutes = Math.round((totalHours - hours) * 60);

    return `${hours}:${minutes.toString().padStart(2, '0')}`;
};

export const createNameGroup = (
    startDate: string,
    hoursSchedule: number,
    lvl: number,
    language: 'EN' | 'RU'
) =>
    `${format(new Date(startDate), 'dd.MM.yyyy')}_${HoursScheduleFilter[hoursSchedule]}_${
        LanguageFilter[language]
    }_${LevelFilter[lvl]}`;

export const convertStringTimeToDate = (time: string) => {
    const date = new Date();
    const timeParts = time.split(':');
    if (timeParts && timeParts.length > 1) {
        date.setHours(+timeParts[0]);
        date.setMinutes(+timeParts[1]);

        return date;
    }

    return new Date();
};

export const createFormDataFromObject = (
    id: string,
    name: string,
    level: number,
    lang: string,
    isPublic: boolean,
    isActive: boolean,
    hoursSchedule: number,
    daysSchedule: DaysSchedule,
    whatsappUrl: string,
    comment: string,
    timeStart: string,
    timeEnd: string,
    dateStart: string,
    dateEnd: string
) => {
    const formData = new FormData();

    const objToSubmit: GroupDto = {
        id: id ? id : generateGuid(),
        name,
        level,
        lang,
        isPublic,
        isActive,
        hoursSchedule,
        daysSchedule: daysScheduleToDbFormatString(daysSchedule),
        whatsappUrl,
        comment,
        timeStart: convertStringTimeToDate(timeStart),
        timeEnd: convertStringTimeToDate(timeEnd),
        dateStart: new Date(dateStart),
        dateEnd: new Date(dateEnd)
    };

    for (const key in objToSubmit) {
        formData.append(key, objToSubmit[key]);
    }

    return formData;
};

export const checkOnErrorField = (
    modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined,
    fieldName: string
) => {
    let message = '';
    if (modalErrors) {
        modalErrors.filter((element) => {
            if (element.field === fieldName) {
                message = element.message;
            }
        });
    }
    return message;
};

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export const getModalOptions = (component: { ref: any }) => {
    return {
        type: 'component',
        component
    };
};

export const getTimeByHoursSchedule = (hoursSchedule: HoursScheduleFilter) => {
    const times: Record<HoursScheduleFilter, { startInitialTime: string; endInitialTime: string }> = {
        [HoursScheduleFilter.בוקר]: {startInitialTime: '08:30', endInitialTime: '12:55'},
        [HoursScheduleFilter.צהריים]: {startInitialTime: '13:00', endInitialTime: '17:00'},
        [HoursScheduleFilter.ערב]: {startInitialTime: '17:15', endInitialTime: '20:30'}
    };

    return Object.values(HoursScheduleFilter).includes(hoursSchedule as HoursScheduleFilter)
        ? times[hoursSchedule]
        : times[HoursScheduleFilter.בוקר];
};

export const loadingWrap = async (func: () => Promise<any>) => {
    LoadingState.set(true);
    return await func().finally(() => LoadingState.set(false));
};

export const switchHebrewFont = (hebrewToggle: boolean) => {
    return hebrewToggle ? 'font-arimo' : 'font-levin';
};

export const getDatetimeLocal = (date: Date) => {
    const ten = (i: number) => {
            return (i < 10 ? '0' : '') + i;
        },
        YYYY = date.getFullYear(),
        MM = ten(date.getMonth() + 1),
        DD = ten(date.getDate()),
        HH = ten(date.getHours()),
        II = ten(date.getMinutes()),
        SS = ten(date.getSeconds())
    ;
    return YYYY + '-' + MM + '-' + DD + 'T' +
        HH + ':' + II + ':' + SS;
};

export const getTimeLocal = (date: Date) => {
    const ten = (i: number) => {
            return (i < 10 ? '0' : '') + i;
        },
        HH = ten(date.getHours()),
        II = ten(date.getMinutes());

    return HH + ':' + II;
};

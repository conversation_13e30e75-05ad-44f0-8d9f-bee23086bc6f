import {db} from '../service-clients/db';
import {generateGuid} from '$common/core/utils';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import type {ShortStudentRequestDto} from '$common/models/dtos/student-request.dto';
import type {StudentRequestDto} from '$common/models/dtos/student-request.dto';
import type {ExtendedStudentDto, StudentDto} from '$common/models/dtos/student.dto';
import {mapper} from '$common/core/mapper';
import type {StudentFilterDto} from '$common/models/filters/student-filter.dto';
import type {
    StopCurrentGroupDto,
    StudentGroupsDto,
    SwitchGroupDto,
    UpdateStudentGroupDto
} from '$common/models/dtos/student-groups.dto';
import {add, addDays, addMinutes, addSeconds, intervalToDuration, isFuture, isPast} from 'date-fns';
import type {BaseSortingDto} from '$common/models/filters/base-filter.dto';
import {serializeNonPOJOs} from '$lib/common/utils';
import {
    getStudentRequestObjectFromMapEn,
    getStudentRequestObjectFromMapRu
} from '$lib/common/student-helpers';
import type {StudentsRequestsFilterDto} from "$common/models/filters/students-requests-filter.dto";
import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";


export const getStudents = async (filter: StudentFilterDto, sort?: BaseSortingDto): Promise<TableDataDto<ExtendedStudentDto>> => {
    const where = composeWhereClause(filter);
    const count = await db.students.count({where});
    const students = await db.students.findMany({
        take: filter.take === -1 ? undefined : filter.take,
        skip: filter.skip,
        include: {
            groupsStudents: {
                include: {
                    group: {
                        include: {
                            groupHoliday: true,
                            groupHolidaysExceptions: {
                                select: {
                                    generalHoliday: true
                                }
                            },
                            groupScheduleChanges: true
                        }
                    }
                },
                orderBy: {createdAt: 'desc'}
            },
            results: {
                select: {
                    results: true,
                    task: true
                }
            }
        },
        where,
        orderBy: sort ? {[sort.sortBy!]: sort?.sortDir} : undefined
    });


    const dtos = students?.map((x) => {
        const currentGroup = x.groupsStudents.length > 0 ? x.groupsStudents[0]?.group : null;
        const lastTaskDelayDuration = intervalToDuration({
            start: new Date(),
            end: addMinutes(new Date(), +x.lastTaskDelay.toString())
        });
        const averageTaskDelayDuration = intervalToDuration({
            start: new Date(),
            end: addMinutes(new Date(), +x.averageTaskDelay.toString())
        });
        const studentLearningHistory = x.groupsStudents.map((gs) => ({
            groupId: gs.group.id,
            from: gs.dateStartActual,
            to: gs.dateEndActual,
            totalHoursAmount: gs.group.totalHoursAmount,
            schedule: [...gs.group.groupScheduleChanges],
            groupHolidays: [...gs.group.groupHoliday],
            groupHolidaysExceptions: [...gs.group.groupHolidaysExceptions]
        })).flat()
        return {
            id: x.id,
            tz: x.tz,
            email: x.email,
            firstname: x.firstname,
            lastname: x.lastname,
            whatsApp: x.whatsapp,
            phone: x.phone,
            comment: x.comment,
            dob: x.dob,
            document1url: x.document1url,
            document2url: x.document2url,
            currentGroup: currentGroup?.name ?? '',
            currentGroupStartDate: x.currentGroupStartDate,
            registrationDate: x.registrationDate,
            lastTaskScore: x.lastTaskScore,
            averageTaskScore: x.averageTaskScore,
            lastTaskDelay: lastTaskDelayDuration,
            averageTaskDelay: averageTaskDelayDuration,
            studentLearningHistory
        };
    });

    return {data: dtos as any, count};
};


export const getStudentGroupsWithScheduleAndHolidays = async () => {
    const data = await db.students.findMany({
        include: {
            groupsStudents: {
                include: {
                    group: {
                        include: {
                            groupHoliday: true,
                            groupHolidaysExceptions: true,
                            groupScheduleChanges: true
                        }
                    }
                }
            }
        }
    });
    return data;
}

const composeWhereClause = (filter: StudentFilterDto): Record<string, any> => {
    const whereClause: Record<string, any> = {};

    if (filter.onlyIsActive) {
        whereClause.groupsStudents = {
            some: {
                groupId: filter.groupId,
                OR: [
                    {
                        dateEndActual: null
                    },
                    {
                        dateEndActual: {
                            gte: new Date()
                        }
                    }
                ]
            }
        }
    }

    if (filter.search) {
        whereClause.OR = [
            {
                firstname: {
                    contains: filter.search
                }
            },
            {
                lastname: {
                    contains: filter.search
                }
            },
            {
                tz: {
                    contains: filter.search
                }
            }
        ]
    }

    if (filter.groupId) {
        whereClause.groupsStudents = {
            some: {
                groupId: filter.groupId,
                OR: [
                    {
                        dateEndActual: null
                    },
                    {
                        dateEndActual: {
                            gte: new Date()
                        }
                    }
                ]
            }
        }
        // whereClause.groupsStudents = {
        //     some: {
        //         groupId: {
        //             equals: filter.groupId
        //         }
        //     }
        // };
    }

    return whereClause;
};


export const getStudentByTZ = async (tz: string) => {
    const student = await db.students.findFirst({
        where: {
            tz
        }
    });
    if (!student) return null;
    return mapper<StudentDto, unknown>(student);
};

export const updateStudent = async (student: StudentDto) => {

    const dataToUpdate = {...student, dob: add(new Date(student.dob), {hours: 6})};

    const data = await db.students.update({
        where: {
            id: student.id
        },
        data: dataToUpdate
    });


    return mapper<StudentDto, unknown>(serializeNonPOJOs(data));
};

export const handleStudentRequest = async (studentRequest: StudentRequestDto) => {
    const studentRequestFromDb = await db.student_requests.findUnique({
        where: {id: studentRequest.id}
    });
    const group = await db.groups.findUnique({where: {id: studentRequest.groupId}});
    if (!group) throw new Error('no such group');

    const data = await db.$transaction(
        async (tx) => {
            const studentDataToSave = {
                tz: studentRequest.tz,
                email: studentRequest.email,
                firstname: studentRequest.firstname,
                lastname: studentRequest.lastname,
                dob: new Date(studentRequest.dob),
                phone: studentRequest.phone,
                whatsapp: studentRequest.whatsapp,
                currentGroup: group.id,
                currentGroupStartDate: new Date(studentRequest.learnStartDate),
                comment: studentRequest.comment,
                document1url: studentRequest.teudatOleUrl,
                document2url: studentRequest.photoUrl,
                registrationDate: new Date(studentRequestFromDb?.createdAt ?? new Date())
            };


            const student = studentRequest.isExistingStudent
                ? await tx.students.update({
                    where: {tz: studentRequest.tz},
                    data: studentDataToSave
                })
                : await tx.students.create({
                    data: {...studentDataToSave, id: generateGuid()}
                });

            await tx.students_groups.create({
                data: {
                    id: generateGuid(),
                    studentId: student.id,
                    groupId: group.id,
                    dateStartActual: new Date(studentRequest.learnStartDate),
                    dateStartTasks: new Date(studentRequest.dateStartTasks)
                }
            });

            await tx.student_requests.update({
                where: {
                    id: studentRequest.id
                },
                data: {
                    isHandled: true
                }
            });
        },
        {
            maxWait: 5000,
            timeout: 10000
        }
    );


};

export const deleteStudentRequest = async (id: string) => {
    await db.student_requests.delete({
        where: {
            id
        }
    });
    return {success: true};
}

export const getStudentRequests = async (filter: StudentsRequestsFilterDto): Promise<TableDataDto<ShortStudentRequestDto>> => {
    const where = studentsRequestscomposeWhereClause(filter);
    const count = await db.student_requests.count({where});
    const data = await db.student_requests.findMany({
        where,
        take: filter.take,
        skip: filter.skip,
        orderBy: {createdAt: 'desc'}
    });

    const dtos = data.map((x) => mapper<ShortStudentRequestDto, unknown>(x));

    return {data: dtos, count};
};

const studentsRequestscomposeWhereClause = (filter: StudentsRequestsFilterDto) => {
    const whereClause: Record<string, any> = {isHandled: false};
    if (filter.search) {
        whereClause.OR = [
            {
                firstname: {
                    contains: filter.search
                }
            },
            {
                lastname: {
                    contains: filter.search
                }
            },
            {
                tz: {
                    contains: filter.search
                }
            }
        ]
    }
    return whereClause;
}

export const getGroupsHistoryByStudentId = async (id: string) => {
    const studentGroups = await db.students_groups.findMany({
        where: {
            studentId: id
        },
        include: {
            group: {
                include: {
                    groupHoliday: true,
                    groupHolidaysExceptions: {
                        select: {
                            generalHoliday: true,
                        }
                    },
                    groupScheduleChanges: true
                }
            }
        }
    });


    return studentGroups.map((element) => mapper<StudentGroupsDto, unknown>({...element}));
};

export const createStudentRequest = async (studentRequest: ShortStudentRequestDto) => {

    if (!await checkUnhandledStudentRequestTzExisting(studentRequest.tz)) {

        return await db.student_requests.create({
            data: {
                ...studentRequest,
                id: generateGuid(),
                city: studentRequest.city.substring(0, 35),
                groupStartDate: !studentRequest.groupStartDate ? null : studentRequest.groupStartDate
            }
        });
    }

    return null;
};

export const checkUnhandledStudentRequestTzExisting = async (tz: string) => {
    return await db.student_requests.findFirst({where: {tz}});//, isHandled: false}})
}

export const deleteStudentGroup = async (id: string) => {
    const data = db.students_groups.delete({
        where: {
            id
        }
    });

    return mapper<StudentGroupsDto, unknown>(data);
};

export const updateStudentGroup = async (dto: UpdateStudentGroupDto) => {
    const studentGroupToUpdate = await db.students_groups.findFirst({where: {id: dto.id}});
    const student = await db.students.findFirst({where: {id: dto.studentId}});
    const currentGroupChanges = dto.dateEndActual && isPast(new Date(dto.dateEndActual))
        ? {currentGroup: null, currentGroupStartDate: null}
        : {currentGroup: dto.groupId, currentGroupStartDate: new Date(dto.dateStartActual!)};
    const {dateStartActual, dateStartTasks, dateEndActual, groupId, studentId, id} = dto;
    const data = await db.$transaction(
        async (tx) => {
            if (student?.currentGroup && student?.currentGroup === studentGroupToUpdate?.groupId) {
                await tx.students.update({
                    where: {
                        id: studentId
                    },
                    data: currentGroupChanges
                });
            }

            return await tx.students_groups.update({
                where: {
                    id: id
                },
                data: {
                    groupId: groupId,
                    dateStartActual: dateStartActual ? new Date(dateStartActual) : null,
                    dateStartTasks: dateStartTasks ? new Date(dateStartTasks) : null,
                    dateEndActual: dateEndActual ? new Date(dateEndActual) : null
                }
            });
        },
        {
            maxWait: 5000,
            timeout: 10000
        }
    );

    return mapper<StudentGroupsDto, unknown>(data);
};

export const setDateEndActualToCurrentGroup = async (dto: StopCurrentGroupDto) => {
    const {dateEndActual, studentId, id} = dto;
    const data = await db.$transaction(async (tx) => {
        if (!isFuture(new Date(dateEndActual))) {
            await tx.students.update({
                where: {
                    id: studentId
                },
                data: {
                    currentGroup: null
                }
            });
        }

        return await tx.students_groups.update({
            where: {
                id: id
            },
            data: {
                dateEndActual: new Date(dateEndActual),
                dateEndTasks: addDays(new Date(dateEndActual), 1)
            }
        });
    });
    return mapper<StudentGroupsDto, unknown>(data);
};

export const switchStudentGroup = async (dto: SwitchGroupDto) => {
    const {studentId, dateStartSelectedGroup, taskStartSelectedGroup, idSelectedGroup} = dto;
    const data = await db.$transaction(
        async (tx) => {
            const newGroup = await tx.groups.findFirst({
                where: {
                    id: idSelectedGroup
                },
                select: {
                    id: true
                }
            });

            const newStudentGroup = await tx.students_groups.create({
                data: {
                    id: generateGuid(),
                    dateStartTasks: taskStartSelectedGroup ? new Date(taskStartSelectedGroup) : null,
                    groupId: idSelectedGroup,
                    dateStartActual: new Date(dateStartSelectedGroup),
                    studentId: studentId
                }
            });

            if (!isFuture(new Date(dateStartSelectedGroup))) {
                await tx.students.update({
                    where: {
                        id: studentId
                    },
                    data: {
                        currentGroup: newGroup?.id,
                        currentGroupStartDate: new Date(dto.dateStartSelectedGroup)
                    }
                });
            }


            return newStudentGroup;
        },
        {
            maxWait: 5000, // default: 2000
            timeout: 10000 // default: 5000
        }
    );

    return mapper<StudentGroupsDto, unknown>(data);
};

export const extractStudentRequest = (data: any, lang: 'ru' | 'en'): ShortStudentRequestDto => {
    const {answers} = data;
    const answerMap = new Map(answers.map((answer: any) => [answer.q, answer]));

    return lang === 'en'
        ? getStudentRequestObjectFromMapEn(answerMap)
        : getStudentRequestObjectFromMapRu(answerMap);
};

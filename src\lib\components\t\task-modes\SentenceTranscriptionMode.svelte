<script lang="ts">
	import type { TaskResultDto } from '$common/models/dtos/task.dto';
	import BaseButton from '$components/common/BaseButton.svelte';
	import { createEventDispatcher } from 'svelte';
	import { initialDate } from '$lib/state/task-current-completion.state';
	import { checkTranslationResponse, isInitialDate } from '$lib/common/task-helpers';
	import { t } from '$lib/i18n/config';
	import SentenceWithTranscription from '../SentenceWithTranscription.svelte';

	const dispatcher = createEventDispatcher();

	export let model: TaskResultDto;
	export let task: any;
	export let maxScore = 100;
	export let disabled = false;

	$: completions = model?.completions;
	$: revealMode = !isInitialDate(model.finishedAt, initialDate);

	const check = () => {
		checkTranslationResponse(model, maxScore);
		dispatcher('submitTask');
	};
</script>

{#each completions as sentence, i}
	<SentenceWithTranscription
		index={i + 1}
		{revealMode}
		{disabled}
		bind:sentence
		on:inputStarted|once={() => {
			if (i === 0) model.startedAt = new Date();
		}}
	/>
{/each}

{#if !revealMode}
	<BaseButton on:click={check}>{$t('t.button')}</BaseButton>
{/if}

import type {ExclusionsDto} from "$common/models/dtos/Exclusions.dto";
import {db} from "$api/core/service-clients/db";
import {mapper} from "$common/core/mapper";


export const getExclusions = async () => {
    const count = await db.exclusions.count();
    const data = await db.exclusions.findMany({
        select: {
            id: true,
            exclusions: true
        },
        orderBy: {
            createdAt: 'desc'
        }
    });
    const dtos = data.map((x) => mapper<ExclusionsDto, unknown>(x));
    return {data: dtos, count};
};

export const updateExclusions = async (exclusion: ExclusionsDto) => {
    const data = await db.exclusions.update({
        where: {
            id: exclusion.id
        },
        data: {
            exclusions: exclusion.exclusions
        }
    });
    return mapper<ExclusionsDto, unknown>(data)
}


export const deleteExclusions = async (id:string)=>{
    const data = await db.exclusions.delete({
        where:{
            id
        }
    });
    return mapper<ExclusionsDto, unknown>(data);
}

export const createExclusions = async (exclusion: ExclusionsDto) => {
    const data = await db.exclusions.create({
        data: {...exclusion}
    });
    return mapper<ExclusionsDto, unknown>(data)
}
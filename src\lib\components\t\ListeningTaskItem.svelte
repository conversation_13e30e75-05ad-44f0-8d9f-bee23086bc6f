<script lang="ts">
	import { page } from '$app/stores';
	import {createEventDispatcher} from "svelte";

	const dispatcher = createEventDispatcher()

	export let index: number;
	export let audioUrl: string;

	export let done = false;

	export let audio: HTMLAudioElement;


	export const play = () => {
		audio.play();
	};

	export const pause = () => {
		audio.pause();
	};

	const composeAudioToPlay = (fileName) =>
		fileName
			? `https://${$page.data.envs.VITE_S3_BUCKET}.s3.${$page.data.envs.VITE_S3_REGION}.amazonaws.com/${$page.data.envs.VITE_SENTENCESAUDIO_FOLDERNAME}/${fileName}`
			: undefined;

	const listened = () => {
		done = true;
		dispatcher('listened');
	}

</script>

<div dir="ltr" class="w-full flex flex-row items-center my-3">
	<span class="text-2xl mr-5">{index}.</span>
		<div class="w-full flex">
			<audio bind:this={audio} class="w-full" on:play={() => dispatcher('play')} on:ended={listened} controls src={composeAudioToPlay(audioUrl)}></audio>
		</div>

	{#if done}
		<div class="ml-4">
			<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-circle-check text-success-500" width="48" height="48" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
				<path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
				<path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"></path>
				<path d="M9 12l2 2l4 -4"></path>
			</svg>
		</div>
	{/if}
</div>

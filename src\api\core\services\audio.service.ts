import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { config } from '../config';

export const uploadAudio = async (file: File) => {
	try {
		const ab= await file.arrayBuffer();
		const s3 = new S3Client({
			region: 'eu-central-1',
			credentials: {
				accessKeyId: config.s3.ACCESS,
				secretAccessKey: config.s3.SECRET
			}
		});

		const params = {
			Bucket: config.s3.BUCKET,
			Key: `${config.s3.SENTENCESAUDIO_FOLDERNAME || ''}/${file.name}`,
			ContentType: 'audio/mp3',
			Body: Buffer.from(ab),
			ACL: 'public-read'
		};

		const command = new PutObjectCommand(params);
		const response = await s3.send(command);

		return response && response?.$metadata?.httpStatusCode === 200;

	} catch (error) {
		console.log(error);
		throw new Error('Error uploading content to the storage...');
	}
};
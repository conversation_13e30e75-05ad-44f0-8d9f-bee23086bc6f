{"title": "<PERSON><PERSON><PERSON><PERSON>", "editButton": "Edit", "handledButton": "Handled", "tabs": {"complaints": "<PERSON><PERSON><PERSON><PERSON>", "statistic": "Complaints statistic"}, "table": {"titles": {"sentence": "Sentence", "task": "Task", "actions": "Actions", "complaints": "Number of complaints", "description": "Description of complaint", "latestUpdater": "Last update"}, "student": "Student"}, "modal": {"title": "<PERSON><PERSON><PERSON><PERSON> about the sentence", "placeholder": "Write your complaint...", "cancelButton": "Cancel", "confirmButton": "Send"}, "filter": {"handledComplaintFilter": {"none": "–", "handled": "Handled", "notHandled": "Not handled"}, "titleHandled": "Is handled", "titleUpdater": "Last update"}, "notifications": {"toComplain": {"success": "<PERSON><PERSON><PERSON><PERSON> successfully sent", "error": "An error occurred!"}, "handled": {"success": "<PERSON><PERSON><PERSON><PERSON> successfully handled", "error": "An error occurred!"}}, "statistic": {"title": "Complaints statistic", "filter": {"titleUpdate": "Latest update"}, "table": {"teacherName": "Teacher name", "numberOfComplaints": "Number of complaints", "numberOfSentences": "Number of sentences", "numberOfSentencesHandled": "Number of sentences handled", "numberOfSentencesNotHandled": "Number of sentences not handled", "sentences": "Sentences"}}}
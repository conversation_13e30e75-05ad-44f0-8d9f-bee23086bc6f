import {writable} from 'svelte/store';
import type {BaseSortingDto} from "$common/models/filters/base-filter.dto";

export const pageSize = 25;
export const initialStudentPaging = {
    take: pageSize,
    skip: 0
};

export const StudentPagingState = writable({...initialStudentPaging});

export const initialStudentSorting: BaseSortingDto = {
    sortBy: undefined,
    sortDir: 'asc',
};
export const StudentSortingState = writable({...initialStudentSorting});
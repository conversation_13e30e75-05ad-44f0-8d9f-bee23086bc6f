import {
    createUser,
    updateUser
} from '$api/core/services/user.service';
import {type Actions, fail, type RequestEvent} from '@sveltejs/kit';
import {userSchemaForCreatingUser, userSchemaForUpdatingUser} from '$lib/validation-schemes/users';
import {parseFormData} from 'parse-nested-form-data';
import {validateEntityBySchema} from '$lib/common/utils';
import {mapper} from '$common/core/mapper';
import type {FormUserDto} from '$common/models/dtos/user.dto';
import { setDisabledToken } from '$api/core/services/auth.service';

export const ssr = false;

export const actions: Actions = {
    update: async ({request, locals}: RequestEvent) => {
        const formData = parseFormData(await request.formData());
        const {result, errors} = validateEntityBySchema(formData, userSchemaForUpdatingUser);
        if (!result) return fail(400, {error: true, errors});
        const dto = mapper<FormUserDto, unknown>(formData)
        if (dto.role === 'disabled') {
            await setDisabledToken(dto.id);
        }
        return await updateUser(dto, locals?.user?.id);
    },
    create: async ({request, locals}: RequestEvent) => {
        const formData = parseFormData(await request.formData());
        const {result, errors} = validateEntityBySchema(formData, userSchemaForCreatingUser);
        if (!result) return fail(400, {error: true, errors});
        const dto = mapper<FormUserDto, unknown>(formData);
        return await createUser(dto, locals?.user?.id);
    }
};

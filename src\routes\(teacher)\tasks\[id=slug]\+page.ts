import { SentenceApiClient } from '$lib/core/api-clients/sentence-api-client';
import { GroupApiClient } from '$lib/core/api-clients/group-api-client';
import { TaskApiClient } from '$lib/core/api-clients/task-api-client';
import { paramsToKeyValue } from '$api/core/utils';
import { IncentiveApiClient } from '$lib/core/api-clients/incentive-api-client';
import type { PageLoad } from '../../../../../.svelte-kit/types/src/routes/(teacher)/tasks/[id=slug]/$types';
import { UserApiClient } from '$lib/core/api-clients/user-api-client';
import { GeneralHolidaysApiClient } from '$lib/core/api-clients/generalHolidays-api.client';

export const ssr = false;

export const load: PageLoad = async ({ params, url, depends }) => {
	try {
		depends('load:tasks/id');
		const { id } = params;
		const { isn, duplicate } = paramsToKeyValue(url.searchParams);
		return {
			task:
				isn && duplicate
					? new TaskApiClient().getTaskById(duplicate!)
					: isn
					? null
					: new TaskApiClient().getTaskById(id!),
			sentences: new SentenceApiClient().getSentences(),
			groups: new GroupApiClient().getGroups(true),
			incentiveContent: new IncentiveApiClient().getIncentiveContent(true),
			users: new UserApiClient().getUsers(),
			generalHolidays: new GeneralHolidaysApiClient().getGeneralHolidays()
			
		};
	} catch (error) {
		return {
			errorMessage: error
		};
	}
};

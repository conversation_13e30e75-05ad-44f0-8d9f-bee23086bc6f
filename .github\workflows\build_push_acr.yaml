name: Build and Push to ACR

on:
  push:
    branches:
      - '**'
jobs:
  build:
    name: Build and Push to ACR
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set Up Branch Name
        id: vars
        run: |
          BRANCH_NAME="${GITHUB_REF#refs/heads/}"
          SANITIZED_BRANCH_NAME=$(echo "$BRANCH_NAME" | sed 's/\//_/g')
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          echo "SANITIZED_BRANCH_NAME=$SANITIZED_BRANCH_NAME" >> $GITHUB_ENV

      - name: Docker Login to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_SERVER }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v2
        with:
          push: true
          tags: ${{ secrets.ACR_SERVER }}/${{ secrets.IMAGE_NAME }}:${{ env.SANITIZED_BRANCH_NAME }}
          file: ./Dockerfile
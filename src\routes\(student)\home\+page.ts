import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
import {CampaignApiClient} from '$lib/core/api-clients/campaign-api.client';
import type {Load} from "@sveltejs/kit";
import {StudentApiClient} from "$lib/core/api-clients/student-api-client";
import {GeneralHolidaysApiClient} from "$lib/core/api-clients/generalHolidays-api.client";
import {SurveyApiClient} from "$lib/core/api-clients/survey-api.client";

export const ssr = false;

export const load: Load = async ({depends, data}) => {
    try {
        depends('load:home');

        const dateFromLocal = localStorage.getItem('survey');
        return {
            tasks: new TaskApiClient().getStudentTasksByGroupId(),
            notifications: new CampaignApiClient().getAllUnreadNotificationsByStudentId(),
            studentGroups: new StudentApiClient().getGroupsHistoryByStudentId(),
            generalHolidays: new GeneralHolidaysApiClient().getGeneralHolidays(),
            isSurveyEligible: new SurveyApiClient().isSurveyEligible(dateFromLocal ? dateFromLocal : undefined)
        }
    } catch (error) {
        return error;
    }
};

import type { Readable, Subscriber, Unsubscriber } from "svelte/store"

export type Message = {
    data: any
    id: number
    repeated: number
}
type InternalMessage = {
    data: any
    ttl: number
    id: number
    repeated: number
    timeoutId: any
}
export interface Expirable extends Readable<Array<Message>> {
    push(data: any, ttl?: number): void
}

export const expirable = (): Expirable => {
    let subscribers: Array<Subscriber<Array<Message>>> = []
    let messages: Array<InternalMessage> = []
    let lastMessageId = 0

    const executeRun = () => {
        subscribers.forEach((run) => executeOneRun(run))
    }

    const executeOneRun = (run: Function) => run(messages.map((message) => ({
        data: message.data,
        repeated: message.repeated,
        id: message.id
    })))

    return {
        subscribe(run: Subscriber<Array<Message>>): Unsubscriber {
            subscribers.push(run)
            executeOneRun(run)
            return () => {
                subscribers = subscribers.filter((runFunction) => runFunction !== run)
            }
        },
        push(data, ttl: number = 3) {
            const existing = messages.find(msg => msg.data === data)

            if (existing) {
                clearTimeout(existing.timeoutId)
                existing.timeoutId = setTimeout(() => {
                    messages = messages.filter(msg => msg.data !== data)
                    executeRun()
                }, ttl * 1000)
                existing.ttl = ttl || 3
                existing.repeated++
            } else {
                lastMessageId++
                const timeoutId = setTimeout(() => {
                    messages = messages.filter(msg => msg.data !== data)
                    executeRun()
                }, ttl * 1000)
                messages.push({
                    data,
                    ttl,
                    id: lastMessageId,
                    repeated: 0,
                    timeoutId
                })
            }

            executeRun()
        }
    }
}

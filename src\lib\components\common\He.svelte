<script>
    import {switchHebrewFont} from '$lib/common/utils';
    import {HebrewFontState} from '$lib/state/hebrew-font-state';
    import {CurrentEditableState} from "$lib/state/task-current-editable-state";

    export let useLocalState = false;
	export let bypassedFont = null;
</script>

<p
		class="{useLocalState
		? switchHebrewFont($CurrentEditableState.hebrewFont)
		: bypassedFont !== null ? switchHebrewFont(bypassedFont)
		: switchHebrewFont($HebrewFontState)} break-normal tracking-wider"
		dir="rtl"
>
	<slot/>
</p>

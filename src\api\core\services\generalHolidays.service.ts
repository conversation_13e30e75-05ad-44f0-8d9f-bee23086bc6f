import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
import {db} from "$api/core/service-clients/db";
import {mapper} from "$common/core/mapper";


export const createGeneralHoliday = async (dto: GeneralHolidayDto) => {

    const data = db.generalHolidays.create({
        data: {
            id: dto.id,
            comment: dto.comment,
            date: dto.date
        }
    })

    return mapper<GeneralHolidayDto, unknown>(data)
}


export const getGeneralHolidays = async () => {
    const count = await db.generalHolidays.count();
    const data = await db.generalHolidays.findMany({
        orderBy: {
            date: 'asc'
        }
    })

    const dtos = data.map((g) => mapper<GeneralHolidayDto, unknown>(g))

    return {data: dtos, count}
}


export const deleteGeneralHoliday = async (id: string) => {
    return await db.$transaction(async (tx) => {
        await tx.groupHolidaysExceptions.deleteMany({
            where: {
                generalHolidayId: id,
            }
        })
        return await tx.generalHolidays.delete({
            where: {
                id
            }
        })
    })
}
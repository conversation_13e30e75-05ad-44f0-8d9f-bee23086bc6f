<script lang="ts">
    import {IconChevronDown} from "@tabler/icons-svelte";
    import ContentEditor from "$components/content/ContentEditor.svelte";
    import {slide} from "svelte/transition";
    import type {AboutContentsDto} from "$common/models/dtos/AboutContents.dto";

    export let content;
    export let id;
    export let lang;
    export let save: ({id, value}:Omit<AboutContentsDto,'lang'>) => void;

    let isOpenItem = false;
</script>

<div class="flex-1 card variant-glass-primary break-all">
    <div
            class="flex justify-between py-2 min-h-[50px] px-8 cursor-pointer"
            on:click={() => (isOpenItem = !isOpenItem)}
    >
        <div class="flex items-center w-full gap-5">
            <div class=" min-w-[150px]">
                {lang.toUpperCase()}
            </div>
        </div>
        <div class="flex justify-end items-center cursor-pointer mr-3">
            <IconChevronDown/>
        </div>
    </div>
    {#if isOpenItem}
        <div
                dir="ltr"
                class=" pt-5 pb-10 pl-9 pr-9 flex flex-col justify-center items-center"
                transition:slide={{ duration: 500 }}
        >
            <div class="w-full flex flex-col">
                <ContentEditor
                        readMode={false}
                        on:contentUpdated={(e) => {
                            save({id,value:e.detail.content})
						isOpenItem = false;
					}}

                        bind:content
                />
            </div>
        </div>
    {/if}
</div>
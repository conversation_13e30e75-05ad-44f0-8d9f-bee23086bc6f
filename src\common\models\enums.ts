export enum NotificationType {
    info = 'info',
    success = 'success',
    warning = 'warning',
    error = 'error'
}

export enum AudioFilter {
    'with' = 1,
    'without' = 0 // this should be translation key
}

export enum HandledComplaintFilter {
    "handled" = 1,
    'notHandled' = 0
}

export enum CampaignFilter {
    'student' = 'student',
    'group' = 'group'
}

export enum LanguageFilter {
    'EN' = 'EN',
    'RU' = 'RU'
}

export enum HoursScheduleFilter {
    'בוקר' = 0,
    'צהריים' = 1,
    'ערב' = 2
}

export enum LevelFilter {
    'א' = 0,
    'א+' = 1,
    'ב' = 2,
    'ב+' = 3,
    'ג' = 4,
    'ג+' = 5
}

export enum TaskFilter {
    'homework' = 'home',
    'classwork' = 'class',
    'sandbox' = 'sandbox'
}

export enum ActiveFilter {
    'active' = 1,
    'inActive' = 0
}

export enum TypeFilter {
    'public' = 1,
    'ragil' = 0
}

export enum UserRole {
    teacher = 'teacher',
    admin = 'admin',
    disabled = 'disabled'
}

export enum Permission {
    editFavorites = 'editFavorites',
    deleteNonFavSentences = 'deleteNonFavSentences'
}

// etc filters

export enum TaskMode {
    translation = 'translation',
    listen = 'listen',
    bytime = 'bytime',
    phantom = 'phantom',
    audiodic = 'audiodic',
    voice = 'voice'
}


export enum ComplaintsStatisticFilterField {
    'complaints' = 'complaints',
    'sentences' = 'sentences',
    'handled' = 'handled',
    'nothandled' = 'notHandled'
}

export enum SurveyStatus {
    none = 'none',
    rare = 'rare',
    daily = 'daily'
}

export enum TaskSpeedMode {
    slow =  '360',
    medium = '240',
    fast = '192',
}

<script lang="ts">
    import Sentence from '$components/sentences/Sentence.svelte';
    import {IconCircleMinus, IconGripVertical, IconSearch, IconPlus, IconArrowsUpDown} from '@tabler/icons-svelte';
    import BaseInput from '$components/common/BaseInput.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {dndzone} from 'svelte-dnd-action';
    import {flip} from 'svelte/animate';
    import {t} from '$lib/i18n/config';
    import {SentenceFilterState} from '$lib/state/sentence-filter-state';
    import {get} from 'svelte/store';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import type {SentenceDto} from '$common/models/dtos/sentence.dto';
    import TaskSentencesCount from '$components/tasks/TaskSentencesCount.svelte';
    import _ from 'lodash';
    import {generateGuid} from '$common/core/utils';
    import BaseSwitchHebrew from '$components/common/BaseSwitchHebrew.svelte';
    import {page} from "$app/stores";
    import BaseSwitch from "$components/common/BaseSwitch.svelte";
    import {LockedDragAndDropState} from "$lib/state/lock-dnd-state";

    export let checkToDisableSaveButtonInTask: () => void;
    export let groupLevel = 0;

    let optionSelected = 0;

    let hoveredSentence = null;
    $: selectedSentences = $CurrentEditableState.sentences.sort((a, b) => a.index - b.index);

    $: countSentencesWithAudio = $CurrentEditableState.sentences.filter(sentence => sentence.audioUrl).length;
    $: sentencesLength = $CurrentEditableState.sentences.length;

    let search = $SentenceFilterState.search;
    const modalStore = getModalStore();

    SentenceFilterState.subscribe(x => {
        if ($SentenceFilterState.search === '' && search !== '') search = '';
    })

    const flipDurationMs = 100;

    const handleConsider = (event: CustomEvent<DndEvent<SentenceDto>>) => {
        selectedSentences = event.detail.items;
        if (event.type === 'finalize') {
            selectedSentences = selectedSentences.map((x, i) => ({...x, index: i + 1}));
            $CurrentEditableState.sentences = selectedSentences;
        }
    };

    const onChangeSelectOrder = (fromIndex: number, toIndex: number) => {
        const element = $CurrentEditableState.sentences.splice(fromIndex, 1)[0];
        $CurrentEditableState.sentences.splice(toIndex, 0, element)
        $CurrentEditableState.sentences = $CurrentEditableState.sentences.map((s, i) => ({...s, index: i + 1}))
    }

    const openTaskSentenceSearchPopUp = () => {
        modalStore.trigger({type: 'component', component: 'taskSentenceSearchPopUp'});
        SentenceFilterState.set({...get(SentenceFilterState), search});
    };

    const openTaskGenereteModal = ()=>{
        modalStore.trigger({type: 'component', component: 'taskGenerateModal'});

    }


    const remove = (sentenceId: string) => {
        $CurrentEditableState.sentences = $CurrentEditableState.sentences.filter(
            (x) => x.id !== sentenceId
        );
        checkToDisableSaveButtonInTask();
    };

    const createNewSentence = () => {
        const sentence = {
            id: generateGuid(),
            value: '',
            translations: [
                {
                    id: generateGuid(),
                    lang: 'EN',
                    value: ''
                },
                {
                    id: generateGuid(),
                    lang: 'RU',
                    value: ''
                }
            ],
            sex: 'm',
            level: groupLevel,
            isNew: true,
            audioRecordedButNotYetSaved: false,
            displayAsText: true,
            index: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
            createdByUser: {
                firstname: $page?.data?.user?.firstname,
                lastname: $page?.data?.user?.lastname
            },
            updatedByUser: {
                firstname: $page?.data?.user?.firstname,
                lastname: $page?.data?.user?.lastname
            }
        };
        selectedSentences = [sentence, ...selectedSentences];

        $CurrentEditableState.sentences.forEach((x, i) => (x.index = i + 1));
        $CurrentEditableState.sentences = selectedSentences;
        checkToDisableSaveButtonInTask();
    };


    const reverseSentences = () => {
        $CurrentEditableState.sentences = $CurrentEditableState.sentences.reverse().map((element, index) => ({
            ...element,
            index: index + 1
        }))
    }


    let handleMouseEnter = (id: string) => {
        hoveredSentence = id;
    }


    function handleMouseLeave() {
        hoveredSentence = null;
    }

</script>

<div class="flex flex-col justify-center items-center gap-6 mt-10 task-sentences-search">
    <div class="flex gap-7 flex-col card p-5 w-full text-token variant-glass-primary">
        <div class="w-full flex justify-between gap-16 pr-8 pl-14">
            <div class="w-full flex justify-start">
                <div class="self-end">
                    <BaseButton on:click={createNewSentence}>
                        <IconPlus/>{$t('tasks.taskSentenceSearch.new')}
                    </BaseButton>
                </div>
                <div class="flex-1 flex justify-evenly items-center">
                    <div class="self-end items-end">
                        <BaseButton on:click={reverseSentences}>
                            <IconArrowsUpDown/>
                        </BaseButton>
                    </div>
                    <div class="self-center mt-6 flex">
                        <TaskSentencesCount {countSentencesWithAudio} {sentencesLength}/>
                    </div>

                    <div class="flex w-1/2 gap-x-3">
                        <BaseInput
                                className="flex-1"
                                title={$t('tasks.taskSentenceSearch.search')}
                                name="Search"
                                bind:value={search}
                                on:keydown={(event) => {
                                    if (event.key === 'Enter') {
                                        event.preventDefault();
                                        openTaskSentenceSearchPopUp()
                                    }
                                }}
                        />
                        <BaseButton className="self-end" on:click={openTaskSentenceSearchPopUp}>
                            <IconSearch/>
                        </BaseButton>
                    </div>

                    <div dir="ltr" class="self-end h-10 w-fit">
                        <BaseSwitchHebrew bigSize={true} bind:state={$CurrentEditableState.hebrewFont}/>
                    </div>
                    <div class="self-end !mb-0">
                        <BaseSwitch name="hint" title={$t('tasks.additionalTask.hint')}
                                    bind:checked={$CurrentEditableState.hintsEnabled}/>
                    </div>
                    <div class="self-end ">
                        <BaseButton on:click={openTaskGenereteModal}>
                            Generate
                        </BaseButton>
                    </div>
                </div>
            </div>
        </div>
        <section
                class="flex flex-col gap-2 overflow-y-auto overflow-x-hidden"
                use:dndzone={{
				items: selectedSentences,
				flipDurationMs,
				dragDisabled: true,
				dropTargetStyle: { outline: 'rgb(201, 204, 209) solid 2px' }
			}}
                on:consider={handleConsider}
                on:finalize={handleConsider}
        >
            {#each selectedSentences as sentence, outerIndex (sentence.id)}
                <div class="flex items-center" animate:flip={{ duration: flipDurationMs }}>
                    <div class="flex flex-col items-center self-center mb-4 justify-center gap-2 {hoveredSentence?'ml-2':''}">
                        <div on:mouseenter={()=>{
                            handleMouseEnter(sentence.id)
                            optionSelected = outerIndex;
                        }}
                             on:mouseleave={handleMouseLeave}
                             class="relative text-2xl">
                            {#if (sentence.id !== hoveredSentence)}
                                {outerIndex + 1}
                            {:else}
                                {#if sentence.id === hoveredSentence}
                                    <select bind:value={optionSelected} on:change={()=>{
                                        onChangeSelectOrder(outerIndex,optionSelected)
                                        console.log(`now - ${outerIndex}, select - ${optionSelected}`)
                                    }} id={sentence.id}
                                            class="select rounded ">
                                        {#each selectedSentences as s, selectIndex (s.id)}
                                            <option class="border-2 border-red-900 w-[100px]"
                                                    value={selectIndex}>
                                                {selectIndex + 1}
                                            </option>
                                        {/each}
                                    </select>
                                {/if}
                            {/if}
                        </div>
                        <IconGripVertical class="cursor-grab w-7 h-7"/>
                    </div>
                    <div class="flex-1">
                        <Sentence
                                id={sentence.id}
                                bind:sentence
                                selectedLang={$CurrentEditableState.lang}
                                isTaskMode={true}
                                displayAsText={sentence.displayAsText}
                                {checkToDisableSaveButtonInTask}
                                on:globalDelete={() => remove(sentence.id)}
                        />
                    </div>
                    <div on:click={() => remove(sentence.id)}>
                        <IconCircleMinus class="w-10 h-10 mr-2 text-red-700 cursor-pointer"/>
                    </div>
                </div>
            {/each}
        </section>
    </div>
</div>

<style>
    .task-sentences-search {
        position: relative;
        z-index: 1;
    }
</style>

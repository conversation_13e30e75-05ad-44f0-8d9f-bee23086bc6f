<script lang="ts">
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher} from 'svelte';
    import {t} from '$lib/i18n/config';
    import {IconMessage, IconCheck} from '@tabler/icons-svelte';
    import {getTimeFromDateString} from '$lib/common/utils';
    import type {NotificationByStudentIdDto} from '$common/models/dtos/notification.dto';
    import {format} from "date-fns";

    const markAsRead = createEventDispatcher();

    export let notification: NotificationByStudentIdDto;
    const handleClickButton = () => {
        markAsRead('markAsRead', {
            id: notification.id
        });
    };
</script>

<div class="py-2 px-4 rounded-xl flex flex-col gap-4 card variant-glass-primary" dir="rtl">
    <div class="flex items-center gap-1 self-end font-medium text-sm opacity-50" dir="ltr">
        <IconMessage size="30" class="mr-2"/>
        {#if notification?.type === 'group'}
            {`${$t('notifications.toGroup')} ${notification?.title}`}
        {:else}
            {`${$t('notifications.toStudent')} ${notification?.title}`}
        {/if}
    </div>

    <div class="self-end font-bold flex justify-between w-full items-center break-words">
        <div class="flex items-center">
            {#if !notification?.isRead}
                <BaseButton on:click={handleClickButton} name="isRead" size="sm">
                    <!--{$t('notifications.read')}-->
                    <IconCheck stroke={3}/>
                </BaseButton>
            {/if}
        </div>
        <div class="font-bold text-base pr-5" dir="ltr">
            {notification?.message}
        </div>
    </div>
    <div class="self-end text-sm flex justify-start w-full italic opacity-50" dir="auto">
        {#if notification?.createdAt}
            <div class="flex">
                <div>
                    {`${getTimeFromDateString(notification?.createdAt.toString())} ${format(new Date(notification?.createdAt), 'dd.MM.yyyy')}`}
                </div>
            </div>
        {/if}
        <div class="mx-3">
            {notification?.author}
        </div>
    </div>
</div>

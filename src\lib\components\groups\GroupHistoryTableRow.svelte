<script lang="ts">
    import {goto} from "$app/navigation";
    import {page} from "$app/stores";
    import {format} from "date-fns";
    import {calculateStudentStudyHoursAndDays, durationToMinutesToHuman} from "$lib/common/utils.js";
    import {t} from "$lib/i18n/config.js";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import ResultBadge from "$components/t/ResultBadge.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconEdit, IconSend} from "@tabler/icons-svelte";
    import {createEventDispatcher, onMount} from "svelte";

    export let groupId;
    export let history;


    export let generalHolidays;
    const dispatchModal = createEventDispatcher();

    const id = $page.params.id;
    let statistics = {totalDays: 0, totalHours: 0};

    onMount(() => {
        let scheduleInGroup = history?.student?.studentLearningHistory.find((el) => el.groupId === id);
        statistics = calculateStudentStudyHoursAndDays([{...scheduleInGroup}], generalHolidays);
    });

    const handleClickToRow = async () => {
        const link = `${$page.url.origin}${$page.url.pathname}/${history.student.id}`;
        await goto(link);
    };


</script>


<tr on:click={handleClickToRow} class="cursor-pointer {history.dateEndActual ? '!bg-surface-300-600-token' : ''}">
    <td>
        {`${history.student.firstname} ${history.student.lastname}`}
    </td>
    <td class="">
        <div on:click={async () => {
                            await goto(`${$page.url.origin}/groups/${groupId}`)
                    }}
             class="cursor-pointer w-full flex font-medium !p-1 !m-0 text-blue-600 dark:text-blue-400 hover:underline badge text-sm btn btn-ghost">
            {history.student.currentGroup || ''}
        </div>
    </td>
    <td>{history.student.registrationDate ? format(new Date(history.student.registrationDate), 'P') : ''}</td>
    <td>
        {format(new Date(history.dateStartActual), 'dd.MM.yyy')}
    </td>
    <td>
        {#if history.dateEndActual}
            {format(new Date(history.dateEndActual), 'dd.MM.yyyy')}
        {:else}
            —
        {/if}
    </td>
    <td>
        {format(new Date(history.dateStartTasks), 'dd.MM.yyyy')}
    </td>
    <td>{history.student.comment ? history.student.comment : ''}</td>
    <td>
        <ResultBadge result={history.student.lastTaskScore ? history.student.lastTaskScore : 0}/>
    </td>
    <td>
        <ResultBadge result={history.student.averageTaskScore ? history.student.averageTaskScore : 0}/>
    </td>
    <td>{durationToMinutesToHuman(history.student.lastTaskDelay)}</td>
    <td>{durationToMinutesToHuman(history.student.averageTaskDelay)}</td>
    <td class="">
        {#if history.student.whatsapp}
            <a href="https://web.whatsapp.com/send/?phone={history.student.whatsapp}&type=phone_number&app_absent=0"
               target="_blank">
                <BaseButton size="sm" className="bg-green-800 w-full">
                    <span dir="auto">{history.student.whatsapp}</span>
                    <IconSend size={20} stroke="1.5"/>
                </BaseButton>
            </a>
        {:else}
            (｡◕‿◕｡)
        {/if}
    </td>
    <OnlyForRole>
        <td class="w-fit">
            <p class="mb-1 text-center">
                <span>{$t('students.students.table.hours')}: <b>{statistics.totalHours}</b></span>
            </p>
            <p class="text-center">
                <span>{$t('students.students.table.days')}: <b>{statistics.totalDays}</b></span>
            </p>
        </td>
    </OnlyForRole>
    <td class="flex items-center gap-2">
        <OnlyForRole>
            <BaseButton
                    size="sm"
                    on:click={(e) => {
								e.stopPropagation();
								dispatchModal('triggerEditModal', { action: 'update', row:history.student });
							}}
            >
                {$t('students.students.table.editButton')}
                <IconEdit size={20} stroke="1.5"/>
            </BaseButton>
        </OnlyForRole>
        <BaseButton
                size="sm"
                on:click={(e) => {
								e.stopPropagation();
								dispatchModal('triggerSendMessageModal', {
									recipientId: history.student.id,
									recipientName: `${history.student.firstname} ${history.student.lastname}`,
									currentGroup: history.student.currentGroup
								});
							}}
        >
            <IconSend size={20} stroke="1.5"/>
        </BaseButton>
    </td>
</tr>

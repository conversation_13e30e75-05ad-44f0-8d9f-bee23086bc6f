<script lang="ts">
    import {
        initialStudentPaging,
        pageSize as pageSizeStudents,
        StudentPagingState,
        StudentSortingState
    } from '$lib/state/student-paging-state';
    import {getModalStore, type ModalSettings, Tab, TabGroup} from '@skeletonlabs/skeleton';
    import {t} from '$lib/i18n/config';
    import {StudentsTabState} from '$lib/state/students-tab-state';
    import RequestsTable from '$components/student-requests/RequestsTable.svelte';
    import StudentsTable from '$components/students/StudentsTable.svelte';
    import CountSpan from '$components/common/CountSpan.svelte';
    import {StudentEditModalState} from '$lib/state/student-edit-state';
    import {StudentAcceptModalState} from '$lib/state/student-accept-state';
    import {SendNotificationModalState} from '$lib/state/send-notification-state';
    import {generateGuid} from '$common/core/utils';
    import {get} from 'svelte/store';
    import StudentFilter from '$components/students/StudentFilter.svelte';
    import {beforeNavigate, invalidate} from '$app/navigation';
    import {StudentFilterState} from '$lib/state/student-filter-state';
    import {onDestroy, onMount} from 'svelte';
    import {calculateStudentStudyHoursAndDays, loadingWrap} from '$lib/common/utils';
    import {
        initialStudentsRequestsPaging,
        pageSize as pageSizeRequests,
        StudentsRequestsPagingState
    } from "$lib/state/students-requests-paging-state";
    import StudentRequestFilter from "$components/students/StudentRequestFilter.svelte";
    import {
        initialStudentsRequestsFilterState,
        StudentsRequestsFilterState
    } from "$lib/state/students-requests-filter.state";
    import {StudentApiClient} from "$lib/core/api-clients/student-api-client";
    import NotificationState from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";
    import {initialStudentFilter} from "$common/models/filters/student-filter.dto";
    import _ from "lodash";

    export let data;
    const modalStore = getModalStore();

    $: requests = data?.requests.data;
    $: requestsCount = data?.requests?.count;
    $: students = data?.students?.data;
    $: studentsCount = data?.students?.count;
    $: groups = data?.groups?.data;


    $:if ($StudentsTabState.toString()) {
        $StudentFilterState = _.cloneDeep(initialStudentFilter);
        $StudentsRequestsFilterState = _.cloneDeep(initialStudentsRequestsFilterState)
    }

    const loadMoreStudents = async () => {
        if (studentsCount > students?.length) {
            await loadingWrap(async () => {
                StudentPagingState.set({take: 25, skip: students.length});
                const {data} = await new StudentApiClient().getStudents();
                students = [...students, ...data];
            });
        }
    };

    const loadMoreStudentsRequest = async () => {
        if (requestsCount > requests.length) {
            await loadingWrap(async () => {
                StudentsRequestsPagingState.set({take: 25, skip: requests.length});
                const {data} = await new StudentApiClient().getStudentRequests()
                requests = [...requests, ...data];
            })
        }
    }

    const openEditStudentModal = (event) => {
        const studentStudyHoursAndDays = calculateStudentStudyHoursAndDays(event.detail.row.studentLearningHistory, data.generalHolidays.data);
        StudentEditModalState.set({
            ...event.detail.row,
            teudatOleUrl: event.detail.row.document1url,
            photoUrl: event.detail.row.document2url,
            totalHours: studentStudyHoursAndDays.totalHours,
            totalDays: studentStudyHoursAndDays.totalDays,
            generalHolidays: data.generalHolidays.data,
            groups
        });
        modalStore.trigger({type: 'component', component: 'updateStudentModal'});
    };

    const openAcceptStudentModal = (event) => {
        StudentAcceptModalState.set({
            ...event.detail,
            groups: groups.filter(x => !x.isPublic),
            document1url: event.detail.teudatOleUrl,
            document2url: event.detail.photoUrl
        });
        modalStore.trigger({type: 'component', component: 'acceptStudentModal'});
    };

    const openSendNotificationStudentModal = (event) => {
        const groupLang = event.detail.currentGroup.includes('RU') ? 'ru' : 'en';
        SendNotificationModalState.set({
            ...get(SendNotificationModalState),
            id: generateGuid(),
            type: 'student',
            title: `${$t(`campaigns.modal.titleStudent.${groupLang}`)} ${event.detail.recipientName}`,
            recipientId: event.detail.recipientId
        });
        modalStore.trigger({type: 'component', component: 'sendNotificationStudentModal'});
    };

    const deleteStudentRequest = async (event) => {
        const modalResult = await new Promise<boolean>((resolve) => {
            const modal: ModalSettings = {
                type: 'confirm',
                title: `<h1 dir="auto">${$t('students.requests.modalDelete.title')}</h1>`,
                body: `<p dir="auto">${$t('students.requests.modalDelete.body')}</p>`,
                buttonTextConfirm: $t('students.requests.modalDelete.buttonTextConfirm'),
                buttonTextCancel: $t('students.requests.modalDelete.buttonTextCancel'),
                response: (r: boolean) => resolve(r)
            };
            modalStore.trigger(modal);
        });
        if (modalResult) {
            const result = await new StudentApiClient().deleteStudentRequest(event.detail);
            if (result) {
                NotificationState.push({
                    type: NotificationType.success,
                    message: $t('students.requests.modalDelete.notification.success')
                });
                await invalidate('load:students');
            } else {
                NotificationState.push({
                    type: NotificationType.error,
                    message: $t('students.requests.modalDelete.notification.error')
                });
            }
        } else {
            NotificationState.push({
                type: NotificationType.success,
                message: $t('students.requests.modalDelete.notification.cancel')
            });
        }
    }

    const unsubscribeFilterStudent = StudentFilterState.subscribe(async () => {
        $StudentPagingState = _.cloneDeep(initialStudentPaging)
        await loadingWrap(async () => {
            await invalidate('load:students');
        });
    });

    const unsubscribeFilterStudentsRequests = StudentsRequestsFilterState.subscribe(async () => {
        $StudentsRequestsPagingState = _.cloneDeep(initialStudentsRequestsPaging)
        await loadingWrap(async () => {
            await invalidate('load:students')
        })
    })

    const unsubscribeSort = StudentSortingState.subscribe(async () => {
        $StudentPagingState = _.cloneDeep(initialStudentPaging)
        await loadingWrap(async () => {
            await invalidate('load:students');
        });
    });

    beforeNavigate(() => {
        unsubscribeFilterStudent();
        unsubscribeSort();
        unsubscribeFilterStudentsRequests();
    })

    onDestroy(() => {
        $StudentFilterState = _.cloneDeep(initialStudentFilter)
        $StudentsRequestsFilterState = _.cloneDeep(initialStudentsRequestsFilterState)
    })

</script>

<div class="flex flex-col h-[calc(100vh-85px)] overflow-hidden  mx-6">
    <div class="h-full mt-3 ">
        <TabGroup class="flex flex-col h-full">
            <Tab on:click={null} bind:group={$StudentsTabState} name="requests-tab" value={0}>
                <b
                >{$t('students.tabs.students')}
                    <span class={$StudentFilterState.onlyIsActive ? 'text-green-800' : ''}>
							<CountSpan bind:count={studentsCount}/>
						</span>
                </b>
            </Tab>
            <Tab bind:group={$StudentsTabState} name="students-tab" value={1}>
                <b
                >{$t('students.tabs.requests')}
                    <CountSpan bind:count={requestsCount}/>
                </b>
            </Tab>
            <svelte:fragment slot="panel">
                {#if $StudentsTabState === 0}
                    <div class="h-full flex flex-col">
                        <StudentFilter/>
                        {#key $StudentFilterState.search}
                            <StudentsTable
                                    bind:students
                                    {openEditStudentModal}
                                    {openSendNotificationStudentModal}
                                    on:triggerEditModal={openEditStudentModal}
                                    on:triggerSendMessageModal={openSendNotificationStudentModal}
                                    {groups}
                                    loadMoreFunc={loadMoreStudents}
                                    generalHolidays={data.generalHolidays.data}
                            />
                        {/key}
                    </div>


                {:else if $StudentsTabState === 1}
                    <div class="h-full flex flex-col">
                        <StudentRequestFilter/>
                        <RequestsTable
                                {requests}
                                on:triggerAcceptModal={openAcceptStudentModal}
                                on:deleteStudentRequest={deleteStudentRequest}
                                loadMoreFunc={loadMoreStudentsRequest}
                        />
                    </div>
                {:else}
                    Loading...
                {/if}
            </svelte:fragment>
        </TabGroup>
    </div>
</div>

<style lang="scss">
  :global(.tab-panel) {
    flex: 1 1 0;
    overflow: hidden;
  }
</style>

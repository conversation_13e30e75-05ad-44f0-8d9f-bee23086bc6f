import {wrapFunc} from '$api/core/misc/response-wrapper';
import {deleteStudentRequest, getStudentRequests} from '$api/core/services/students.service';
import {paramsToKeyValue} from "$api/core/utils";
import type {RequestEvent} from "@sveltejs/kit";

export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {take, skip, search} = paramsToKeyValue(
            url.searchParams
        );
        return await getStudentRequests({take: +take, skip: +skip, search});
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);
        return await deleteStudentRequest(id);
    })

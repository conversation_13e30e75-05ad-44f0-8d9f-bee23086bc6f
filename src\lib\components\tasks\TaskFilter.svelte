<script lang="ts">
    import BaseInput from '../common/BaseInput.svelte';
    import BaseSelect from '../common/BaseSelect.svelte';
    import {t} from '$lib/i18n/config';
    import {TaskFilter} from '$common/models/enums';
    import {mapEnumToOptions} from '$lib/common/utils';
    import {get} from 'svelte/store';
    import {TaskFilterState} from '$lib/state/task-filter-state';
    import type {TaskFilterDto} from '$common/models/filters/task-filter.dto';
    import _ from 'lodash';
    import type {UserDto} from "$common/models/dtos/user.dto";

    export let groups;

    export let users: UserDto[];

    $: options = [
        {displayValue: '', value: ''},
        ...groups?.map((x) => {
            return {displayValue: x.name, value: x.id};
        })
    ];

    let userOptions = [
        {displayValue: '', value: ''},
        ...users.map((u) => {
            return {
                value: u.id,
                displayValue: `${u.firstname} ${u.lastname}`
            }
        })
    ]

    let typeOptions = [{value: '', displayValue: ''}, ...mapEnumToOptions(TaskFilter)];

    let inputValue = '';

    const onInput = _.debounce(
        () =>
            TaskFilterState.set(<TaskFilterDto>{
                ...get(TaskFilterState),
                search: inputValue
            }),
        1000
    );
</script>

<div class="flex flex-col">
    <div class="flex flex-row gap-10 items-baseline">
        <div class="w-72">
            <BaseInput
                    name="search"
                    bind:value={inputValue}
                    on:input={onInput}
                    title={$t('tasks.filters.input')}
            />
        </div>

        <div class="flex w-full gap-10 items-baseline">
            <BaseSelect
                    name="audio"
                    title={$t('tasks.filters.group')}
                    {options}
                    bind:value={$TaskFilterState.groupId}
            />

            <BaseSelect
                    name="type"
                    title={$t('tasks.filters.type')}
                    options={typeOptions}
                    bind:value={$TaskFilterState.type}
            />
            <BaseSelect
                    name="createdBy"
                    title={$t('tasks.filters.createdBy')}
                    options={userOptions}
                    bind:value={$TaskFilterState.createdBy}
            />
        </div>
    </div>
</div>

import {AboutContentsApiClient} from "$lib/core/api-clients/aboutContents-api.client";
import type {PageLoad} from "../../../../.svelte-kit/types/src/routes/(student)/about/$types";

export const ssr = false;

export const load: PageLoad = async ({depends}) => {
    try {
        depends('load:about');
        return {
            aboutContents: new AboutContentsApiClient().getAboutContents()
        }
    } catch (error) {
        return error;
    }
};
import type {Actions, RequestEvent} from '@sveltejs/kit';
import {parseFormData} from 'parse-nested-form-data';
import {getDateString, validateEntityBySchema} from '$lib/common/utils';
import {groupSchemaForCreatingGroup} from '$lib/validation-schemes/groups';
import {fail} from '@sveltejs/kit';
import {createGroup, stopGroup, updateGroup} from '$api/core/services/group.service';
import {mapper} from '$common/core/mapper';
import {convertStringTimeToDate} from '$lib/common/utils';
import type {GroupDto, StopGroupDto} from '$common/models/dtos/group.dto';
import _ from "lodash";
import {schemaForUpdateAndAcceptStudent} from "$lib/validation-schemes/accept-request";
import {
    deleteStudentGroup,
    setDateEndActualToCurrentGroup, switchStudentGroup,
    updateStudent,
    updateStudentGroup
} from "$api/core/services/students.service";
import type {StudentDto} from "$common/models/dtos/student.dto";
import type {StopCurrentGroupDto, SwitchGroupDto, UpdateStudentGroupDto} from "$common/models/dtos/student-groups.dto";
import {scheduleChangesUpdateValidation} from "$lib/validation-schemes/scheduleChanges";
import type {CreateUpdateGroupScheduleDto, GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
import {createGroupScheduleChanges, updateGroupScheduleChanges} from "$api/core/services/groupSchedule.service";

export const ssr = false;

export const actions: Actions = {
    stopGroup: async ({request}: { request: Request }) => {
        const data = parseFormData(await request.formData());
        const dto = mapper<StopGroupDto, unknown>(data);
        return await stopGroup(dto);
    },
    update: async ({request}: { request: Request }) => {
        const group = parseFormData(await request.formData());
        const {result, errors} = validateEntityBySchema({
            ...group,
            hoursSpendBySession: group.hoursSpendBySession ? +group.hoursSpendBySession : null,
            totalHoursAmount: group.totalHoursAmount ? +group.totalHoursAmount : null
        }, groupSchemaForCreatingGroup);

        if (!result) return fail(400, {error: true, errors});

        const {
            level,
            isPublic,
            isActive,
            hoursSchedule,
            timeStart,
            timeEnd,
            dateStart,
            dateEnd,
            hoursSpendBySession,
            totalHoursAmount
        } =
            group;


        const dto = mapper<GroupDto, any>({
            ...group,
            level: +(level || 0),
            isPublic: isPublic === 'true',
            isActive: isActive === 'true',
            hoursSchedule: +(hoursSchedule || 0),
            timeStart: new Date(convertStringTimeToDate(timeStart?.toString() || '')),
            timeEnd: new Date(convertStringTimeToDate(timeEnd?.toString() || '')),
            dateStart: new Date(dateStart?.toString() || ''),
            dateEnd: new Date(dateEnd?.toString() || ''),
            hoursSpendBySession: +hoursSpendBySession!,
            totalHoursAmount: +totalHoursAmount!
        });
        return await updateGroup(dto);
    },

    updateStudent: async ({request}: { request: Request }) => {
        const studentUpdate = _.omit(parseFormData(await request.formData()), [
            'isExistingStudent',
            'dob_input'
        ]);
        const {result, errors} = validateEntityBySchema(
            studentUpdate,
            schemaForUpdateAndAcceptStudent
        );

        if (!result) return fail(400, {error: true, errors});
        return await updateStudent(mapper<StudentDto, unknown>(studentUpdate));
    },
    deleteStudentGroup: async ({request}: { request: Request }) => {
        const {id} = parseFormData(await request.formData());
        if (!id) return fail(400, {error: true, message: 'Id not found'});
        return await deleteStudentGroup(id.toString());
    },
    updateStudentGroup: async ({request}: { request: Request }) => {
        const studentGroupToUpdate = parseFormData(await request.formData());
        const dto = mapper<UpdateStudentGroupDto, unknown>(studentGroupToUpdate);
        return await updateStudentGroup(dto);
    },
    stopCurrentGroup: async ({request}: { request: Request }) => {
        const stopDate = _.omit(parseFormData(await request.formData()), ['dateEndActual_input']);

        return await setDateEndActualToCurrentGroup(mapper<StopCurrentGroupDto, unknown>(stopDate));
    },
    switchStudentGroup: async ({request}: { request: Request }) => {
        const data = parseFormData(await request.formData());
        const {
            idCurrentGroup,
            dateEndCurrentGroup,
            idSelectedGroup,
            dateStartSelectedGroup,
            taskStartSelectedGroup,
            studentId
        } = data;
        const dtoStopGroup = mapper<StopCurrentGroupDto, unknown>({
            id: idCurrentGroup,
            dateEndActual: dateEndCurrentGroup,
            studentId: studentId
        });
        const dtoSwitchGroup = mapper<SwitchGroupDto, unknown>({
            idSelectedGroup,
            studentId,
            dateStartSelectedGroup: dateStartSelectedGroup,
            taskStartSelectedGroup: taskStartSelectedGroup
        });
        return {
            currentGroup: await setDateEndActualToCurrentGroup(dtoStopGroup),
            selectedGroup: await switchStudentGroup(dtoSwitchGroup)
        };
    },
    updateSchedule: async ({request, locals}: RequestEvent) => {
        const formData = parseFormData(await request.formData());
        const updaterId = locals?.user?.id;
        const {result, errors} = validateEntityBySchema({
            ...formData,
            hoursPerSession: formData.hoursPerSession ? +formData.hoursPerSession : null,
        }, scheduleChangesUpdateValidation);

        if (!result) return fail(400, {error: true, errors});

        const dto = mapper<CreateUpdateGroupScheduleDto, unknown>({
            ...formData,
            dateStart: new Date(formData?.dateStart),
            hoursPerSession: formData.hoursPerSession ? +formData.hoursPerSession : undefined,
            updatedElementIndex: formData?.updatedElementIndex === '0' ? 0 : -1
        });

        return await updateGroupScheduleChanges(dto, updaterId);
    },
    createSchedule: async ({request, locals}: RequestEvent) => {
        const formData = parseFormData(await request.formData());
        const creatorId = locals?.user?.id;
        const {result, errors} = validateEntityBySchema({
            ...formData,
            hoursPerSession: formData.hoursPerSession ? +formData.hoursPerSession : null,
        }, scheduleChangesUpdateValidation);

        if (!result) return fail(400, {error: true, errors});

        const dto = mapper<GroupScheduleDto, unknown>({
            ...formData,
            dateStart: new Date(formData?.dateStart),
            hoursPerSession: formData.hoursPerSession ? +formData.hoursPerSession : undefined,
            createdAt: new Date(),
            updatedElementIndex: undefined
        });
        return await createGroupScheduleChanges(dto, creatorId);
    },
};

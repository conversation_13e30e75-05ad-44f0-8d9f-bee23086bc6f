import {z} from "zod";


export const scheduleChangesUpdateValidation = z
    .object({
        daysSchedule: z.string().refine((value) => !/^0*$/.test(value), {
            message: 'groups.modal.formFieldsErrors.daysSchedule'
        }),
        hoursPerSession: z.number({invalid_type_error: 'groups.modal.formFieldsErrors.hoursSpendBySessionInvalidType'})
            .min(1, {message: 'groups.modal.formFieldsErrors.hoursSpendBySessionMin'})
            .max(10, {message: 'groups.modal.formFieldsErrors.hoursSpendBySessionMax'}),

    })
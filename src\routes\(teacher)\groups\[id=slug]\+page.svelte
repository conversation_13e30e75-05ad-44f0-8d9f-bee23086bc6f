<script lang="ts">
    import {t} from '$lib/i18n/config.js';
    import {GroupTabSet} from '$lib/state/group-tabs-state';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {GroupEditModalState, initialGroupForm} from '$lib/state/group-edit-state';
    import {onDestroy, onMount} from 'svelte';
    import {
        DbFormatStringToDaysScheduleObject,
        loadingWrap,
        calculateGroupEndDay,
        getCurrentGroupStudyHours,
        getCurrentGroupStudyDays,
        calculateGroupRemainingDays,
        calculateStudentStudyHoursAndDays, getTimeFromDateString
    } from '$lib/common/utils';
    import CountSpan from '$lib/components/common/CountSpan.svelte';
    import StudentsTable from '$lib/components/students/StudentsTable.svelte';
    import {IconEdit, IconPlus} from '@tabler/icons-svelte';
    import GroupInfo from '$components/groups/GroupInfo.svelte';
    import {StudentEditModalState} from '$lib/state/student-edit-state';
    import {SendNotificationModalState} from '$lib/state/send-notification-state';
    import {get} from 'svelte/store';
    import {generateGuid} from '$common/core/utils';
    import GroupSpecificTasksTable from '$components/groups/GroupSpecificTasksTable.svelte';
    import {Tab, TabGroup, getModalStore} from '@skeletonlabs/skeleton';
    import GroupSettings from '$components/groups/GroupSettings.svelte';
    import {goto, invalidate} from '$app/navigation';
    import {StudentFilterState} from '$lib/state/student-filter-state';
    import {TaskFilterState} from '$lib/state/task-filter-state';
    import {initialTaskPaging, pageSize, TaskPagingState} from '$lib/state/task-paging-state';
    import {initialStudentFilter} from "$common/models/filters/student-filter.dto";
    import {initialTaskFilter} from "$common/models/filters/task-filter.dto";
    import GroupHistory from "$components/groups/GroupHistory.svelte";
    import _ from "lodash";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {page} from "$app/stores";
    import {PreviousTaskPageState} from "$lib/state/previous-task-page.state";
    import Surveys from "$components/groups/Surveys.svelte";

    export let data;


    $: group = data?.group;
    $: groups = data?.groups.data;
    $: students = data?.students.data;
    $: tasks = data?.tasks.data;
    $: tasksCount = data?.tasks.count;
    $: studentsCount = data?.students.count;
    $: groupHistory = data?.groupHistory.data;
    $: groupHistoryCount = data?.groupHistory.count;


    $:endDate = '';

    $:groupStudyHours = 0;

    $:groupStudyDays = 0;

    $:groupRemainingDays = 0;

    $:groupRemainingHours = 0;


    $:percentage = 0;


    $:if (data.groupScheduleChanges) {
        GroupEditModalState.set({
            ...group,
            action: 'update',
            timeStart: getTimeFromDateString(group?.timeStart),
            timeEnd: getTimeFromDateString(group?.timeEnd),
            hoursSpendBySession: data.groupScheduleChanges.length > 0 ? data.groupScheduleChanges[data.groupScheduleChanges.length - 1]?.hoursPerSession : 0,
            daysSchedule: data.groupScheduleChanges.length > 0 ? DbFormatStringToDaysScheduleObject(data.groupScheduleChanges[data.groupScheduleChanges.length - 1]?.daysSchedule) : '00000',
            groupScheduleChanges: data.groupScheduleChanges,
            generalHolidays: data.generalHolidays.data,
            groupHolidays: data.groupHolidays,
            groupHolidaysExc: data.groupHolidaysExc
        });
        let endGroupLearning = calculateGroupEndDay(data.groupScheduleChanges, data.group.totalHoursAmount, {
            globalH: data.generalHolidays.data,
            localH: data.groupHolidays,
            groupHExceptions: data.groupHolidaysExc
        })
        if (endGroupLearning) {
            endDate = new Date(endGroupLearning);
            if (endDate) {
                groupStudyHours = getCurrentGroupStudyHours(new Date(endDate), data.groupScheduleChanges, data.group.totalHoursAmount, {
                    globalH: data.generalHolidays.data,
                    localH: data.groupHolidays,
                    groupHExceptions: data.groupHolidaysExc
                });
                groupStudyDays = getCurrentGroupStudyDays(new Date(endDate), data.groupScheduleChanges, data.group.totalHoursAmount, {
                    globalH: data.generalHolidays.data,
                    localH: data.groupHolidays,
                    groupHExceptions: data.groupHolidaysExc
                });
                groupRemainingDays = calculateGroupRemainingDays(groupStudyDays, new Date(endDate), data.groupScheduleChanges, data.group.totalHoursAmount, {
                    globalH: data.generalHolidays.data,
                    localH: data.groupHolidays,
                    groupHExceptions: data.groupHolidaysExc
                })
                groupRemainingHours = data.group.totalHoursAmount - groupStudyHours;
                percentage = +((groupStudyHours / data.group.totalHoursAmount) * 100).toFixed(0);
            }
        } else {
            endDate = ''
        }
    }

    $:studentFromCurrentGroup = students?.filter((s) => s.currentGroup === group.name);

    const modalStore = getModalStore();

    onMount(() => {
        let previousPath = $page?.url?.pathname;
        if (previousPath) {
            $PreviousTaskPageState = previousPath
        }
    });

    const loadMoreTasks = async () => {
        if (tasksCount > tasks.length) {
            TaskPagingState.set({
                skip: 0,
                take: pageSize + tasks.length
            });
            await loadingWrap(async () => {
                await invalidate('load:groups/id');
            });
        }
    };


    const openCreateGroupModal = () => {
        GroupEditModalState.set({
            ...group,
            action: 'update',
            timeStart: getTimeFromDateString(group?.timeStart),
            timeEnd: getTimeFromDateString(group?.timeEnd),
            hoursSpendBySession: data.groupScheduleChanges[data.groupScheduleChanges.length - 1]?.hoursPerSession,
            daysSchedule: DbFormatStringToDaysScheduleObject(data.groupScheduleChanges[data.groupScheduleChanges.length - 1]?.daysSchedule),
            groupScheduleChanges: data.groupScheduleChanges,
            generalHolidays: data.generalHolidays.data,
            groupHolidays: data.groupHolidays,
            groupHolidaysExc: data.groupHolidaysExc
        });
        modalStore.trigger({type: 'component', component: 'createGroupModal'});
    };

    const openStopGroupModal = () => {
        $GroupEditModalState = _.cloneDeep(group)
        modalStore.trigger({type: 'component', component: 'stopGroupModal'});
    };

    const openEditStudentModal = (event) => {
        const studentStudyHoursAndDays = calculateStudentStudyHoursAndDays(event.detail.row.studentLearningHistory, data.generalHolidays.data);
        $TaskPagingState = _.cloneDeep(initialTaskPaging)
        StudentEditModalState.set({
            ...event.detail.row, ...studentStudyHoursAndDays,
            generalHolidays: data.generalHolidays.data,
            groups
        });
        modalStore.trigger({type: 'component', component: 'updateStudentModal'});
    };

    const openSendNotificationStudentModal = (event) => {
        SendNotificationModalState.set({
            ...get(SendNotificationModalState),
            id: generateGuid(),
            type: 'student',
            title: `${event.detail.recipientName}`,
            recipientId: event.detail.recipientId
        });
        modalStore.trigger({type: 'component', component: 'sendNotificationStudentModal'});
    };

    const newTask = () => {
        goto(`/tasks/${generateGuid()}?isn=1&groupId=${group.id}`);
    };

    onDestroy(() => {
        $GroupEditModalState = _.cloneDeep(initialGroupForm);
        $StudentFilterState = _.cloneDeep(initialStudentFilter);
        $TaskFilterState = _.cloneDeep(initialTaskFilter);
    });
</script>

<div class="flex flex-col gap-5 h-[calc(100vh-85px)] overflow-hidden  mx-6 ">
    <div class="mt-3 flex items-center justify-between">
        <div class="flex items-center justify-center">
            <ol class="breadcrumb gap-x-1">
                <li class="crumb">
                    <a class="anchor" href="/groups"
                    ><h1 class="title mb-1 font-medium text-xl">{$t('groups.groups')}</h1></a
                    >
                </li>
                <li class="crumb-separator !m-1" aria-hidden="true">
                    <h1 class="title mb-1 font-medium text-xl">{'>'}</h1>
                </li>
                <li class="crumb">
                    <h1 class="title mb-1 font-medium text-xl">{group?.name}</h1>
                </li>
            </ol>
        </div>

        <div class="flex justify-end items-center gap-4">
            <GroupInfo {group}
                       endDate={endDate ? endDate : ''}
                       {percentage}
                       {groupStudyHours}
                       {groupStudyDays}
                       {groupRemainingDays}
                       {groupRemainingHours}
                       groupScheduleChanges={data.groupScheduleChanges}/>
        </div>
        <OnlyForRole>
            <div class="flex justify-end items-center gap-4">
                <BaseButton on:click={openCreateGroupModal}>
                    {$t('groups.edit')}
                    <IconEdit size={20} stroke="1.5"/>
                </BaseButton>
            </div>
        </OnlyForRole>

    </div>
    <TabGroup class="flex flex-col overflow-hidden h-full">
        <Tab bind:group={$GroupTabSet} name="tab1" value={0}>
            <b
            >{$t('groups.tabsName.tasks')}
                <CountSpan bind:count={tasksCount}/>
            </b>
        </Tab>
        {#if !group.isPublic}
            {#if studentFromCurrentGroup.length > 0 }
                <Tab bind:group={$GroupTabSet} name="tab2" value={1}>
                    <b
                    >{$t('groups.tabsName.students')}
                        <CountSpan bind:count={studentFromCurrentGroup.length}/>
                    </b>
                </Tab>
            {/if}
            <Tab bind:group={$GroupTabSet} name="tab4" value={2}>
                <b>
                    {$t('groups.tabsName.history')}
                    <CountSpan bind:count={groupHistoryCount}/>
                </b>
            </Tab>
        {/if}
        <OnlyForRole>
            <Tab bind:group={$GroupTabSet} name="tab3" value={3}>
                <b>
                    {$t('groups.tabsName.settings')}
                </b>
            </Tab>
        </OnlyForRole>
        <OnlyForRole>
            <Tab bind:group={$GroupTabSet} name="tab4" value={4}>
                <b>
                    Surveys
                </b>
            </Tab>
        </OnlyForRole>

        <svelte:fragment slot="panel">
            {#if $GroupTabSet === 0}
                <div class="flex flex-col overflow-hidden h-full gap-2">
                    <BaseButton className="self-start" on:click={newTask}>
                        {$t('tasks.new')}
                        <IconPlus/>
                    </BaseButton>
                    <div class="flex flex-col overflow-auto ">
                        <GroupSpecificTasksTable loadMoreFunc={loadMoreTasks} bind:tasks bind:groups
                                                 students={students}/>
                    </div>

                </div>

            {:else if $GroupTabSet === 1}
                <div class="flex flex-col h-full overflow-hidden">
                    <StudentsTable
                            fromGroup={true}
                            on:triggerSendMessageModal={openSendNotificationStudentModal}
                            on:triggerEditModal={openEditStudentModal}
                            students={studentFromCurrentGroup}
                            {groups}
                            generalHolidays={data.generalHolidays.data}
                    />
                </div>

            {:else if $GroupTabSet === 2}
                <div class="flex flex-col h-full overflow-hidden">
                    <GroupHistory {groupHistory} {groups}
                                  generalHolidays={data.generalHolidays.data}
                                  on:triggerSendMessageModal={openSendNotificationStudentModal}
                                  on:triggerEditModal={openEditStudentModal}
                    />
                </div>
            {:else if $GroupTabSet === 3}
                <div class="flex flex-col h-full overflow-hidden">
                    <GroupSettings groupHolidaysExc={data.groupHolidaysExc} generalHolidays={data.generalHolidays.data}
                                   groupHolidays={data.groupHolidays} groupScheduleChanges={data.groupScheduleChanges}
                                   on:triggerStopGroupModal={openStopGroupModal} {group}
                    />
                </div>

            {:else if $GroupTabSet === 4}
                <Surveys linkDailySurvey={data?.group?.dailySurveyUrl} linkRareSurvey={data?.group?.rareSurveyUrl}
                         rareSurvayDate={data?.group?.rareSurveyDate}/>
            {/if}
        </svelte:fragment>
    </TabGroup>
</div>


<style lang="scss">
  :global(.tab-panel) {
    flex: 1 1 0;
    overflow: hidden;
  }
</style>

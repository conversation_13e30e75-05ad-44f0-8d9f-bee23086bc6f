<script lang="ts">
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {initialSendNotificationModalState, SendNotificationModalState} from '$lib/state/send-notification-state';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import {IconCircleLetterX, IconSend} from '@tabler/icons-svelte';
    import {CampaignApiClient} from '$lib/core/api-clients/campaign-api.client';
    import {onDestroy, onMount} from 'svelte';
    import type {CampaignByRecipientIdDto, CampaignDto} from '$common/models/dtos/notification.dto';
    import NotificationsSkeleton from '$components/notifications/NotificationsSkeleton.svelte';
    import NotificationsHistory from '$components/notifications/NotificationsHistory.svelte';
    import _ from 'lodash';
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";

    let userNotificationsHistory: CampaignByRecipientIdDto[] = [];
    let loading = false;
    const modalStore = getModalStore();
    $: disableButton = !$SendNotificationModalState.message;

    onMount(async () => {
        loading = true;
        const data = await new CampaignApiClient().getAllCampaignsByRecipientId(
            $SendNotificationModalState.recipientId
        );
        userNotificationsHistory = [...data];
        loading = false;
    });

    const handleSubmit = async() => {
       await new CampaignApiClient().createCampaign(
            _.omit($SendNotificationModalState, ['groupLang']) as CampaignDto
        );
        modalStore.close();
        NotificationStore.push(
            {
                type: NotificationType.success,
                message: t.get('campaigns.notifications.message.success')
            }, 5);
    };

    onDestroy(() => {
        $SendNotificationModalState = _.cloneDeep(initialSendNotificationModalState)
    })
</script>

{#if $modalStore[0]}
    <div class="modal card w-1/2 shadow-xl p-8 overflow-hidden">
        <div class="flex flex-col gap-4">
            <div dir="ltr" class="text-2xl font-bold">
                {$t(
                    `campaigns.modal.${
                        $SendNotificationModalState.type === 'student' ? 'titleStudent' : 'titleGroup'
                    }.${$SendNotificationModalState.groupLang}`
                )}
                {$SendNotificationModalState.title}
            </div>
            <div class="flex-1">
				<textarea
                        dir="ltr"
                        name="message"
                        placeholder={$t('campaigns.modal.placeholder')}
                        class="resize-none input rounded p-4 w-full h-1/2"
                        bind:value={$SendNotificationModalState.message}
                />
            </div>
            <div class="flex justify-between items-center">
                <BaseButton disabled={disableButton} on:click={handleSubmit}>
                    <IconSend/>
                    {$t('campaigns.modal.submit')}
                </BaseButton>
                <BaseButton on:click={() => modalStore.close()}>
                    <IconCircleLetterX/>
                    {$t('campaigns.modal.cancel')}
                </BaseButton>
            </div>
        </div>
        {#if loading}
            <NotificationsSkeleton/>
        {:else}
            <NotificationsHistory {userNotificationsHistory}/>
        {/if}
    </div>
{/if}

import type {Handle} from '@sveltejs/kit';
import {redirect} from "@sveltejs/kit";
import {AlreadyLoggedInForbiddenRoutes, ForbiddenByRoleRoutes, HomePageByRole} from "$common/core/routes";
import {Constants} from "$api/core/constants";

const checkPathForStudent = (path: string) => {
    if (path === '/') {
        return true
    }
    return ForbiddenByRoleRoutes[Constants.StudentRole].some((x) => path.startsWith(x))
}

export const authorizationMiddleware: Handle = async ({event, resolve}) => {
    const {url} = event;
    const {user} = event?.locals;

    const isNotApiRequest = !url.href.includes('/api/');
    const isNotAuthorizedByRoleRequest = user?.role && user?.role === Constants.StudentRole ? checkPathForStudent(url.pathname) : ForbiddenByRoleRoutes[user?.role]?.some((x) => url.pathname.includes(x));
    const isAlreadyLoggedInRedirect = user?.role && AlreadyLoggedInForbiddenRoutes?.some((x) => url.pathname.startsWith(x));


    if (isNotApiRequest && (isNotAuthorizedByRoleRequest || isAlreadyLoggedInRedirect)) {
        console.log('authorizationMiddleware:::redirected to login because ' + isNotAuthorizedByRoleRequest ? 'isNotAuthorizedByRoleRequest:true; ' : 'isAlreadyLoggedInRedirect:true' + '; url: ' + url.href)
        throw redirect(302, HomePageByRole[user?.role]);
    }

    return resolve(event);
};

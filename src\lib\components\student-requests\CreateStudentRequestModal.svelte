<script lang="ts">
    import { getModalStore } from '@skeletonlabs/skeleton';
    import { deserialize } from '$app/forms';
    import { invalidate } from '$app/navigation';
    import { t } from '$lib/i18n/config';
    import { StudentRequestCreateModalState } from '$lib/state/student-request-create-state';
    import { NotificationType } from '$common/models/enums';
    import { onMount } from 'svelte';
    import { loadingWrap } from '$lib/common/utils';
    import NotificationStore from "$lib/state/notification-state";
    import BaseInput from '$components/common/BaseInput.svelte';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import { IconDeviceFloppy, IconCircleLetterX } from '@tabler/icons-svelte';
    import { getDateString } from '$lib/common/utils';
    import SveltyPicker from 'svelty-picker';

    const modalStore = getModalStore();

    let modalErrors:
        | {
            field: string | number;
            message: string;
        }[]
        | undefined;

    let formData = {
        tz: '',
        firstname: '',
        lastname: '',
        email: '',
        phone: '',
        whatsapp: '',
        dob: '',
        city: '',
        groupLevel: '',
        groupLang: '',
        groupStartDate: '',
        learnStartDate: ''
    };

    let dob = '';
    let groupStartDate = '';
    let learnStartDate = '';

    const groupLevelOptions = [
        { value: 'א', displayValue: 'א' },
        { value: 'א+', displayValue: 'א+' },
        { value: 'ב', displayValue: 'ב' },
        { value: 'ב+', displayValue: 'ב+' },
        { value: 'ג', displayValue: 'ג' },
        { value: 'ד', displayValue: 'ד' },
        { value: 'ה', displayValue: 'ה' }
    ];

    const groupLangOptions = [
        { value: 'russian', displayValue: 'Russian' },
        { value: 'english', displayValue: 'English' }
    ];

    onMount(() => {
        StudentRequestCreateModalState.subscribe((state) => {
            formData = { ...state };
            dob = formData.dob ? getDateString(formData.dob) || '' : '';
            groupStartDate = formData.groupStartDate ? getDateString(formData.groupStartDate) || '' : '';
            learnStartDate = formData.learnStartDate ? getDateString(formData.learnStartDate) || '' : '';
        });
    });

    async function handleSubmit(event) {
        const data = new FormData(this);
        
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            modalErrors = result.data.errors;
        } else {
            NotificationStore.push(
                {
                    type: NotificationType.success,
                    message: t.get('students.requests.notifications.create.success')
                }, 5);
            await loadingWrap(async () => {
                await invalidate('load:students');
            });
            modalStore.close();
        }
    }

    function getErrorMessage(fieldName: string): string | undefined {
        const error = modalErrors?.find(e => e.field === fieldName);
        return error ? $t(error.message) : undefined;
    }
</script>

{#if $modalStore[0]}
    <div class="modal max-h-[700px] overflow-y-auto card py-6 px-6 w-2/3 shadow-xl space-y-4">
        <header class="text-xl font-bold">{$t('students.requests.modal.createTitle')}</header>

        <form action="?/createStudentRequest" on:submit|preventDefault={handleSubmit} class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <BaseInput
                        name="tz"
                        title={$t('students.students.modal.fields.tz')}
                        bind:value={formData.tz}
                        required
                    />
                    {#if getErrorMessage('tz')}
                        <span class="text-error-500 text-sm">{getErrorMessage('tz')}</span>
                    {/if}
                </div>

                <div>
                    <BaseInput
                        name="firstname"
                        title={$t('students.students.modal.fields.firstname')}
                        bind:value={formData.firstname}
                        required
                    />
                    {#if getErrorMessage('firstname')}
                        <span class="text-error-500 text-sm">{getErrorMessage('firstname')}</span>
                    {/if}
                </div>

                <div>
                    <BaseInput
                        name="lastname"
                        title={$t('students.students.modal.fields.lastname')}
                        bind:value={formData.lastname}
                        required
                    />
                    {#if getErrorMessage('lastname')}
                        <span class="text-error-500 text-sm">{getErrorMessage('lastname')}</span>
                    {/if}
                </div>

                <div>
                    <BaseInput
                        name="email"
                        title={$t('students.students.modal.fields.email')}
                        bind:value={formData.email}
                    />
                    {#if getErrorMessage('email')}
                        <span class="text-error-500 text-sm">{getErrorMessage('email')}</span>
                    {/if}
                </div>

                <div>
                    <BaseInput
                        name="phone"
                        title={$t('students.students.modal.fields.phone')}
                        bind:value={formData.phone}
                    />
                </div>

                <div>
                    <BaseInput
                        name="whatsapp"
                        title={$t('students.students.modal.fields.whatsapp')}
                        bind:value={formData.whatsapp}
                    />
                </div>

                <div>
                    <span class="block input__title font-medium text-base mb-1">
                        {$t('students.students.modal.fields.dob')}
                    </span>
                    <SveltyPicker
                        name="dob"
                        mode="date"
                        clearToggle={false}
                        weekStart={0}
                        initialDate={dob ? new Date(dob) : null}
                        format="yyyy-mm-dd"
                        bind:value={dob}
                        inputClasses="w-full input rounded h-10 p-2"
                    />
                    {#if getErrorMessage('dob')}
                        <span class="text-error-500 text-sm">{getErrorMessage('dob')}</span>
                    {/if}
                </div>

                <div>
                    <BaseInput
                        name="city"
                        title={$t('students.students.modal.fields.city')}
                        bind:value={formData.city}
                        required
                    />
                    {#if getErrorMessage('city')}
                        <span class="text-error-500 text-sm">{getErrorMessage('city')}</span>
                    {/if}
                </div>

                <div>
                    <BaseSelect
                        name="groupLevel"
                        title={$t('students.students.modal.fields.groupLevel')}
                        options={groupLevelOptions}
                        bind:value={formData.groupLevel}
                    />
                    {#if getErrorMessage('groupLevel')}
                        <span class="text-error-500 text-sm">{getErrorMessage('groupLevel')}</span>
                    {/if}
                </div>

                <div>
                    <BaseSelect
                        name="groupLang"
                        title={$t('students.students.modal.fields.groupLang')}
                        options={groupLangOptions}
                        bind:value={formData.groupLang}
                    />
                    {#if getErrorMessage('groupLang')}
                        <span class="text-error-500 text-sm">{getErrorMessage('groupLang')}</span>
                    {/if}
                </div>

                <div>
                    <span class="block input__title font-medium text-base mb-1">
                        {$t('students.students.modal.fields.groupStartDate')}
                    </span>
                    <SveltyPicker
                        name="groupStartDate"
                        mode="date"
                        clearToggle={false}
                        weekStart={0}
                        initialDate={groupStartDate ? new Date(groupStartDate) : null}
                        format="yyyy-mm-dd"
                        bind:value={groupStartDate}
                        inputClasses="w-full input rounded h-10 p-2"
                    />
                    {#if getErrorMessage('groupStartDate')}
                        <span class="text-error-500 text-sm">{getErrorMessage('groupStartDate')}</span>
                    {/if}
                </div>

                <div>
                    <span class="block input__title font-medium text-base mb-1">
                        {$t('students.students.modal.fields.learnStartDate')}
                    </span>
                    <SveltyPicker
                        name="learnStartDate"
                        mode="date"
                        clearToggle={false}
                        weekStart={0}
                        initialDate={learnStartDate ? new Date(learnStartDate) : null}
                        format="yyyy-mm-dd"
                        bind:value={learnStartDate}
                        inputClasses="w-full input rounded h-10 p-2"
                    />
                    {#if getErrorMessage('learnStartDate')}
                        <span class="text-error-500 text-sm">{getErrorMessage('learnStartDate')}</span>
                    {/if}
                </div>
            </div>

            <div class="flex justify-between mt-4">
                <BaseButton type="submit">
                    <IconDeviceFloppy/> {$t('students.requests.modal.buttons.create')}
                </BaseButton>
                <BaseButton on:click={() => modalStore.close()}>
                    <IconCircleLetterX/> {$t('students.requests.modal.buttons.cancel')}
                </BaseButton>
            </div>
        </form>
    </div>
{/if}

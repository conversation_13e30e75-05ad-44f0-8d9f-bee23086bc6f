import {wrapFunc} from '$api/core/misc/response-wrapper';
import {mapper} from '$common/core/mapper';
import type {TaskFilterDto} from '$common/models/filters/task-filter.dto';
import type {RequestEvent} from '@sveltejs/kit';
import {createTask, deleteTask, getTaskById, getTasks} from '$api/core/services/task.service';
import {paramsToKeyValue} from '$api/core/utils';

export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id, take, skip, groupId, type, search, createdBy} = paramsToKeyValue(url.searchParams);

        if (id) {
            return await getTaskById(id);
        }

        return await getTasks(
            mapper<TaskFilterDto, unknown>({
                take: +take,
                skip: +skip,
                groupId,
                type,
                search,
                createdBy
            })
        );
    });

export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const task = await event.request.json();
        return await createTask(task, event?.locals?.user?.id);
    });


export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);
        return await deleteTask(id)
    })

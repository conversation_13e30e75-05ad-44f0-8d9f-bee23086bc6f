<script lang="ts">
    import {format} from "date-fns";
    import BaseInput from "$components/common/BaseInput.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import BaseSwitch from "$components/common/BaseSwitch.svelte";
    import type {GroupHolidayDto} from "$common/models/dtos/group-holiday.dto";
    import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
    import type {GroupHolidayExcDto} from "$common/models/dtos/group-holiday-exc.dto";
    import {t} from "$lib/i18n/config";
    import {GroupHolidaysExcApiClient} from "$lib/core/api-clients/groupHolidaysExc-api-client";
    import {generateGuid} from "$common/core/utils";
    import {formatToPickerDate, loadingWrap} from "$lib/common/utils";
    import {invalidate} from "$app/navigation";
    import {GroupHolidaysApiClient} from "$lib/core/api-clients/groupHolidays-api-client";


    export let groupHolidays: GroupHolidayDto[];


    export let generalHolidays: GeneralHolidayDto[]

    export let groupHolidaysExc: GroupHolidayExcDto[];

    export let groupId: string;

    const onChangeSwitch = async (ghe: GroupHolidayExcDto, generalHolidayId: string) => {
        if (ghe) {
            await new GroupHolidaysExcApiClient().deleteGroupHolidaysExceptions(ghe.id)
        } else {
            await new GroupHolidaysExcApiClient().postGroupHolidaysExceptions({
                id: generateGuid(),
                groupId,
                generalHolidayId
            })
        }
        await loadingWrap(async () => {
            await invalidate('load:groups/id');
        });
    }

    const deleteLocalHoliday = async (groupHoliday: GroupHolidayDto) => {
        if (groupHoliday.isNew) {
            groupHolidays = groupHolidays.filter((g) => g.id !== groupHoliday.id)
        } else {
            await new GroupHolidaysApiClient().deleteGroupHoliday(groupHoliday.id);
            await loadingWrap(async () => {
                await invalidate('load:groups/id');
            });
        }
    }

    const createNewLocalHoliday = () => {
        groupHolidays = [
            {
                id: generateGuid(),
                date: formatToPickerDate(new Date()),
                comment: '',
                groupId,
                isNew: true
            },
            ...groupHolidays
        ]
    }

    const save = async (groupHoliday: GroupHolidayDto) => {
        if (!groupHoliday.date || !groupHoliday.comment) return ''
        await new GroupHolidaysApiClient().postGroupHolidays(groupHoliday);
        await loadingWrap(async () => {
            await invalidate('load:groups/id');
        });

    }


    $: tableTitles = [
        $t('groups.groupSettings.holidayTable.tableTitles.date'),
        $t('groups.groupSettings.holidayTable.tableTitles.comment'),
        $t('groups.groupSettings.holidayTable.tableTitles.type'),
        $t('groups.groupSettings.holidayTable.tableTitles.action'),
    ];
</script>


<div class="flex flex-col gap-5 h-full mt-5 ">
    <div class="flex justify-between items-center">
        <h1 class="title mb-1 font-medium text-xl">{$t('groups.groupSettings.holidayTable.title')}</h1>
        <BaseButton on:click={createNewLocalHoliday} className="self-start">
            +
        </BaseButton>
    </div>

    <table class="table overflow-y-auto overflow-x-auto table-hover table-compact h-fit">
        <thead on:keypress>
        <tr>
            {#each tableTitles as title}
                <th class="text-right  {title==='Action'?'w-[20%]':''}">{title}</th>
            {/each}
        </tr>
        </thead>
        <tbody>
        {#each groupHolidays as holiday,index}
            <tr>
                {#if holiday.isNew}
                    <td>
                        <input dir="rtl" class="input h-[43px] rounded pr-[5px] w-[70%]" type="date"
                               bind:value={groupHolidays[index].date}>
                    </td>
                {:else}
                    <td>{format(new Date(holiday.date), 'dd.MM.yyyy')}</td>
                {/if}
                {#if holiday.isNew}
                    <td class="">
                        <BaseInput class="w-full" type="text" bind:value={groupHolidays[index].comment}/>
                    </td>
                {:else}
                    <td>{holiday.comment}</td>
                {/if}
                <td>
                    {$t('groups.groupSettings.holidayTable.typeLocal')}
                </td>
                <td>
                    <BaseButton on:click={()=>{
                        deleteLocalHoliday(holiday)
                    }}>
                        {$t('groups.groupSettings.holidayTable.buttons.delete')}
                    </BaseButton>
                    {#if holiday.isNew}
                        <BaseButton on:click={()=>{
                          save(holiday)
                        }}>
                            {$t('groups.groupSettings.holidayTable.buttons.save')}
                        </BaseButton>
                    {/if}
                </td>
            </tr>
        {/each}
        {#each generalHolidays as gHoliday}
            {@const isExc = groupHolidaysExc.find((ghe) => ghe.generalHolidayId === gHoliday.id)}
            <tr>
                <td>{format(new Date(gHoliday.date), 'dd.MM.yyyy')}</td>
                <td>{gHoliday.comment}</td>
                <td>{$t('groups.groupSettings.holidayTable.typeGlobal')}</td>
                <td>
                    <BaseSwitch on:change={()=>onChangeSwitch(isExc,gHoliday.id)} checked={!isExc}/>
                </td>
            </tr>
        {/each}
        </tbody>
    </table>
</div>
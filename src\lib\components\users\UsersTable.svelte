<script lang="ts">
    import {afterUpdate, createEventDispatcher} from 'svelte';
    import BaseButton from '../common/BaseButton.svelte';
    import {t} from '$lib/i18n/config';
    import {IconEdit} from '@tabler/icons-svelte';
    import type {UserDto} from '$common/models/dtos/user.dto';
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";

    export let users: UserDto[];
	export let loadMoreFunc;

    const dispatchModal = createEventDispatcher();

    $: tableTitles = [
        $t('users.table.head.tz'),
        $t('users.table.head.firstname'),
        $t('users.table.head.lastname'),
        $t('users.table.head.email'),
        $t('users.table.head.phone'),
        $t('users.table.head.edit')
    ];


</script>

<div class="h-full ">
    <table class="table overflow-y-auto overflow-x-auto table-hover table-compact h-fit">
        <thead on:keypress>
        <tr>
            {#each tableTitles as title}
                <th class="text-right">{title}</th>
            {/each}
        </tr>
        </thead>
        <tbody>
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each users as row, rowIndex}
                <tr>
                    <td>{row.tz}</td>
                    <td>{row.firstname}</td>
                    <td>{row.lastname}</td>
                    <td>{row.email}</td>
                    <td>{row.phone}</td>
                    <td>
                        <BaseButton
                                size="sm"
                                on:click={() => dispatchModal('triggerModal', { action: 'update', row })}
                        >
                            {$t('users.table.editButton')}
                            <IconEdit size={20} stroke="1.5"/>
                        </BaseButton>
                    </td>
                </tr>
            {/each}

        </InfiniteTableScrollContainer>
        </tbody>
    </table>
</div>

<style>
    table td {
        vertical-align: middle;
    }
</style>

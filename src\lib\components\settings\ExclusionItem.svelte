<script lang="ts">
    import {t} from "$lib/i18n/config.js";
    import BaseInput from "$components/common/BaseInput.svelte";
    import {IconCircleMinus} from "@tabler/icons-svelte";
    import type {ExclusionsDto} from "$common/models/dtos/Exclusions.dto";

    export let exclusion: ExclusionsDto;

    export let onInput: (exclusion: ExclusionsDto) => void;
    export let updateInputError: string;

    export let deleteExclusion: (id: string) => void;
</script>

<div class="flex gap-1.5">
    <div class="flex flex-col max-w-[500px] w-full">
        <BaseInput on:input={()=>{onInput(exclusion)}} className="w-full"
                   bind:value={exclusion.exclusions}/>
        {#if updateInputError}
            <p dir="rtl" class="text-red-600">
                {$t(updateInputError)}
            </p>
        {/if}
    </div>

    <div class="cursor-pointer" on:click={()=>{deleteExclusion(exclusion.id)}}>
        <IconCircleMinus class="w-10 h-10 mr-2 text-red-700 cursor-pointer"/>
    </div>
</div>
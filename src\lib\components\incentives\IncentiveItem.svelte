<script lang="ts">
    import BaseInput from '$components/common/BaseInput.svelte';
    import ContentEditor from '$components/content/ContentEditor.svelte';
    import {slide} from 'svelte/transition';
    import {IconChevronDown, IconCircleMinus} from '@tabler/icons-svelte';
    import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
    import {t} from '$lib/i18n/config';
    import LexicalContent from "$components/common/LexicalContent.svelte";

    export let id;
    export let name: string;

    export let content: any;
    export let comment: string;
    export let isNew: boolean;

    export let pageContent;


    $: isOpenItem = isNew;
    export let save: (incentiveContent: IncentiveContentDto) => void;
</script>

<!--variant-glass-primary-->
<div class="flex-1 card bg-primary-400 break-all">
    <div
            class="flex justify-between py-2 min-h-[50px] px-8 cursor-pointer"
            on:click={() => (isOpenItem = !isOpenItem)}
    >
        <div class="flex items-center w-full gap-5">
            {#if isOpenItem}
                <div on:click={(e) => e.stopPropagation()} class=" min-w-[150px]">
                    <BaseInput
                            title={$t('incentiveContent.incentiveContentItem.name')}
                            bind:value={name}
                    />
                </div>
                <div on:click={(e) => e.stopPropagation()} class="flex-1 w-full">
                    <BaseInput
                            title={$t('incentiveContent.incentiveContentItem.comment')}
                            bind:value={comment}
                    />
                </div>
            {:else}
                <div class=" min-w-[150px]">
                    {name}
                </div>
                <div class="flex-1 w-full">
                    {comment}
                </div>
            {/if}
        </div>
        <div class="flex justify-end items-center cursor-pointer mr-3">
            <IconChevronDown/>
        </div>
    </div>
    {#if isOpenItem}
        <div
                dir="ltr"
                class=" pt-5 pb-10 pl-9 pr-9 flex flex-col justify-center items-center"
                transition:slide={{ duration: 500 }}>
            <div class="w-full flex flex-col">
                {#if content}
                    <ContentEditor
                            readMode={false}
                            on:contentUpdated={(e) => {
						save({ id, name, value: e.detail.content, comment, isNew });
						isOpenItem = false;
					}}
                            {id}
                            bind:content
                    />
                {:else}
                    <LexicalContent
                            bind:content={pageContent}
                            on:contentUpdated={(e) => {
						        save({ id, name, value: '', comment, isNew, page_content:pageContent }
						    );
					    	isOpenItem = false;
					}}
                    />
                {/if}
            </div>
        </div>
    {/if}
</div>

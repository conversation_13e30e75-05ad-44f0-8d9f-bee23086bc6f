<div role="status" class="animate-pulse flex flex-col gap-4 mt-4 p-2 max-h-[180px] overflow-hidden">
	<div class=" flex gap-5">
		<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 flex-1 mb-4" />
		<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 flex-1 mb-4" />
		<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 mb-4 flex-1" />
		<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 mb-4 flex-1" />
	</div>
	<div class="flex flex-col gap-4 break-all">
		{#each { length: 2 } as _, i}
			<div class="flex gap-5">
				<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 flex-1 mb-2" />
				<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 flex-1 mb-2" />
				<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 mb-2 flex-1" />
				<div class="h-2.5 bg-gray-700 rounded-full dark:bg-gray-200 mb-2 flex-1" />
			</div>
		{/each}
	</div>
</div>

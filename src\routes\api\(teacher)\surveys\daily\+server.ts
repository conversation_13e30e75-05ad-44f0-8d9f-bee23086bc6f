import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {mapper} from "$common/core/mapper";
import type {DailySurveyDto} from "$common/models/dtos/daily-survey.dto";
import {updateGroupDailySurvey} from "$api/core/services/group.service";


export const PUT = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const dailySurvey = await event.request.json();
        const dto = mapper<DailySurveyDto, unknown>(dailySurvey);
        await updateGroupDailySurvey(dto);
    });
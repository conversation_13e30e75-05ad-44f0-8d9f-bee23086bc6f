{"title": "Users", "addUserButton": "Add user", "placeHolderSearch": "Search", "table": {"head": {"tz": "Tz", "firstname": "Firstname", "lastname": "Lastname", "email": "Email", "phone": "Phone", "edit": "Actions"}, "editButton": "Edit"}, "modalUsers": {"titles": {"titleToCreate": "Create new user", "titleToUpdate": "Change data user", "titlePermissions": "Permissions"}, "submitButton": {"update": "Save", "create": "Create"}, "formFields": {"firstname": "Firstname", "lastname": "Lastname", "phone": "Phone", "email": "Email", "password": "Password", "tz": "TZ", "role": "Role", "permissions": {"editFavorites": "Edit favorite sentences", "deleteNonFavSentences": "Delete sentences"}}, "formFieldsErrors": {"tzMin": "The user tz must contain at least 6 characters", "tzMax": "The user tz must have a maximum of 12 characters", "firstname": "The firstname must contain at least 2 characters", "lastname": "The lastname must contain at least 2 characters", "emailLength": "The user email must contain at least 2 characters", "email": "Invalid email", "phone": "The user phone must be correct", "passwordMin": "The password must contain at least 5 characters", "passwordUpperLetter": "Password must contain at least one uppercase letter", "passwordDigit": "The password must contain at least one digit", "passwordToUpdate": "The password must contain at least 5 characters and consist of at least one number and a letter"}, "selectFields": {"role": {"teacher": "teacher", "admin": "admin", "disabled": "disabled"}}, "notifications": {"create": {"success": "User successfully created"}, "update": {"success": "User successfully updated"}}}}
import type {
    CompletionTaskStateModel,
    TaskDto,
    TaskResultDto,
    TranslationCompletion
} from "$common/models/dtos/task.dto";
import _ from "lodash";
import {compareFn} from "$common/core/utils";
import {intervalToDuration} from "date-fns";
import {TaskMode} from "$common/models/enums";
import type {SentenceInTaskDto} from "$common/models/dtos/sentence.dto";
import {initialDate} from "$lib/state/task-current-completion.state";
import {animateScroll} from "svelte-scrollto-element";
import {browser} from "$app/environment";

export const isInitialDate = (date: Date, initialDate: Date): boolean => {
    return new Date(date)?.getFullYear() === initialDate?.getFullYear();
}

export const checkTranslationResponse = (model: TaskResultDto, maxScore: number) => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    _(model.completions).forEach((x: TranslationCompletion) => {
        x.mistakes = compareFn(x.correctValue, x.answerValue);
        x.isCorrect = x.mistakes.length === 0 && x.answerValue.length > 0;
    });
    model.finishedAt = new Date();
    model.spentTime = intervalToDuration({
        start: new Date(!model.startedAt ? model.finishedAt : model.startedAt),
        end: new Date(model.finishedAt)
    });
    model.hintsUsed = _.reduce(_.map(model.completions, x => (x as TranslationCompletion).hintUsedTimes), (a, c) => a += c, 0)
    model.scorePercent = parseFloat(((100 / model.completions.length) * _.filter(model.completions, x => !!x.isCorrect).length).toFixed(2));
    model.scoreAbsolute = parseFloat(((maxScore / model.completions.length) * _.filter(model.completions, x => !!x.isCorrect).length).toFixed(2));
}

export const checkListeningResponse = (model: TaskResultDto, maxScore: number) => {
    model.finishedAt = new Date();
    model.spentTime = intervalToDuration({
        start: new Date(!model.startedAt ? model.finishedAt : model.startedAt),
        end: model.finishedAt
    });
    model.scorePercent = (100 / model.completions.length) * _.filter(model.completions, x => x.isCorrect).length;
    model.scoreAbsolute = (maxScore / model.completions.length) * _.filter(model.completions, x => x.isCorrect).length;
}

export const getBestResultForMode = (mode: TaskMode, state: CompletionTaskStateModel): number => _.maxBy(state?.results?.filter(x => x?.mode === mode), 'scorePercent')?.scorePercent || 0;
export const getTotalAbsoluteResult = (state: CompletionTaskStateModel) => _.sum(_.uniqBy(state?.results, 'mode')?.map(x => getBestResultForMode(x.mode, state))) ?? 0
export const getAveragePercentResult = (state: CompletionTaskStateModel) => parseFloat(_.mean(_.uniqBy(state?.results, 'mode')?.map(x => getBestResultForMode(x.mode, state))).toFixed(2));

export const getTotalEnabledTaskModesCount = (task: TaskDto) => (_.values(task?.additionalTasks)?.filter(x => x.enabled)?.length ?? 0) + 1;
export const getTaskModesDone = (results: TaskResultDto[]) => _.uniqBy(results, 'mode');
export const wasTaskModeDone = (results: TaskResultDto[], mode: TaskMode) => !!_.find(results, x => x.mode === mode);

export const getBestTranslationResult = (results: TaskResultDto[]) => _.maxBy(results.filter(x => x.mode === TaskMode.translation), x => x.scorePercent);
export const getAnyBestResult = (results: TaskResultDto[]) => _.maxBy(results, x => x.scorePercent);

export const switchMode = (newMode: TaskMode, state: CompletionTaskStateModel) => {
    if (state.currentMode !== newMode || state.currentResult == null) {

        const existingResult = _.maxBy(state.results?.filter(x => x.mode == newMode), (x) => new Date(x.finishedAt));

        state.currentMode = newMode;
        state.currentResult = !existingResult
            ? generateInitialTranslationsResult(
                newMode,
                state.task.sentences,
                state.task?.lang || 'EN')
            : existingResult;

        state.intermediateState = !!existingResult;

        if (!state.intermediateState && browser) {
            try {
                animateScroll?.scrollTo({
                    y: document?.getElementById('anchor')?.offsetTop ?? 0,
                    duration: 1000,
                    container: document?.getElementById('page-content')
                });
            } catch (e) {
                console.log('wrong', e);
            }
        }
    }

    return state;
}

export const generateInitialTranslationsResult = (mode: TaskMode, sentences: SentenceInTaskDto[], lang: string, withShuffle = false): TaskResultDto => {
    const completions = withShuffle ?
        sentences.map(s => getCompletionItemBasedOnMode(s, mode, lang)) :
        sentences.map(s => getCompletionItemBasedOnMode(s, mode, lang)).sort((a, b) => a.index - b.index);
    return {
        mode,
        startedAt: initialDate,
        finishedAt: initialDate,
        scorePercent: 0,
        scoreAbsolute: 0,
        hintsUsed: 0,
        spentTime: {},
        completions
    }
}

const getCompletionItemBasedOnMode = (s: SentenceInTaskDto, mode: TaskMode, lang: string): TranslationCompletion => {
    try {
        const completion = createNewDefaultCompletion(s, mode, lang);
        switch (mode) {
            case TaskMode.translation:
                return completion;
            case TaskMode.listen:
                return completion;
            case TaskMode.bytime:
                return completion;
            case TaskMode.audiodic:
                return {...completion, displayAsText: false};
            case TaskMode.phantom:
                return {...completion, displayAsText: true};
            case TaskMode.voice:
                return {...completion, displayAsText: true};
            default:
                return {} as TranslationCompletion;
        }
    } catch (e) {
        console.log(e)
    }

}

const createNewDefaultCompletion = (s: SentenceInTaskDto, mode: TaskMode, lang: string) => {
    return {
        index: s.index,
        sentenceId: s.id,
        taskValue: s.translations.find((translation) => lang === translation.lang)?.value || '',
        correctValue: s.value,
        audioUrl: s.audioUrl ?? '',
        sex: s.sex,
        displayAsText: s.displayAsText,
        answerValue: '',
        isCorrect: false,
        mistakes: '',
        hintUsedTimes: 0
    }
}

export const formatCountdown = (secondsLeft: number) => {
    const minutes = (secondsLeft / 60) | 0;
    const seconds = (secondsLeft % 60) | 0;

    return (minutes < 10 ? "0" + minutes : minutes) + ":" + (seconds < 10 ? "0" + seconds : seconds);
}



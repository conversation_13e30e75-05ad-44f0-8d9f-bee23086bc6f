import {get} from 'svelte/store';
import {SentenceFilterState} from '$lib/state/sentence-filter-state';
import {SentencePagingState} from '$lib/state/sentence-paging-state';
import axios from 'axios';
import type {DataApiResponse} from '$lib/models/api-response.model';
import {BaseApiClient} from './base-api-client';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import type {GenerateTaskSentenceDto, SentenceDto, SentenceInTaskDto} from '$common/models/dtos/sentence.dto';

export class SentenceApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getSentences = async (): Promise<TableDataDto<SentenceDto>> => {
        const currentFilterState = get(SentenceFilterState);
        const currentPagingState = get(SentencePagingState);
        return await this.getDataOrThrow(
            `/api/sentences?updatedBy=${currentFilterState?.updatedBy}&isFavorite=${currentFilterState?.isFavorite}&level=${currentFilterState?.level}&search=${currentFilterState.search}&isStrict=${currentFilterState.isStrict}&lang=${currentFilterState.lang}&audio=${currentFilterState.audio}&take=${currentPagingState.take}&skip=${currentPagingState.skip}`
        );
    };


    public getShuffledSentencesForGenerate = async (dto: GenerateTaskSentenceDto) => {
        const {
            count,
            to,
            from,
            onlyFav,
            onlyAudio,
            selectedSentences,
            lang,
            groupId
        } = dto;
        return await this.getDataOrThrow(`/api/sentences/generate?count=${count}&to=${to}&from=${from}&onlyFav=${onlyFav}&onlyAudio=${onlyAudio}&selectedSentences=${selectedSentences}&lang=${lang}&groupId=${groupId}`)
    }


    public createUpdateSentence = async (sentence: SentenceDto): Promise<SentenceDto> =>
        await this.postDataOrThrow<SentenceDto>('/api/sentences', sentence);

    public deleteSentence = async (id: string): Promise<SentenceDto> =>
        await this.deleteOrThrow(`/api/sentences?id=${id}`);

    public async uploadAudio(formData: FormData, sentence: SentenceInTaskDto): Promise<string> {
        const response = await axios.post('/api/sentences/audio', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        const {data, success, error} = response.data;

        if (success) {
            await new SentenceApiClient().createUpdateSentence({...sentence, audioUrl: data});
        } else {
            throw new Error(error);
        }

        return data;
    }
}

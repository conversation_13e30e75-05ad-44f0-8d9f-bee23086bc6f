import type {RequestEvent} from '@sveltejs/kit';
import {wrapFunc} from '$api/core/misc/response-wrapper';
import {createCampaign, deleteCampaign, getAllCampaigns} from '$api/core/services/campaign.service';
import {paramsToKeyValue} from "$api/core/utils";

export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = event.locals.user;
        const campaign = await event.request.json();

        return await createCampaign({...campaign, createdBy: id});
    });

export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {authorId, type,searchByRecipient,take,skip} = paramsToKeyValue(
            url.searchParams
        );
        return await getAllCampaigns({
            authorId,
            searchByRecipient,
            type,
            take: +take,
            skip: +skip
        });
    });


export const DELETE = async ({url}:RequestEvent) =>
    wrapFunc( async()=>{
        const {id} = paramsToKeyValue(url.searchParams);
        return await deleteCampaign(id)
    })

<script lang="ts">
    import {t} from '$lib/i18n/config';
    import {get} from 'svelte/store';
    import {SentenceFilterState} from '$lib/state/sentence-filter-state';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {onDestroy, onMount} from 'svelte';
    import type {SentenceInTaskDto} from '$common/models/dtos/sentence.dto';
    import {pageSize, SentencePagingState} from '$lib/state/sentence-paging-state';
    import {SentenceApiClient} from '$lib/core/api-clients/sentence-api-client';
    import TaskSentencesCount from '$components/tasks/TaskSentencesCount.svelte';
    import InfiniteScrollContainer from '$components/common/InfiniteScrollContainer.svelte';
    import PopupSentenceSearchItem from '$components/tasks/PopupSentenceSearchItem.svelte';
    import type {SentenceDto} from '$common/models/dtos/sentence.dto';
    import _ from 'lodash';
    import SentenceFilter from "$components/sentences/SentenceFilter.svelte";

    $: countSentencesWithAudio = $CurrentEditableState.sentences.filter(
        (sentence) => sentence.audioUrl !== null
    ).length;
    $: sentencesLength = $CurrentEditableState.sentences.length;
    $: sentences = $CurrentEditableState.sentencesToChoose;
    $: count = $CurrentEditableState.sentencesToChooseCount;

    let userOptions = $CurrentEditableState.users?.map((u) => {
        return {
            value: u.id,
            displayValue: `${u.firstname} ${u.lastname}`
        };
    });

    let inputValue = $SentenceFilterState.search;
    const modalStore = getModalStore();

    const loadMore = async () => {
        if (count > sentences.length) {
            SentencePagingState.set({take: pageSize + sentences.length, skip: 0});
            const response = await new SentenceApiClient().getSentences();
            $CurrentEditableState.sentencesToChoose = [...(response?.data ?? [])];
        }
    };

    const findSentence = (id: string) => {
        return !!$CurrentEditableState.sentences.find((sentence) => sentence.id === id);
    };

    const deleteSentenceFromState = (id: string) => {
        $CurrentEditableState.sentences = $CurrentEditableState.sentences.filter(
            (sentence) => sentence.id !== id
        );
    };


    const addSentenceToState = (sentence: SentenceDto) => {
        const newSentence: SentenceInTaskDto = {
            ...sentence,
            index: 0,
            displayAsText: true
        };

        $CurrentEditableState.sentences.forEach((x, i) => (x.index = i + 1));
        $CurrentEditableState.sentences = [newSentence, ...$CurrentEditableState.sentences];
    };

    const onInput = _.debounce(
        () => SentenceFilterState.set({...get(SentenceFilterState), search: inputValue}),
        2000
    );

    onMount(() => {
        $SentenceFilterState.lang = $CurrentEditableState.lang;
    })

    onDestroy(() => {
        $SentenceFilterState.search = '';
        $SentenceFilterState.lang = 'EN';
    });
</script>

<div
        id="taskSentenceSearchPopUp"
        class="card w-[90vw] h-[90vh] p-5 overflow-hidden flex flex-col shadow-xl"
>
    <button
            class="btn-icon fixed top-2 right-2 z-50 font-bold shadow-xl variant-filled"
            on:click={() => {
			modalStore.close();
		}}
    >×
    </button>
    <div class="modal flex justify-between p-3 items-center variant-glass-primary card">
        <div class="flex-1">
            <SentenceFilter languageDisabled={false} users={$CurrentEditableState.users}/>
        </div>
        <div class="min-w-[75px] flex">
            <TaskSentencesCount {countSentencesWithAudio} {sentencesLength}/>
        </div>
    </div>
    <div id="popUpLists" class=" overflow-y-auto mt-5 flex-1 variant-glass-primary card">
        <div class=" p-5 grid grid-cols-10 align-middle">
            <!--			<div class="font-bold ml-10">Found: </div>-->
            <div class="col-start-2 col-end-6 font-bold self-center">
                {$t('tasks.taskSentenceSearchPopUp.sentence')}
            </div>
            <div class="col-start-6 col-end-10 font-bold self-center">
                {$t('tasks.taskSentenceSearchPopUp.translation')}
            </div>

            <div class="font-bold text-center self-center">
                {`${$t('tasks.taskSentenceSearchPopUp.audio')}, ${$t(
                    'tasks.taskSentenceSearchPopUp.translations'
                )}, ${$t('tasks.taskSentenceSearchPopUp.level')}`}
            </div>
        </div>
        <hr class="mt-2"/>
        <InfiniteScrollContainer loadMoreFunc={loadMore} mode="popUp">
            {#each sentences as sentence}
                {@const value = sentence.translations.find((tr) => tr.lang === $SentenceFilterState.lang)?.value}
                <PopupSentenceSearchItem
                        {sentence}
                        translation={value || ''}
                        {findSentence}
                        {deleteSentenceFromState}
                        {addSentenceToState}
                />
            {/each}
        </InfiniteScrollContainer>

    </div>
</div>

<script>
    import BaseInput from '../common/BaseInput.svelte';
    import {StudentFilterState} from '../../state/student-filter-state';
    import {t} from '$lib/i18n/config';
    import BaseSwitch from '$components/common/BaseSwitch.svelte';
    import _ from 'lodash';
    import {get} from 'svelte/store';
    import {IconEye, IconEyeOff} from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import OnlyForRole from "$components/common/OnlyForRole.svelte";

    let inputValue = '';

    const onInput = _.debounce(
        () => StudentFilterState.set({...get(StudentFilterState), search: inputValue}),
        1000
    );
</script>

<div class="flex flex-col min-h-[80px] px-1">
    <div class="flex flex-row gap-10 items-center w-full justify-between">
        <div class="flex gap-x-5">
            <div class="flex w-72">
                <BaseInput
                        name="search"
                        placeHolder={$t('students.students.filters.placeHolderSearch')}
                        on:input={onInput}
                        bind:value={inputValue}
                        title={$t('students.students.filters.search')}
                />
            </div>

            <div class="flex gap-10 items-baseline">
                <BaseSwitch
                        bind:checked={$StudentFilterState.onlyIsActive}
                        name="onlyIsActive"
                        title={$t('students.students.filters.isActive')}
                />
            </div>
        </div>
        <OnlyForRole>
            <div class="flex justify-end">
                <BaseButton
                        className="outline bg-transparent text-accent"
                        on:click={() => ($StudentFilterState.showFullTable = !$StudentFilterState.showFullTable)}
                >
                    {$t('students.students.filters.info')}
                    {#if $StudentFilterState.showFullTable}
                        <IconEyeOff/>
                    {:else}
                        <IconEye/>
                    {/if}
                </BaseButton>
            </div>
        </OnlyForRole>

    </div>
</div>

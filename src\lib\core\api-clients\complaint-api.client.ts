import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import {ComplaintFilterState} from "$lib/state/complaint-filter-state";
import {get} from 'svelte/store';
import {ComplaintsPagingState} from "$lib/state/complaints-paging-state";
import {ComplaintsStatisticFilter} from "$lib/state/complaints-statistic-state";


export class ComplaintApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public handledComplaint = async (taskId: string, sentenceId: string) =>
        await this.putDataOrThrow(`/api/complaints`, {taskId, sentenceId})

    public getComplaints = async () => {
        const {isHandled, latestUpdater} = get(ComplaintFilterState);
        const currentPagingState = get(ComplaintsPagingState);
        return await this.getDataOrThrow(`/api/complaints?isHandled=${isHandled}&take=${currentPagingState.take}&skip=${currentPagingState.skip}&latestUpdater=${latestUpdater}`)
    }

    public getUpdaters = async () => {
        return await this.getDataOrThrow(`/api/complaints/updaters`)
    }


    public getStatisticComplaints = async () => {
        const {latestUpdater, sortBy, sortDir} = get(ComplaintsStatisticFilter)
        return await this.getDataOrThrow(`/api/complaints/statistic?latestUpdater=${latestUpdater}&sortBy=${sortBy}&sortDir=${sortDir}`)
    }

    public getTheNumberComplaintsByUserId = async (userId: string) => {
        return await this.getDataOrThrow(`/api/complaints/userId?userId=${userId}`)
    }
}


<script lang="ts">
    import {createEventDispatcher} from 'svelte';
    import BaseButton from '../common/BaseButton.svelte';
    import {IconStatusChange, IconX} from '@tabler/icons-svelte';
    import {t} from '$lib/i18n/config';
    import type {ShortStudentRequestDto} from '$common/models/dtos/student-request.dto';
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";
    import {format} from "date-fns";

    export let requests: ShortStudentRequestDto[];

    const dispatchModal = createEventDispatcher();
    export let loadMoreFunc;

    $: tableTitles = [
        $t('students.requests.table.head.tz'),
        $t('students.requests.table.head.fullname'),
        $t('students.requests.table.head.email'),
        $t('students.requests.table.head.whatsapp'),
        $t('students.requests.table.head.groupStartDate'),
        $t('students.requests.table.head.learnStartDate'),
        $t('students.requests.table.head.lang'),
        $t('students.requests.table.head.level'),
        $t('students.requests.table.head.application'),
        $t('students.requests.table.head.action')
    ];


    const copyText = (text: string) => {
        navigator.clipboard.writeText(text);
    };
</script>

<div class="table-container">
    <!--{#if requests && requests?.count > 0}-->
    <table class="!overflow-x-auto !overflow-y-scroll table table-hover table-compact ">
        <thead on:keypress>
        <tr>
            {#each tableTitles as title}
                <th class="text-right">{title}</th>
            {/each}
        </tr>
        </thead>
        <tbody>
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each requests as row, rowIndex}
                <tr>
                    <td>{row.tz}</td>
                    <td>{`${row.firstname} ${row.lastname}`}</td>
                    <td
                            class="cursor-pointer"
                            on:click={(e) => {
							copyText(row.email);
						}}>{row.email.length > 15 ? `${row.email.slice(0, 15)}...` : row.email}</td
                    >
                    <td>{row.whatsapp}</td>
                    <td>
                        {format(new Date(row.groupStartDate), 'dd.MM.yyyy')}
                    </td>
                    <td>
                        {format(new Date(row.learnStartDate), 'dd.MM.yyyy')}
                    </td>
                    <td>{row.groupLang.toLowerCase() === 'english' ? 'EN' : 'RU'}</td>
                    <td>{row.groupLevel[0]}</td>
                    <td>
                        {format(new Date(row.createdAt), 'dd.MM.yyyy')}
                    </td>
                    <td class="flex gap-2">
                        <BaseButton size="sm" on:click={() => dispatchModal('triggerAcceptModal', row)}>
                            {$t('students.requests.table.handleButton')}
                            <IconStatusChange size={24} stroke={2}/>
                        </BaseButton>
                        <BaseButton className="!bg-error-600 " size="sm"
                                    on:click={()=> dispatchModal('deleteStudentRequest',row.id)}>
                            <IconX size={16} stroke={2}/>
                        </BaseButton>
                    </td>
                </tr>
            {/each}

        </InfiniteTableScrollContainer>
        </tbody>
    </table>
    <!--{:else}-->
    <!--    You are all set!-->
    <!--{/if}-->
</div>

<style>
    table td {
        vertical-align: middle;
    }
</style>

<script lang="ts">
    import {t} from '$lib/i18n/config.js';
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {IconCircleLetterX, IconDeviceFloppy} from '@tabler/icons-svelte';
    import BaseInput from '$components/common/BaseInput.svelte';
    import SveltyPicker from 'svelty-picker';
    import FormErrorMessage from '$components/common/FormErrorMessage.svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {StudentAcceptModalState} from '$lib/state/student-accept-state';
    import {onDestroy} from 'svelte';
    import PastStudentDataValue from '$components/students/PastStudentDataValue.svelte';
    import {getDateString} from '$lib/common/utils';
    import InfoBadge from "$components/common/InfoBadge.svelte";

    export let action: string;
    export let disableSave: boolean;


    export let studentId: string;
    export let handleSubmit: () => void;
    export let formData;

    let dob = getDateString(formData.dob);

    export let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;

    const modalStore = getModalStore();

    onDestroy(() => {
        $StudentAcceptModalState.studentPastData = null;
    });
</script>

<form
        action={`?/${action}`}
        on:submit|preventDefault={handleSubmit}
        class=" modal-form overflow-hidden"
>
    <div class="max-h-[450px] pl-5 overflow-hidden overflow-y-auto">
        <div class="p-4 sm:flex sm:flex-col sm:gap-7 lg:grid lg:grid-cols-3 lg:items-baseline  lg:gap-5">
            <label for="id" class="hidden">
                <BaseInput name="id" value={formData.id}/>
            </label>

            <label for="isExistingStudent" class="hidden">
                <BaseInput name="isExistingStudent" value={!!studentId}/>
            </label>
            <label for="tz" class="label">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.tz')}</span
                >
                <BaseInput name="tz" bind:value={formData.tz} dir="ltr" placeHolder="Enter tz..."/>
                <FormErrorMessage {modalErrors} fieldName="tz"/>
            </label>
            <label for="firstname" class="label">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.firstname')}</span
                >
                <BaseInput
                        name="firstname"
                        bind:value={formData.firstname}
                        placeHolder="Enter firstname..."
                />
                <PastStudentDataValue keyName="firstname"/>
                <FormErrorMessage {modalErrors} fieldName="firstname"/>
            </label>
            <label for="lastname" class="label">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.lastname')}</span
                >
                <BaseInput name="lastname" bind:value={formData.lastname} placeHolder="Enter lastname..."/>
                <PastStudentDataValue keyName="lastname"/>
                <FormErrorMessage {modalErrors} fieldName="lastname"/>
            </label>
            <label for="email" class="label">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.email')}</span
                >
                <BaseInput name="email" bind:value={formData.email} placeHolder="Enter email..."/>
                <PastStudentDataValue keyName="email"/>
                <FormErrorMessage {modalErrors} fieldName="email"/>
            </label>
            <label for="phone" class="label">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.phone')}</span
                >
                <BaseInput dir="ltr" name="phone" bind:value={formData.phone} placeHolder="Enter phone..."/>
                <PastStudentDataValue keyName="phone"/>
                <FormErrorMessage {modalErrors} fieldName="phone"/>
            </label>
            <div>
				<span class="block input__title font-medium text-base">
					{$t('students.students.modal.fields.dob')}
				</span>
                <SveltyPicker
                        name="dob"
                        mode="date"
                        clearToggle={false}
                        weekStart={0}
                        initialDate={new Date(formData.dob)}
                        format="yyyy-mm-dd"
                        bind:value={dob}
                        manualInput={false}
                        inputClasses="w-full input rounded h-10 p-2"
                />
                <PastStudentDataValue keyName="dob"/>
                <FormErrorMessage {modalErrors} fieldName="dob"/>
            </div>
            <label for="whatsapp" class="label">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.whatsapp')}</span
                >
                <BaseInput dir="ltr" name="whatsapp" bind:value={formData.whatsApp} placeHolder="Enter phone..."/>
                <PastStudentDataValue keyName="whatsapp"/>
                <FormErrorMessage {modalErrors} fieldName="whatsapp"/>
            </label>
            {#if formData.teudatOleUrl}
                <label for="teudatOleUrl" class="label flex flex-col h-full w-fit gap-2">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.teudatOle')}</span>
                    <BaseInput name="teudatOleUrl" value={formData.teudatOleUrl} className="hidden"/>
                    <div class="cursor-pointer">
                        <InfoBadge url={`${new URL(formData.teudatOleUrl).origin}${new URL(formData.teudatOleUrl).pathname}`} text="Teudat Ole"/>
                    </div>
                </label>
            {/if}
            {#if formData.photoUrl}
                <label for="photoUrl" class="label flex flex-col h-full w-fit gap-2">
				<span class="block input__title font-medium text-base"
                >{$t('students.students.modal.fields.photo')}</span>
                    <BaseInput name="photoUrl" value={formData.photoUrl} className="hidden"/>
                    <div class="cursor-pointer">
                        <InfoBadge url={`${new URL(formData.photoUrl).origin}${new URL(formData.photoUrl).pathname}`} text="Photo"/>
                    </div>
                </label>
            {/if}
        </div>
        <label for="comment" class="label px-4 mb-5">
			<span class="block input__title font-medium text-base"
            >{$t('students.students.modal.fields.comment')}</span
            >
            <BaseInput name="comment" bind:value={formData.comment} placeHolder="Enter comment..."/>
            <PastStudentDataValue keyName="comment"/>
            <FormErrorMessage {modalErrors} fieldName="comment"/>
        </label>
        {#if $StudentAcceptModalState.studentPastData}
            <span class="text-warning-500">{$t('students.students.modal.pastDataDescription')} </span>
        {/if}
        <slot name="groupSettingsForm"/>
        <slot name="statistics"/>
    </div>

    <div class="m-4 flex justify-between">
        <BaseButton disabled={disableSave} type="submit">
            <IconDeviceFloppy/> {$t('students.students.modal.submit')}</BaseButton
        >
        <BaseButton on:click={() => modalStore.close()}>
            <IconCircleLetterX/> {$t('students.students.modal.cancel')}</BaseButton
        >
    </div>
</form>

<script lang="ts">
    import {t} from '$lib/i18n/config';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import {DbFormatStringToDaysSchedule, getTimeFromDateString} from '$lib/common/utils';
    import {HoursScheduleFilter, LevelFilter} from '$common/models/enums';
    import InfoBadge from "$components/common/InfoBadge.svelte";
    import type {GroupScheduleDto} from "$common/models/dtos/group-schedule.dto";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {format} from "date-fns";

    export let group: GroupDto;

    export let groupStudyHours: number;
    export let groupStudyDays: number;
    export let groupRemainingDays: number;
    export let groupRemainingHours: number;

    export let groupScheduleChanges: GroupScheduleDto;

    export let endDate: string;

    export let percentage;
</script>





<div dir="auto" class="flex flex-col max-w-[570px] items-start !p-0">
    <div class="flex py-0.5 px-1 flex-wrap gap-2 justify-center" dir="rtl">
        {#if group.isPublic}
            <InfoBadge text={group.name}/>
            {#if group.comment}
                <InfoBadge text={group.comment}/>
            {/if}
            <InfoBadge text={group.lang}/>
            <InfoBadge text={group.isPublic
			? $t('groups.infoGroup.isPublic.public')
			: $t('groups.infoGroup.isPublic.nonpublic')} type={group.isPublic ? 'warning' : 'success'}/>
        {:else}
            <InfoBadge
                    text={`${format(new Date(groupScheduleChanges[0]?.dateStart),'dd.MM.yyyy')} ↔ ${format(new Date(endDate),'dd.MM.yyyy')}`}/>
            <InfoBadge text={HoursScheduleFilter[group.hoursSchedule]}/>
            <InfoBadge
                    text={DbFormatStringToDaysSchedule(groupScheduleChanges[groupScheduleChanges.length-1]?.daysSchedule)}/>
            <InfoBadge text={LevelFilter[group.level]}/>
            <InfoBadge text={group.lang}/>
            <InfoBadge url={group.whatsappUrl} text="whatsapp"/>
            {#if group.comment}
                <InfoBadge text={group.comment}/>
            {/if}

            <InfoBadge text={HoursScheduleFilter[group.hoursSchedule]}/>
            <InfoBadge
                    text={`${getTimeFromDateString(group?.timeStart)} ↔ ${getTimeFromDateString(group?.timeEnd)}`}/>
            <OnlyForRole>
                <InfoBadge
                        text={`${$t('groups.infoGroup.days')}: ${groupStudyDays}/${groupStudyDays + groupRemainingDays} (${groupRemainingDays})`}/>
                <InfoBadge
                        text={`${$t('groups.infoGroup.hours')}: ${groupStudyHours}/${groupStudyHours +groupRemainingHours} (${groupRemainingHours})`}/>
                <InfoBadge text={`${$t('groups.infoGroup.progress')} ${percentage}%`}/>
            </OnlyForRole>
            <InfoBadge text={group.isActive
			? $t('groups.infoGroup.isActive.active')
			: $t('groups.infoGroup.isActive.inActive')} type={group.isActive ? 'success' : 'error'}/>
            <InfoBadge text={group.isPublic
			? $t('groups.infoGroup.isPublic.public')
			: $t('groups.infoGroup.isPublic.nonpublic')} type={group.isPublic ? 'warning' : 'success'}/>
        {/if}
    </div>
</div>

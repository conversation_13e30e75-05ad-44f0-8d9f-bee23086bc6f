<script lang="ts">
    import {StudentEditModalState} from "$lib/state/student-edit-state";
    import {t} from '$lib/i18n/config';
    import {GroupHistoryState} from "$lib/state/group-history-state";
    import {onMount} from "svelte";
    import {calculateStudentStudyHoursAndDays} from "$lib/common/utils";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconEdit, IconX} from "@tabler/icons-svelte";


</script>


<div class="flex flex-col gap-2 mt-2">
    <h1 class="text-lg font-bold">{$t('students.students.modal.statistics')}</h1>
    <div class="flex flex-col gap-5 ">
        <div class="flex flex-col gap-1">
            <div class="flex items-center gap-1" dir="auto">
                <p class="text-lg">{$t('students.students.modal.totalHours')}:</p>
                <p class="font-bold">{$StudentEditModalState.totalHours}</p>
            </div>
            <div class="flex items-center gap-1" dir="auto">
                <p class="text-lg">{$t('students.students.modal.totalDays')}:</p>
                <p class="font-bold">{$StudentEditModalState.totalDays}</p>
            </div>
        </div>
    </div>

</div>

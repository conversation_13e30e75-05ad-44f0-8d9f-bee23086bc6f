<script lang="ts">
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {t} from '$lib/i18n/config';
    import type {EditableTaskDto, ExtendedTaskDto, TaskDto} from '$common/models/dtos/task.dto';
    import {goto} from '$app/navigation';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import {page} from '$app/stores';
    import type {SentenceInTaskDto} from '$common/models/dtos/sentence.dto';
    import TaskPresentationPopup from '$components/groups/TaskPresentationPopUp.svelte';
    import TaskTableRow from '$components/groups/TaskTableRow.svelte';
    import {generateGuid} from '$common/core/utils';
    import type {StudentDto} from "$common/models/dtos/student.dto";
    import TaskStudentsResultTable from "$components/groups/TaskStudentsResultTable.svelte";
    import PublicTaskAnonResultTable from "$components/groups/PublicTaskAnonResultTable.svelte";
    import {SendNotificationModalState} from "$lib/state/send-notification-state";
    import {get} from "svelte/store";
    import {format, isWithinInterval} from "date-fns";
    import {TaskFilter} from "$common/models/enums";
    import _ from 'lodash';
    import {LoadingState} from "$lib/state/loading-state";
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";

    export let tasks: ExtendedTaskDto[] = [];
    $: accordeonState = tasks && tasks?.map((x) => {
        return {[`${x.id}`]: false};
    });
    export let groups: GroupDto[] = [];

    export let students: StudentDto[] = [];

    export let loadMoreFunc;

    const modalStore = getModalStore();
    let taskLang: 'EN' | 'RU';
    let taskFont = false;
    let taskSentences: SentenceInTaskDto[];
    let isTaskPresentationPopupVisible = false;

    let isTaskPage = $page.data.pathname === '/tasks';


    $: tableTitles = [
        $t('tasks.table.head.buttons'),
        $t('tasks.table.head.date'),
        $t('tasks.table.head.type'),
        $t('tasks.table.head.group'),
        $t('tasks.table.head.comment'),
        $t('tasks.table.head.additionalTask'),
        $t('tasks.table.head.edit'),
    ];


    const goEditExistingTask = async (task: TaskDto) => {
        CurrentEditableState.set(task as EditableTaskDto);
        await goto(`/tasks/${task.id}`);
    };

    const copyLinkToClipBoard = async (id: string) => {
        const task = tasks.find(x => x.id === id);
        const pathToTask = task?.type === TaskFilter.sandbox ? '/pt/' : '/t/';
        const rootPath: string = $page.url.origin || '';
        const link = `${rootPath}${pathToTask}${id}`;
        await navigator.clipboard.writeText(link);
    };

    const toggleTaskPresentationPopupVisible = () => {
        isTaskPresentationPopupVisible = !isTaskPresentationPopupVisible;
    };

    const handleDispatchOpenPopUp = (e) => {
        taskFont = e.detail.taskFont;
        taskLang = e.detail.lang;
        taskSentences = e.detail.random ? _.shuffle(e.detail.sentences) : e.detail.sentences;
        toggleTaskPresentationPopupVisible();
    };

    const handleDispatchDuplicate = async (id: string) => {
        const rootPath: string = $page.url.origin || '';
        const link = `${rootPath}/tasks/${generateGuid()}?isn=true&duplicate=${id}`;
        await goto(link);
    };

    const openSendNotificationStudentModal = (
        type: 'student' | 'task',
        task: TaskDto,
        student: StudentDto
    ) => {
        const groupLang = groups.find((x) => x.id === task.groupId).lang === 'RU' ? 'ru' : 'en';

        if (type === 'student') {
            SendNotificationModalState.set({
                ...get(SendNotificationModalState),
                id: generateGuid(),
                type: 'student',
                title: `${student.firstname} ${student.lastname}`,
                recipientId: student.id,
                groupLang: groupLang
            });
        } else {
            const nameTask =
                task.date &&
                `${
                    task.type === 'class'
                        ? $t(`t.classTaskWithTranslate.${groupLang}`)
                        : $t(`t.homeTaskWithTranslate.${groupLang}`)
                } ${format(new Date(task.date), 'P')}`;
            SendNotificationModalState.set({
                ...get(SendNotificationModalState),
                id: generateGuid(),
                type: 'student',
                title: `${student.firstname} ${student.lastname} ${$t(`campaigns.modal.assignment.${groupLang}`)}  ${nameTask?.toLowerCase()}`,
                recipientId: student.id
            });
        }
        modalStore.trigger({type: 'component', component: 'sendNotificationStudentModal'});
    };


    const findStudentsRelatedToTask = (students, task) => {
        return students.filter(swr => {
            return swr?.studentLearningHistory?.some(slh => {
                if (slh.groupId !== task?.groupId) return false;
                const slhStartDate = new Date(slh.from);
                const slhEndDate = slh.to ? new Date(slh.to) : new Date();
                const taskDate = task?.date ? new Date(task.date) : null;


                return slhStartDate < slhEndDate && taskDate && isWithinInterval(taskDate, {
                    start: slhStartDate,
                    end: slhEndDate
                });
            });
        });

    }


</script>

{#if isTaskPresentationPopupVisible}
    <div class=" fixed top-0 left-0 h-[100vh] w-[100vw] z-20">
        <TaskPresentationPopup {toggleTaskPresentationPopupVisible} {taskFont} {taskSentences} {taskLang}/>
    </div>
{/if}


<div class="h-full {$LoadingState ? 'animate-pulse cursor-loading' : ''}">
    <table class="table table-hover table-compact h-fit overflow-auto">
        <thead on:keypress>
        <tr class="sticky">
            {#each isTaskPage ? tableTitles : [...tableTitles, $t('tasks.table.head.res')] as title}
                <th class="text-right">{title}</th>
            {/each}
        </tr>
        </thead>
        <tbody class="!flex-1 overflow-auto">
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each tasks as row, rowIndex (rowIndex)}
                {@const groupName = groups?.find((x) => x.id === row.groupId)?.name}
                {@const isPublic = groups?.find((x) => x.id === row.groupId)?.isPublic}
                {@const studentsCount =findStudentsRelatedToTask(students, row)?.length }
                <TaskTableRow {row} {groupName} {isPublic} studentsCount={studentsCount}
                              on:goEditTask={() => goEditExistingTask(row)}
                              on:openPopUp={handleDispatchOpenPopUp}
                              on:copy={() => copyLinkToClipBoard(row.id)}
                              on:duplicate={() => handleDispatchDuplicate(row.id)}
                              on:changeAccordeonState={() => (accordeonState[row.id] = !accordeonState[row.id])}
                              {isTaskPage}
                />
                {#if accordeonState[row.id]}
                    {#if isPublic}
                        <PublicTaskAnonResultTable task={row}/>
                    {:else}
                        <TaskStudentsResultTable {students} task={row}
                                                 findStudentsRelatedToTask={findStudentsRelatedToTask}
                                                 on:openSendNotificationStudentModal={(e) => openSendNotificationStudentModal('task', row, e.detail.student)}/>
                    {/if}
                {/if}
            {/each}
        </InfiniteTableScrollContainer>
        </tbody>
    </table>
</div>


<style>
    table tbody {
        vertical-align: middle;
    }
</style>

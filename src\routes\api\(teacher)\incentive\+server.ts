import type {RequestEvent} from '@sveltejs/kit';
import {wrapFunc} from '$api/core/misc/response-wrapper';
import {
    createIncentiveContent,
    deleteIncentiveContentById,
    getAllIncentiveContent,
    getIncentiveContentById,
    updateIncentiveContentById
} from '$api/core/services/incentives.service';
import {paramsToKeyValue} from '$api/core/utils';
import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';

export const POST = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const incentiveContent: IncentiveContentDto = await event.request.json();
        return createIncentiveContent(incentiveContent);
    });

export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {search, id, take, skip} = paramsToKeyValue(url.searchParams);
        if (id) {
            return getIncentiveContentById(id);
        }
        return getAllIncentiveContent({
            search,
            take: +take,
            skip: +skip
        });
    });

export const PUT = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const incentieContent = await event.request.json();

        return updateIncentiveContentById(incentieContent.id, incentieContent);
    });

export const DELETE = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {id} = paramsToKeyValue(url.searchParams);

        return deleteIncentiveContentById(id);
    });

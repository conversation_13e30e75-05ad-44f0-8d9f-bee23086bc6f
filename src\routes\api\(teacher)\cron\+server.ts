import { updateCurrentGroupForStudent } from '$api/core/services/cron.service';
import { wrapFunc } from '$api/core/misc/response-wrapper';
import { json } from '@sveltejs/kit';

export const GET = async () =>
	wrapFunc(async () => {
		const updatedStudents = await updateCurrentGroupForStudent();
		console.info(updatedStudents.success ? `Updated successfully` : 'Not updated');
		return json({ success: true });
	});

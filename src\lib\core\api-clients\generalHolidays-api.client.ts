import {BaseApiClient} from "$lib/core/api-clients/base-api-client";
import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";

export class GeneralHolidaysApiClient extends BaseApiClient {
    constructor() {
        super();
    }


    public postGeneralHoliday = async (generalHoliday: GeneralHolidayDto) => {
        return await this.postDataOrThrow('/api/generalHolidays', generalHoliday)
    }


    public getGeneralHolidays = async () => {
        return await this.getDataOrThrow('/api/generalHolidays')
    }

    public deleteGeneralHoliday = async (id: string) => {
        return await this.deleteOrThrow(`/api/generalHolidays?id=${id}`)
    }
}
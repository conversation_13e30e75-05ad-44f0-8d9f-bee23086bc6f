<script lang="ts">
    import {IconPlus, IconMinus} from '@tabler/icons-svelte';
    import axios from "axios";

    export let settings: { id: number, timeOffset: number };

    const changeOffset = () => {
        const data = axios.put('/api/settings', settings)
    }

    const addOffset = () => {
        if (settings.timeOffset < 3) {
            settings.timeOffset += 1;
            changeOffset()
        }
    }

    const decreaseOffset = () => {
        if (settings.timeOffset > 0) {
            settings.timeOffset -= 1;
            changeOffset()
        }
    }

</script>


<div class="p-4">
    <div class="flex flex-col gap-2">
        Time offset
        <div class="flex gap-2 items-center">
            <span on:click={()=>addOffset()}>
                <IconPlus class="cursor-pointer"/>
            </span>
            <span class="text-lg font-bold">
                {settings?.timeOffset}
            </span>
            <span on:click={()=>decreaseOffset()}>
                <IconMinus class="cursor-pointer"/>
            </span>
        </div>
    </div>

</div>
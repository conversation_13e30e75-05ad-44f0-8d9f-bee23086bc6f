import type {Template} from '@pdfme/common';
import {BLANK_PDF} from '@pdfme/common';
import {generate} from '@pdfme/generator';
import type {EditableTaskDto} from '$common/models/dtos/task.dto';
import type {SentenceInTaskDto} from '$common/models/dtos/sentence.dto';
import {format} from "date-fns";

const Logo = 'data:image/jpeg;base64,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'
const createHebrewTextForPdf = (sentences: SentenceInTaskDto[], pageNumber: number) => {
    let result = '';
    sentences.forEach((sentence, index) => {
        const sentenceNumber = index + 1 + (pageNumber - 1) * 22;
        result += `${sentenceNumber}. ${sentence.value}\n`;
    });
    return result;
};


const createTranslationsForPdf = (sentences: SentenceInTaskDto[], lang: string, pageNumber: number) => {
    let result = '';
    sentences.forEach((sentence, index) => {
        sentence.translations.forEach((trans) => {
            if (trans.lang === lang) {
                const sentenceNumber = index + 1 + (pageNumber - 1) * 22;
                result += `${sentenceNumber}. ${trans.value}\n`;
            }
        });
    });
    return result;
};


const createInputs = (sentences: SentenceInTaskDto[], lang: string, objForInputs: {
    logo: string,
    email: string,
    date: string,
    title: string,
    phone: string
}) => {
    const countOfPages = Math.ceil(sentences.length / 22);
    const sentencesPages: {
        logo: string,
        email: string,
        date: string,
        title: string,
        phone: string
        textTasks: string
    }[] = [];
    const translationsPages: {
        logo: string,
        email: string,
        date: string,
        title: string,
        phone: string
        textHebrew: string
    }[] = [];


    for (let i = 0; i < countOfPages; i++) {
        const startIndexForSentencesPage = i * 22;
        const endIndexForSentencesPage = Math.min(startIndexForSentencesPage + 22, sentences.length);


        const pageNumber = i + 1;

        sentencesPages.push({
            ...objForInputs,
            textTasks: createTranslationsForPdf(sentences.slice(startIndexForSentencesPage, endIndexForSentencesPage), lang, pageNumber)
        });

        translationsPages.push({
            ...objForInputs,
            textHebrew: createHebrewTextForPdf(sentences.slice(startIndexForSentencesPage, endIndexForSentencesPage), pageNumber)
        });
    }


    return [...sentencesPages, ...translationsPages];
}

function reverseNonHebrewWords(text: string) {
    const lines = text.split('\n');

    const reverseWord = (word: string) => {
        return word.split('').reverse().join('');
    };

    const reversedLines = lines.map(line => {
        const words = line.split(/\s+/);
        const reversedWords = words.map(word => {
            if (!/[\u0590-\u05FF]/.test(word)) {
                return reverseWord(word);
            } else {
                return word;
            }
        });
        return reversedWords.join(' ');
    });

    return reversedLines.join('\n');
}


export const downloadPDF = async (
    CurrentEditableState: EditableTaskDto,
    translationFn: (textForTranslation: string) => string
) => {
    const template: Template = {
        basePdf: BLANK_PDF,
        schemas: [
            {
                logo: {
                    type: 'image',
                    position: {
                        x: 11.23,
                        y: 9.25
                    },
                    width: 30.0,
                    height: 30.00
                },
                textTasks: {
                    type: 'text',
                    position: {
                        x: 11.23,
                        y: 75.0
                    },
                    width: 185.02,
                    height: 238.91,
                    alignment: 'left',
                    fontSize: 14,
                    characterSpacing: 0,
                    lineHeight: 1.7
                },
                email: {
                    type: 'text',
                    position: {
                        x: 134.19,
                        y: 10.57
                    },
                    width: 60.72,
                    height: 9,
                    alignment: 'right',
                    fontSize: 13,
                    characterSpacing: 0,
                    lineHeight: 2
                },
                date: {
                    type: 'text',
                    position: {
                        x: 160,
                        y: 34.66
                    },
                    width: 35,
                    height: 7,
                    alignment: 'right',
                    fontSize: 13,
                    characterSpacing: 0,
                    lineHeight: 1
                },
                phone: {
                    type: 'text',
                    position: {
                        x: 160,
                        y: 24.34
                    },
                    width: 35,
                    height: 7,
                    alignment: 'right',
                    fontSize: 13,
                    characterSpacing: 0,
                    lineHeight: 1
                },
                title: {
                    type: 'text',
                    position: {
                        x: 32.28,
                        y: 55.0
                    },
                    width: 141.05,
                    height: 14.67,
                    alignment: 'center',
                    fontSize: 20,
                    characterSpacing: 0,
                    lineHeight: 1
                },
                textHebrew: {
                    type: 'text',
                    position: {
                        x: 11.23,
                        y: 75.0
                    },
                    width: 185.02,
                    height: 238.91,
                    alignment: 'right',
                    fontSize: 14,
                    characterSpacing: 0,
                    lineHeight: 1.7,
                    fontName: !CurrentEditableState.hebrewFont ? 'gveret' : ''
                }
            }
        ]
    };
    const objForInputs = {
        logo: Logo,
        email: "<EMAIL>",
        date: format(new Date(),'dd.MM.yyyy'),
        title: `${translationFn(
            `tasks.pdf.${CurrentEditableState.lang}.${CurrentEditableState.type}`
        )} ${format(new Date(CurrentEditableState.date),'dd.MM.yyyy')}`,
        phone: "+972535282955"
    };


    let inputs = createInputs(CurrentEditableState.sentences, CurrentEditableState.lang, objForInputs);


    inputs = inputs.map((el) => {
        if (el?.textHebrew) {
            let text = el?.textHebrew?.replaceAll(/(\u05c1)/g, '');
            text = reverseNonHebrewWords(text).replaceAll(' ', '  ');
            return {...el, textHebrew: text}
        }
        return el
    })


    const font = {
        sans: {
            data: await fetch('/fonts/OpenSans-Regular.ttf').then((res) => res.arrayBuffer()),
            fallback: true,

        },
        gveret: {
            data: await fetch('/fonts/testfont.ttf').then((res) => res.arrayBuffer()),
        },
    }


    await generate({
        template,
        inputs,
        options: {
            font
        }
    }).then((pdf) => {
        const blob = new Blob([pdf.buffer], {type: 'application/pdf'});
        const blobUrl = URL.createObjectURL(blob);
        const downloadLink = document.createElement('a');
        downloadLink.href = blobUrl;
        downloadLink.download = `${CurrentEditableState.type}work_${CurrentEditableState.date.substring(0, 10)}`;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        URL.revokeObjectURL(blobUrl);
        document.body.removeChild(downloadLink);
    });
};

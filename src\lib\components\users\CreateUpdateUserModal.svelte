<script lang="ts">
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import {t} from '$lib/i18n/config';
    import BaseInput from '../common/BaseInput.svelte';
    import BaseButton from '../common/BaseButton.svelte';
    import {UserEditModalState} from '$lib/state/user-edit-state';
    import {loadingWrap, mapEnumToOptionsWithTranslations} from '$lib/common/utils';
    import {IconCircleLetterX, IconDeviceFloppy} from '@tabler/icons-svelte';
    import BaseSelect from '$components/common/BaseSelect.svelte';
    import {NotificationType, UserRole} from '$common/models/enums';
    import FormErrorMessage from '$components/common/FormErrorMessage.svelte';
    import NotificationStore from "$lib/state/notification-state";

    export let action: 'update' | 'create' = $UserEditModalState.action;
    const modalStore = getModalStore();

    let titles = {
        create: $t('users.modalUsers.titles.titleToCreate'),
        update: $t('users.modalUsers.titles.titleToUpdate')
    };
    let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;

    const formData = $UserEditModalState;


    async function handleSubmit(event) {
        const data = new FormData(this);

        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            modalErrors = result.data.errors;
        } else {
            await loadingWrap(async () => {
                await invalidate('load:users');
            });
            modalStore.close();
            if (formData.action === 'update') {
                NotificationStore.push(
                    {
                        type: NotificationType.success,
                        message: t.get('users.modalUsers.notifications.update.success')
                    }, 5);
            } else {
                NotificationStore.push(
                    {
                        type: NotificationType.success,
                        message: t.get('users.modalUsers.notifications.create.success')
                    }, 5);
            }
        }
    }
</script>

{#if $modalStore[0]}
    <div class="modal card p-4 w-1/2 shadow-xl space-y-2">
        <h1 class="text-2xl font-bold m-3">{titles[action]}</h1>
        <form action={`?/${action}`} on:submit|preventDefault={handleSubmit} class="modal-form">
            <div
                    class="p-2 sm:overflow-y-scroll sm:flex sm:flex-col sm:gap-5 gap-x-5 lg:grid lg:grid-cols-2 lg:items-center lg:overflow-y-auto"
            >
                <label for="id" class="hidden">
                    <BaseInput name="id" value={formData.id}/>
                </label>

                <label for="firstname" class="label">
                    <span>{$t('users.modalUsers.formFields.firstname')}</span>
                    <BaseInput
                            name="firstname"
                            bind:value={formData.firstname}
                            placeHolder="Enter firstname..."
                    />
                    <FormErrorMessage {modalErrors} fieldName="firstname"/>
                </label>
                <label for="lastname" class="label">
                    <span>{$t('users.modalUsers.formFields.lastname')}</span>
                    <BaseInput
                            name="lastname"
                            bind:value={formData.lastname}
                            placeHolder="Enter lastname..."
                    />
                    <FormErrorMessage {modalErrors} fieldName="lastname"/>
                </label>
                <label for="email" class="label">
                    <span>{$t('users.modalUsers.formFields.email')}</span>
                    <BaseInput className="input-ltr" dir="ltr" name="email" bind:value={formData.email}
                               placeHolder="...Enter email"/>
                    <FormErrorMessage {modalErrors} fieldName="email"/>
                </label>
                <label for="phone" class="label">
                    <span>{$t('users.modalUsers.formFields.phone')}</span>
                    <BaseInput name="phone" bind:value={formData.phone} placeHolder="Enter phone..."/>

                    <FormErrorMessage {modalErrors} fieldName="phone"/>
                </label>
                <div class="col-start-1 col-end-3 flex sm:gap-1 sm:flex-col lg:flex-row lg:gap-5">
                    <label for="role" class="label flex-1">
                        <span>{$t('users.modalUsers.formFields.role')}</span>
                        <BaseSelect
                                name="role"
                                bind:value={formData.role}
                                options={mapEnumToOptionsWithTranslations(
								UserRole,
								'users.modalUsers.selectFields.role',
								$t
							)}
                        />

                        <FormErrorMessage {modalErrors} fieldName="role"/>
                    </label>
                    <label for="tz" class="label flex-1">
                        <span>{$t('users.modalUsers.formFields.tz')}</span>
                        <BaseInput name="tz" bind:value={formData.tz} dir="ltr" placeHolder="Enter tz..."/>
                        <FormErrorMessage {modalErrors} fieldName="tz"/>
                    </label>
                    <label for="password" class="label flex-1">
                        <span>{$t('users.modalUsers.formFields.password')}</span>
                        <BaseInput
                                name="password"
                                bind:value={formData.password}
                                placeHolder="Enter password..."
                        />
                        <FormErrorMessage {modalErrors} fieldName="password"/>
                    </label>
                </div>
                {#if formData.role === 'teacher'}
                    <h1 class="text-2xl font-bold mr-1">{$t('users.modalUsers.titles.titlePermissions')}</h1>
                    <div class="col-start-1 col-end-3 flex sm:gap-1 sm:flex-col lg:flex-row lg:gap-5 ">
                        <label dir="auto" class="flex-1 flex gap-2 items-center justify-end">
                            <input name="permissions.editFavorites" dir="auto" class="checkbox" type="checkbox"
                                   bind:checked={formData.permissions.editFavorites}/>
                            <p class="">{$t('users.modalUsers.formFields.permissions.editFavorites')}</p>
                        </label>
                        <label dir="auto" class="flex-1 flex gap-2 items-center justify-end">
                            <input name="permissions.deleteNonFavSentences" class="checkbox" type="checkbox"
                                   bind:checked={formData.permissions.deleteNonFavSentences}/>
                            <p>{$t('users.modalUsers.formFields.permissions.deleteNonFavSentences')}</p>
                        </label>
                        <div class="flex-1">

                        </div>
                    </div>
                {/if}


            </div>


            <div class="p-2 mt-5 flex justify-between">
                <BaseButton type="submit">
                    <IconDeviceFloppy/> {$t(`users.modalUsers.submitButton.${action}`)}
                </BaseButton>
                <BaseButton on:click={() => modalStore.close()}>
                    <IconCircleLetterX/>
                    Cancel
                </BaseButton>
            </div>
        </form>
    </div>
{/if}




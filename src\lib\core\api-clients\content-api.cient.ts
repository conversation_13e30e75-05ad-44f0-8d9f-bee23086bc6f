import { BaseApiClient } from '$lib/core/api-clients/base-api-client';
import axios from 'axios';

export class ContentApiClient extends BaseApiClient {
	constructor() {
		super();
	}


	public async uploadAudio(formData: FormData): Promise<string> {
		const response = await axios.post('/api/sentences/audio', formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		});

		const {data} = response.data;


		return data;
	}

	public async uploadFile(formData: FormData) {
		const response = await axios.post('/api/tasks/content', formData, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		});

		const { data } = response.data;

		return data;
	}
}

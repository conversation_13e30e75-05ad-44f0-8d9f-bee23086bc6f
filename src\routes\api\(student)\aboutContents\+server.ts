import {wrapFunc} from "$api/core/misc/response-wrapper";
import {getAboutContents, updateAboutContent} from "$api/core/services/aboutContents.service";
import type {AboutContentsDto} from "$common/models/dtos/AboutContents.dto";
import type {RequestEvent} from "@sveltejs/kit";


export const GET = async () =>
    wrapFunc(async () => {
        return await getAboutContents()
    });



export const PUT = async (event: RequestEvent) =>
    wrapFunc(async () => {
        const content:Omit<AboutContentsDto,'lang'> = await event.request.json();

        return await updateAboutContent(content)
    });

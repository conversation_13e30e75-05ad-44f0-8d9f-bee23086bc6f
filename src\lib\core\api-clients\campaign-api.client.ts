import {BaseApiClient} from '$lib/core/api-clients/base-api-client';
import type {
    CampaignByRecipientIdDto,
    CampaignDto,
    NotificationByStudentIdDto,
    ShortCampaignDto
} from '$common/models/dtos/notification.dto';
import {get} from 'svelte/store';
import {CampaignFilterState} from "$lib/state/campaign-filter-state";
import {CampaignPagingState} from "$lib/state/campaign-paiging-state";

export class CampaignApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public createCampaign = async (campaign: CampaignDto): Promise<CampaignDto> =>
        await this.postDataOrThrow<CampaignDto>('/api/campaigns', campaign);

    public getAllCampaigns = async (): Promise<ShortCampaignDto[]> => {
        const {authorId, type, searchByRecipient} = get(CampaignFilterState);
        const {take, skip} = get(CampaignPagingState);
        return await this.getDataOrThrow(`/api/campaigns?authorId=${authorId}&type=${type}&searchByRecipient=${searchByRecipient}&take=${take}&skip=${skip}`);
    }

    public deleteCampaign = async (id:string) =>{
        return await this.deleteOrThrow(`/api/campaigns?id=${id}`)
    }


    public getAllCampaignsByRecipientId = async (id: string): Promise<CampaignByRecipientIdDto[]> =>
        await this.getDataOrThrow(`/api/campaigns/recipient?id=${id}`);

    public getAllNotificationsByStudentId = async (): Promise<NotificationByStudentIdDto[]> =>
        await this.getDataOrThrow('/api/notifications');

    public getAllUnreadNotificationsByStudentId = async (): Promise<NotificationByStudentIdDto[]> =>
        await this.getDataOrThrow('/api/notifications/unread');

    public markNotificationAsReadById = async (id: string): Promise<NotificationByStudentIdDto> =>
        await this.postDataOrThrow('/api/notifications/read', {id});
}

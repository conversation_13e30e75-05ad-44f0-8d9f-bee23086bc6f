<script lang="ts">
    import type {GroupDto} from '$common/models/dtos/group.dto';
    import SortingTableHeader from '$components/common/SortingTableHeader.svelte';
    import {GroupSortingState} from '$lib/state/group-paging-state';
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";
    import type {GeneralHolidayDto} from "$common/models/dtos/general-holiday.dto";
    import GroupsTableRow from "$components/groups/GroupsTableRow.svelte";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {t} from '$lib/i18n/config';


    export let loadMoreFunc;

    export let generalHolidays: GeneralHolidayDto[];

    export let groups: GroupDto[];

    export let openSendNotificationGroupModal;


    let tableHeaders = [
        {title: 'groups.table.head.name', sortBy: 'name'},
        {title: 'groups.table.head.dateStart', sortBy: 'dateStart'},
        {title: 'groups.table.head.language', sortBy: 'lang'},
        {title: 'groups.table.head.level', sortBy: 'level'},
        {title: 'groups.table.head.days', sortBy: 'daysSchedule'},
        {title: 'groups.table.head.hoursSchedule', sortBy: 'hoursSchedule'},
        {title: 'groups.table.head.whatsapp'},
        {title: 'groups.table.head.comment', sortBy: 'comment'},
        {title: 'groups.table.head.isActive', sortBy: 'isActive'},
        {title: 'groups.table.head.studentsCount'},
        {title: 'groups.table.head.statistics'},
        {title: 'Actions'}
    ];


</script>

<div class="h-full">
    <table class="table table-hover !overflow-auto table-compact ">
        <thead>
        <tr>
            {#each tableHeaders as header}
                {#if header.title === 'groups.table.head.statistics'}
                    <OnlyForRole>
                        <th class="flex justify-center">
                            <SortingTableHeader
                                    header={header}
                                    state={GroupSortingState}
                            />
                        </th>
                    </OnlyForRole>
                {:else}
                    <th class="text-center">
                        <SortingTableHeader
                                header={header}
                                state={GroupSortingState}
                        />
                    </th>
                {/if}
            {/each}
        </tr>
        </thead>
        <tbody>
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each groups as row, rowIndex (row.id)}
                <GroupsTableRow {row} {generalHolidays} on:triggerSendMessageModal={openSendNotificationGroupModal}/>
            {/each}

        </InfiniteTableScrollContainer>
        </tbody>
    </table>
</div>

<style>
    table td {
        vertical-align: middle;
    }
</style>

<script lang="ts">
    import {t} from "$lib/i18n/config";
    import {Tab, TabGroup} from "@skeletonlabs/skeleton";
    import {SettingsTabState} from "$lib/state/settings-tab-state";
    import Exclusions from "$components/settings/Exclusions.svelte";
    import AboutContent from "$components/settings/AboutContent.svelte";
    import HolidaysTab from "$components/settings/HolidaysTab.svelte";
    import SettingsTab from "$components/settings/SettingsTab.svelte";

    export let data;

    $:aboutContents = data.aboutContents;
    $:exclusions = data.exclusions.data;
    $:exclusionsCount = data.exclusions.count
    $:settings = data?.settings;


</script>


<div class="flex flex-col m-6">
    <div class="flex flex-col mt-3">
        <div>
            <TabGroup>
                <Tab on:click={null} bind:group={$SettingsTabState} name="requests-tab" value={0}>
                    <b
                    >
                        {$t('settings.tabs.exclusions')}
                        ({exclusionsCount})
                    </b>
                </Tab>
                <Tab on:click={null} bind:group={$SettingsTabState} name="requests-tab" value={1}>
                    <b
                    >
                        {$t('settings.tabs.about')}
                    </b>
                </Tab>
                <Tab on:click={null} bind:group={$SettingsTabState} name="requests-tab" value={2}>
                    <b
                    >
                        {$t('settings.tabs.holidays')}
                        ({data.generalHolidays.count})
                    </b>
                </Tab>
                <Tab on:click={null} bind:group={$SettingsTabState} name="requests-tab" value={3}>
                    <b
                    >
                        {$t('settings.tabs.settings')}
                    </b>
                </Tab>

                <svelte:fragment slot="panel">
                    {#if $SettingsTabState === 0}
                        <Exclusions exclusions={exclusions}/>
                    {:else if $SettingsTabState === 1}
                        <AboutContent aboutContents={aboutContents}/>
                    {:else if $SettingsTabState === 2}
                        <HolidaysTab generalHolidays={data.generalHolidays.data}/>
                    {:else if $SettingsTabState === 3}
                        <SettingsTab {settings}/>
                    {:else}
                        Loading...
                    {/if}
                </svelte:fragment>
            </TabGroup>
        </div>
    </div>
</div>
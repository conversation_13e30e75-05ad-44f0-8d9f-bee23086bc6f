<script lang="ts">

    import ComplaintsStatisticTable from "$components/complaints/ComplaintsStatisticTable.svelte";
    import ComplaintsStatisticFilter from "$components/complaints/ComplaintsStatisticFilter.svelte";
    import {t} from "$lib/i18n/config";


    export let statisticComplaints;
    export let updaters;

</script>


<div class="h-full mt-3">
    <h1 class="title mb-1 font-medium text-xl">
        {$t('complaints.statistic.title')}
    </h1>

    <ComplaintsStatisticFilter {updaters}/>

    <div class="mt-3  overflow-y-auto h-[62vh]">
        <ComplaintsStatisticTable {statisticComplaints}/>
    </div>
</div>
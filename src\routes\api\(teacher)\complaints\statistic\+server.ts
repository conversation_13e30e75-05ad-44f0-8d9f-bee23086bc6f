import {wrapFunc} from "$api/core/misc/response-wrapper";
import {getStatisticsComplaints} from "$api/core/services/complaint.service";
import type {RequestEvent} from "@sveltejs/kit";
import {paramsToKeyValue} from "$api/core/utils";
import type {ComplaintsStatisticFilterField} from "$common/models/enums";


export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {latestUpdater, sortBy, sortDir} = paramsToKeyValue(
            url.searchParams
        );
        return await getStatisticsComplaints({
            latestUpdater,
            sortBy: sortBy as ComplaintsStatisticFilterField,
            sortDir
        });
    });
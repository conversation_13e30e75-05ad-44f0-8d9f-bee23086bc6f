import { GroupApiClient } from '$lib/core/api-clients/group-api-client';
import type { PageLoad } from '../../../../.svelte-kit/types/src/routes/(teacher)/groups/$types';
import {GeneralHolidaysApiClient} from "$lib/core/api-clients/generalHolidays-api.client";

export const ssr = false;
export const load: PageLoad = async ({ depends }) => {
	try {
		depends('load:groups');
		return {
			groups:new GroupApiClient().getGroups(),
			generalHolidays:new GeneralHolidaysApiClient().getGeneralHolidays()
		}
	} catch (error) {
		return error;
	}
};

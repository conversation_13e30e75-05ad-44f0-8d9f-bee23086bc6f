<script lang="ts">
    export let value: number
    export let name:string;
</script>

<input
        bind:value
        min={0}
        max={100}
        type="number"
        {name}
        class="w-full input rounded p-2 remove-arrow"
/>


<style lang="scss">
  .remove-arrow::-webkit-inner-spin-button,
  .remove-arrow::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .remove-arrow {
    -moz-appearance: textfield;
  }
</style>
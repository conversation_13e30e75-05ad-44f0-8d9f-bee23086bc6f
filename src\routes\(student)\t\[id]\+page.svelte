<script lang="ts">
    import AdditionalTasksNavigation from '$components/t/AdditionalTasksNavigation.svelte';
    import ResultBlock from '$components/t/ResultBlock.svelte';
    import {TaskApiClient} from '$lib/core/api-clients/task-api-client';
    import {getCertainCompletionState} from '$lib/state/task-current-completion.state';
    import _ from 'lodash';
    import {page} from '$app/stores';
    import type {CompletionTaskDto, CompletionTaskStateModel} from '$common/models/dtos/task.dto';
    import TranslationMode from '$components/t/task-modes/TranslationMode.svelte';
    import {TaskMode} from '$common/models/enums';
    import ListeningMode from '$components/t/task-modes/ListeningMode.svelte';
    import {get} from 'svelte/store';
    import {generateGuid} from '$common/core/utils';
    import TranslationByTimeMode from '$components/t/task-modes/TranslationByTimeMode.svelte';
    import PhantomDictantMode from '$components/t/task-modes/PhantomDictantMode.svelte';
    import VoiceMode from '$components/t/task-modes/VoiceMode.svelte';
    import {mergeDateTime} from '$lib/common/utils';
    import {getAveragePercentResult, getTotalAbsoluteResult, switchMode, isInitialDate} from '$lib/common/task-helpers';
    import {initialDate} from '$lib/state/task-current-completion.state';
    import {t} from '$lib/i18n/config';
    import ContentEditor from "$components/content/ContentEditor.svelte";
    import {HebrewFontState} from "$lib/state/hebrew-font-state";
    import {animateScroll} from 'svelte-scrollto-element';
    import {onMount} from "svelte";
    import {ExclusionsState} from "$lib/state/exclusions-state";
    import {downloadPDF} from "$lib/common/pdf-helpers";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {IconPrinter} from "@tabler/icons-svelte";
    import {format} from "date-fns";
    import {CurrentEditableState} from "$lib/state/task-current-editable-state";
    import LexicalContent from "$components/common/LexicalContent.svelte";

    export let data;


    $:exclusions = data.exclusions.data;

	onMount(() => {
		if (exclusions && exclusions.length > 0) {
			$ExclusionsState = [...exclusions];
		}
	});


	const customTaskMode = $page.url.searchParams.get('tm');
	const usePersistentState = !customTaskMode;

	let top;

	let currentState = getCertainCompletionState($page.params.id, usePersistentState);

	let incentiveToShow;

	const isInitialIntermediate = (state = $currentState) => !customTaskMode && !state.currentResult && (state.task.navigationInPublicTaskEnabled && !state.task.navigationInPublicTaskOnlyAfter60);

	$: data && initAndRun(data);


	const downloadTaskPDF = async () => {
		await downloadPDF({
			...data.task
		}, $t);
	};

	const initAndRun = (data) => {
		let rerunAttempted = false;

		const execute = () => {
			try {
				// Check if there's an interrupted task in localStorage (currentState)
				const hasInterruptedTask = $currentState.currentResult &&
					$currentState.task.id &&
					isInitialDate($currentState.currentResult.finishedAt, initialDate);

				let updatedState;

				if (hasInterruptedTask) {
					// Priority 1: Use interrupted task from localStorage
					updatedState = {
						...$currentState,
						task: data.task, // Update task data but keep the interrupted state
						results: data.results?.results || $currentState.results
					} as CompletionTaskStateModel;
				} else {
					// Priority 2: Load from database results or start fresh
					updatedState = {
						...$currentState,
						id: data.results?.id ?? generateGuid(),
						task: data.task,
						results: data.results?.results
					} as CompletionTaskStateModel;

					// Determine the target mode (teacher's selection or default)
					const targetMode = customTaskMode ? customTaskMode as TaskMode : data.task.startingMode ?? TaskMode.translation;

					// Use switchMode to properly handle existing results or create new ones
					updatedState = switchMode(targetMode, updatedState);
				}

				HebrewFontState.set(data.task.hebrewFont ?? false);

				// For initial intermediate state, preserve navigation logic
				if (!isInitialIntermediate(updatedState) || !updatedState.currentResult) {
					// Only set intermediateState to false if we're truly starting fresh (no previous results)
					if (updatedState.results?.length === 0) {
						updatedState.intermediateState = false;
					}
				}

				$currentState = { ...updatedState };

				incentiveToShow = _.maxBy($currentState.task.task_incentive_content?.filter(x => x.scoreThreshold < $currentState.currentScoreAbsolute), 'scoreThreshold');
			} catch (error) {
				if (!rerunAttempted) {
					rerunAttempted = true;
					clearOldLocalStorage(data.results?.id);

					execute();
				} else {
					throw error;
				}
			}
		};

		execute();
	};

	const save = async () => {
		$currentState.intermediateState = true;
		animateScroll?.scrollTo({
			element: top,
			duration: 1500,
			easing: easeOutElastic,
			container: document?.getElementById('page-content')
		});

		const state = get(currentState);
		$currentState.results =
			!state?.results || state?.results?.length === 0
				? [state.currentResult!]
				: [...state.results, state.currentResult!];

		$currentState.currentScore = getAveragePercentResult($currentState);
		$currentState.currentScoreAbsolute = getTotalAbsoluteResult($currentState);
		const releaseDt = mergeDateTime($currentState.task.date, $currentState.task.time);

		const payload = {
			..._.omit($currentState, ['task', 'currentMode', 'intermediateState', 'currentResult']),
			taskId: $page.params.id,
			taskReleaseDate: releaseDt
		} as CompletionTaskDto;


		incentiveToShow = _.maxBy($currentState.task.task_incentive_content?.filter(x => x.scoreThreshold < $currentState.currentScoreAbsolute), 'scoreThreshold');


        let userId = localStorage?.getItem('userId');
        await new TaskApiClient().createTaskResult(payload, userId || '');
    };

	function easeOutElastic(x: number): number {
		const c4 = (2 * Math.PI) / 3;

		return x === 0
			? 0
			: x === 1
				? 1
				: Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1;
	}

	const clearOldLocalStorage = (exceptId: string) => {
		const now = new Date();
		for (var key in localStorage) {
			if (key.length === 36 && key !== exceptId) { //check for uuid
				const itemJson = localStorage.getItem(key);
				if (itemJson) {
					const item = JSON.parse(itemJson);
					if (new Date(now.setDate(now.getDate() - 5)) > new Date(item?.currentResult?.finishedAt) && new Date(item?.currentResult?.finishedAt)?.getFullYear() !== 1970) {
						localStorage.removeItem(key); // clear if it was done more than 5 days ago
					}
				}
			}
		}
	};
</script>

<div class="flex justify-center" dir="ltr">
    <div class="p-4 max-w-[760px] flex flex-col gap-5 w-full mt-10 mb-10">
        <div class="flex justify-between ">
            {#if data.task.type === 'home'}
                <p class="flex-1 text-2xl badge font-bold">
                    {$t('t.home')}
                    {format(new Date(data.task.date), 'dd.MM.yyyy')}
                </p>
            {:else}
                <p class="flex-1 text-2xl badge font-bold">
                    {$t('t.class')}
                    {format(new Date(data.task.date), 'dd.MM.yyyy')}
                </p>
            {/if}
            {#if data.task.type === 'home' || data.task.type === 'class'}
                <div class="flex justify-end m-2">
                    <BaseButton size="sm" className="rounded variant-ghost text-secondary" on:click={downloadTaskPDF}>
                        <IconPrinter class="bg-secondary"/>
                    </BaseButton>
                </div>

            {/if}
        </div>

        {#if data?.task?.content?.length > 0}
            <div class="mt-10 min-h-[30vh] w-full">
                <ContentEditor bind:content={data.task.content} readMode="true"/>
            </div>
        {:else if data?.task?.pageContent && data?.task?.pageContent.content_items.length > 0}
            <div class="pt-5 pb-10 px-0 md:px-9">
                <LexicalContent
                        isReadMode={true}
                        content={data?.task?.pageContent}
                />
            </div>
        {/if}

        <div id="anchor" bind:this={top}>
            <hr>
        </div>

        {#if _.values($currentState.task.additionalTasks).some((x) => x.enabled) && $currentState.intermediateState && !customTaskMode}
            <AdditionalTasksNavigation useTempState={!usePersistentState}/>
        {/if}

        {#if $currentState.intermediateState}
            <ResultBlock useTempState={!usePersistentState}/>
        {/if}

        {#if $currentState.currentMode === TaskMode.translation}
            <TranslationMode
                    bind:model={$currentState.currentResult}
                    hintsEnabled={$currentState.task.hintsEnabled}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.listen}
            <ListeningMode
                    bind:model={$currentState.currentResult}
                    maxScore={$currentState.task.additionalTasks.listen.maxScore}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.bytime}
            <TranslationByTimeMode
                    bind:model={$currentState.currentResult}
                    task={$currentState.task.additionalTasks.bytime}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.audiodic}
            <TranslationMode
                    bind:model={$currentState.currentResult}
                    hintsEnabled={$currentState.task.additionalTasks.audiodic.hintEnabled}
                    maxScore={$currentState.task.additionalTasks.audiodic.maxScore}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.phantom}
            <PhantomDictantMode
                    bind:model={$currentState.currentResult}
                    task={$currentState.task.additionalTasks.phantom}
                    on:submitTask={save}
            />
        {:else if $currentState.currentMode === TaskMode.voice}
            <VoiceMode
                    bind:model={$currentState.currentResult}
                    hintsEnabled={$currentState.task.additionalTasks.voice?.hintEnabled ?? true}
                    maxScore={$currentState.task.additionalTasks.voice?.maxScore ?? 100}
                    on:submitTask={save}
            />
        {/if}

        {#if incentiveToShow && $currentState.intermediateState}
            <div class="mt-10  p-4">
                <p class="flex text-lg font-bold justify-center mb-5">{$t('t.inventiveTitle')}</p>
                {#if incentiveToShow.incentive_content.value}
                    <ContentEditor readMode={true} content={incentiveToShow.incentive_content.value}/>
                {:else}
                    <LexicalContent
                            isReadMode={true}
                            content={incentiveToShow.incentive_content.page_content}
                    />
                {/if}
            </div>
        {/if}
    </div>
</div>


<style>
    :global(#page-content) {
        overflow: scroll;
        height: auto;
    }
</style>

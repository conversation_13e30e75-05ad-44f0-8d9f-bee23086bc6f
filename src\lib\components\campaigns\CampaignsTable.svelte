<script lang="ts">

    import type {ShortCampaignDto} from "$common/models/dtos/notification.dto";
    import {t} from "$lib/i18n/config";
    import {format} from "date-fns";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import BaseButton from "$components/common/BaseButton.svelte";
    import {createEventDispatcher} from "svelte";
    import {IconX} from "@tabler/icons-svelte";
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";


    export let loadMoreFunc;
    export let campaigns: ShortCampaignDto[];
    const dispatch = createEventDispatcher();


    $: tableTitles = [
        {title: $t('campaigns.table.author'), textCenter: false},
        {title: $t('campaigns.table.message'), textCenter: true},
        {title: $t('campaigns.table.date'), textCenter: false},
        {title: $t('campaigns.table.recipient'), textCenter: false},
    ];

</script>


<div class=" h-full">
    <table dir='rtl' class="table !overflow-auto  table-hover table-compact">
        <thead on:keypress>
        <tr>
            {#each tableTitles as titleObj}
                <th class="{titleObj.textCenter?'text-center':'text-start'}">{titleObj.title}</th>
            {/each}
            <OnlyForRole>
                <th class="text-start">{$t('campaigns.table.action')}</th>
            </OnlyForRole>
        </tr>
        </thead>
        <tbody>
        <InfiniteTableScrollContainer loadMoreFunc={loadMoreFunc}>
            {#each campaigns as campaign}
                <tr>
                    <td class="w-[10%]">{campaign.author}</td>
                    <td dir="auto" class="break-all text-center">{campaign.message}</td>
                    <td class="w-[10%]">{format(new Date(campaign.createdAt), 'dd.MM.yyyy')}</td>
                    <td class="w-[15%]">{campaign.recipientName}</td>
                    <OnlyForRole>
                        <td>
                            <BaseButton className="bg-error-600" on:click={dispatch('deleteCampaign',campaign.id)}
                                        size="sm">
                                <IconX size="18"/>
                            </BaseButton>
                        </td>
                    </OnlyForRole>
                </tr>
            {/each}

        </InfiniteTableScrollContainer>
        </tbody>
    </table>

</div>
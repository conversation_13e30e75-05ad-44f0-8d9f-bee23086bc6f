import { createClient } from 'redis';
import { Repository, Schema } from 'redis-om';
import { config } from '../config';

export const sentenceSchema = new Schema('sentence', {
	value: { type: 'text' },
})

export const getSentenceSchema = (name: string) => new Schema(name, {
	value: { type: 'text' },
})

export const redis = createClient({ url: config.redis.URL })


const getSentenceRepository = async (schema: string): Promise<Repository> => {
	if (!redis.isOpen) {
		await redis.connect()
	}

	return new Repository(getSentenceSchema(schema), redis)
}

export { getSentenceRepository }
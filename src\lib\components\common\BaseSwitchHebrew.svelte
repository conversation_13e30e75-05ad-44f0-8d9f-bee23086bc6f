<script lang="ts">
    import PrintLetter from '../../../img/PrintLetter.png';
    import ScriptLetter from '../../../img/ScriptLetter.png';
    import {modeCurrent} from '@skeletonlabs/skeleton';

    export let state: boolean;
    const cTransition = `transition-all duration-[200ms]`;
    const cTrack = 'cursor-pointer';
    const cThumb = 'aspect-square scale-[0.8] flex justify-center items-center';
    export let bigSize = false;
    export let bgLight = 'bg-surface-50-900-token';
    export let bgDark = 'bg-surface-900-50-token';
    export let width = 'w-12';
    export let height = bigSize ? 'h-10' : 'h-6';
    export let ring = 'ring-[1px] ring-surface-500/30';

    export let rounded = 'rounded-token';
    const translateX = bigSize ? 'translate-x-[50%]' : 'translate-x-[100%]';
    $: trackBg = state === true ? bgLight : bgDark;
    $: thumbBg = state === true ? bgDark : bgLight;
    $: thumbPosition = state === true ? translateX : '';
    $: classesTrack = `${cTrack} ${cTransition} ${width} ${height} ${ring} ${rounded} ${trackBg} ${
        $$props.class ?? ''
    }`;
    $: classesThumb = `${cThumb} ${cTransition} ${height} ${rounded} ${thumbBg} ${thumbPosition}`;

    function onToggleHandler(): void {
        state = !state;
    }
</script>

<div
        class="lightswitch-track relative {classesTrack} {bigSize ? 'w-[90px] h-full' : 'w-[70px]'}"
        on:click={onToggleHandler}
        on:click
        on:keydown
        on:keyup
        on:keypress
        role="switch"
        aria-label="Light Switch"
        aria-checked={state}
        title="Toggle Hebrew Mode"
        tabindex="0"
>

    <div class="lightswitch-thumb {classesThumb}">
        <img
                class={`${state ? 'w-4' : 'w-7'} ${$modeCurrent ^ state ? '' : 'invert'}`}
                src={state ? PrintLetter : ScriptLetter}
                alt="text"
        />
    </div>
</div>

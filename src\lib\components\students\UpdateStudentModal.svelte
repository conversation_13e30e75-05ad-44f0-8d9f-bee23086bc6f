<script lang="ts">
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import {t} from '$lib/i18n/config';
    import {StudentEditModalState} from '$lib/state/student-edit-state';
    import StudentModalForm from '$components/students/StudentModalForm.svelte';
    import {onMount} from 'svelte';
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import GroupsHistory from '$components/students/GroupsHistory.svelte';
    import {loadingWrap} from '$lib/common/utils';
    import NotificationStore from "$lib/state/notification-state";
    import {NotificationType} from "$common/models/enums";
    import StudentStatistics from "$components/students/StudentStatistics.svelte";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";

    let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;

    const modalStore = getModalStore();

    const formData = $StudentEditModalState;

    onMount(() => {
        $GroupHistoryState.previousModalName = $t('students.students.modal.previousmodalname');
    });

    async function handleSubmit(event) {
        const data = new FormData(this);
        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            modalErrors = result.data.errors;
        } else {
            await loadingWrap(async () => {
                await invalidate('load:students');
            });
            modalStore.close();
            NotificationStore.push(
                {
                    type: NotificationType.success,
                    message: t.get('students.students.notifications.edit.success')
                }, 5);
        }
    }

</script>

{#if $modalStore[0]}
    <div class="overflow-hidden modal card p-4 shadow-xl space-y-4 sm:w-2/3 lg:w-1/2">
        <header class="text-2xl font-bold mx-5">{$t('students.students.modal.title')}</header>
        <StudentModalForm action="updateStudent" {handleSubmit} {formData} {modalErrors}>
            <svelte:fragment slot="groupSettingsForm">
                <GroupsHistory studentId={$StudentEditModalState?.id}/>
            </svelte:fragment>
            <svelte:fragment slot="statistics">
                <OnlyForRole>
                    <br/>
                    <StudentStatistics/>
                </OnlyForRole>
            </svelte:fragment>
        </StudentModalForm>
    </div>
{/if}

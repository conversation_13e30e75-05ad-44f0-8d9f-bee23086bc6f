import {wrapFunc} from "$api/core/misc/response-wrapper";
import {paramsToKeyValue} from "$api/core/utils";
import {getTheNumberComplaintsByUserId} from "$api/core/services/complaint.service";
import type {RequestEvent} from "@sveltejs/kit";

export const GET = async ({url}: RequestEvent) =>
    wrapFunc(async () => {
        const {userId} = paramsToKeyValue(
            url.searchParams
        );
        return await getTheNumberComplaintsByUserId(userId)
    });
<script lang="ts">
    import {IconInfoCircle} from "@tabler/icons-svelte";
    import InfiniteTableScrollContainer from "$components/common/InfiniteTableScrollContainer.svelte";
    import {modeCurrent, popup} from '@skeletonlabs/skeleton';
    import SortingTableHeader from "$components/common/SortingTableHeader.svelte";
    import {ComplaintsStatisticFilter} from "$lib/state/complaints-statistic-state";
    import {ComplaintsStatisticFilterField} from "$common/models/enums";
    import {t} from "$lib/i18n/config";

    export let statisticComplaints;


    $: tableTitles = [
        {
            title: $t('complaints.statistic.table.teacherName')
        },
        {
            title: $t('complaints.statistic.table.numberOfComplaints'),
            sortBy: ComplaintsStatisticFilterField.complaints

        },
        {
            title: $t('complaints.statistic.table.numberOfSentences'),
            sortBy: ComplaintsStatisticFilterField.sentences
        },
        {
            title: $t('complaints.statistic.table.numberOfSentencesHandled'),
            sortBy: ComplaintsStatisticFilterField.handled
        },
        {
            title: $t('complaints.statistic.table.numberOfSentencesNotHandled'),
            sortBy: ComplaintsStatisticFilterField.nothandled

        },
        {
            title: $t('complaints.statistic.table.sentences')

        }
    ];


    function createToolTipHover(index) {
        return {
            event: 'click',
            target: `item-${index}`,
            placement: 'top'
        };
    }
</script>
<table dir='rtl' class="table table-hover  table-compact">
    <thead on:keypress>
    {#each tableTitles as header}
        <th class="text-right {!$modeCurrent ? 'wright-color-dark': 'wright-color'} accent-primary-300  !shadow-none !border-none !ring-0 !rounded-none ">
            <SortingTableHeader
                    {header} state={ComplaintsStatisticFilter}
            />
        </th>
    {/each}
    </thead>
    <tbody>

    {#each statisticComplaints as statistic}
        <tr>
            <td class="">
                {statistic.teacherName}
            </td>
            <td>
                {statistic.numberOfComplaints}
            </td>
            <td>
                {statistic.numberOfSentences}
            </td>
            <td>
                {statistic.numberOfSentencesHandled}
            </td>
            <td>
                {statistic.numberOfSentencesNotHandled}
            </td>
            <td>
                <button use:popup={createToolTipHover(statistic.id)}
                        class="btn btn-md variant-filled bg-secondary gap-2 text-black border-[1px] border-accent">
                    <IconInfoCircle size={20} stroke="1.5"/>
                </button>
                <div class="card variant-filled-primary w-fit max-w-[300px] h-fit max-h-[250px]"
                     data-popup="item-{statistic.id}">
                    <div class="flex flex-col max-h-[250px] overflow-hidden overflow-y-auto p-2 gap-2">
                        {#each statistic.sentences as sentence,index}
                            <a data-sveltekit-preload-data="tap" href="/tasks/{sentence.taskId}"
                               class="flex gap-1 h-fit w-fit {!sentence.isHandled?'text-red-600':'text-success-300-600-token'}">
                                <p>{index + 1}.</p>
                                <p>{sentence.sentenceValue}</p>
                            </a>

                        {/each}
                    </div>
                    <div class="arrow variant-filled-primary"/>
                </div>
            </td>
        </tr>
    {/each}
    </tbody>
</table>

<script lang="ts">
    import {onMount} from 'svelte';
    import {browser} from "$app/environment";

    export let loadMoreFunc: () => void;

    let infiniteScrollLoadingTrigger: HTMLElement;
    export let mode: 'default' | 'popUp' = 'default';

    onMount(() => {
        if (browser) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    const htmlElement = document?.getElementById(mode === 'popUp' ? 'popUpLists' : 'page');
                    const elementHeight = mode === 'popUp' ? htmlElement?.clientHeight : window?.innerHeight
                    if (entry?.isIntersecting) loadMoreFunc()
                });
            });

            observer?.observe(infiniteScrollLoadingTrigger);

            return () => {
                observer?.disconnect();
                infiniteScrollLoadingTrigger.scrollIntoView();
            };
        }
    });
</script>

<!--<div class=" {$LoadingState ? 'animate-pulse cursor-loading' : ''}">-->
<slot/>

<tr class="h-1" bind:this={infiniteScrollLoadingTrigger}></tr>
<!--</div>-->


<script lang="ts">
	import { afterUpdate } from 'svelte';
	import BaseButton from '../common/BaseButton.svelte';
	import { t } from '$lib/i18n/config';
	import { IconEdit } from '@tabler/icons-svelte';
	import type { EditableTaskDto, TaskDto } from '$common/models/dtos/task.dto';
	import { goto } from '$app/navigation';
	import { intlFormat } from 'date-fns';
	import { CurrentEditableState } from '$lib/state/task-current-editable-state';
	import type { GroupDto } from '$common/models/dtos/group.dto';

	export let tasks: TaskDto[] = [];
	export let groups: GroupDto[] = [];

	$: tableTitles = [
		$t('tasks.table.head.date'),
		$t('tasks.table.head.type'),
		$t('tasks.table.head.lang'),
		$t('tasks.table.head.group'),
		$t('tasks.table.head.comment'),
		$t('tasks.table.head.dict'),
		$t('tasks.table.head.edit'),
		$t('tasks.table.head.res')
	];



	const goEditExistingTask = async (task: TaskDto) => {
		CurrentEditableState.set(task as EditableTaskDto);
		await goto(`/tasks/${task.id}`);
	};
</script>

<div class="table-container">
	<table class="table table-hover table-compact mt-5">
		<thead on:keypress>
			<tr>
				{#each tableTitles as title}
					<th class="text-right">{title}</th>
				{/each}
			</tr>
		</thead>
		<tbody>
			{#each tasks as row, rowIndex}
				{@const groupName = groups?.find((x) => x.id === row.groupId)?.name}
				<tr>
					<td
						>{intlFormat(new Date(row.date), {
							day: '2-digit',
							month: '2-digit',
							year: 'numeric'
						})}</td
					>
					<td>{row.type}</td>
					<td>{row.lang}</td>
					<td>
						<a
							class="font-medium text-blue-600 dark:text-blue-400 hover:underline badge text-sm"
							href="/groups/{row.groupId}"
							target="_self">{groupName}</a
						>
					</td>
					<td>{row.commentPublic}</td>
					<td>dict</td>
					<!-- data.data[0].groupsStudents[0].group.name -->
					<td class="flex items-center gap-2">
						<BaseButton size="sm" on:click={() => goEditExistingTask(row)}>
							{$t('students.students.table.editButton')}
							<IconEdit size={20} stroke="1.5" />
						</BaseButton>
					</td>
					<td />
				</tr>
			{/each}
		</tbody>
	</table>
</div>

<style>
	table td {
		vertical-align: middle;
	}
</style>

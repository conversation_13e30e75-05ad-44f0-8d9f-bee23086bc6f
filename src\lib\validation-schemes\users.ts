import {z} from 'zod';

export const userSchemaForUpdatingUser = z.object({
    tz: z
        .string()
        .min(6, {message: 'users.modalUsers.formFieldsErrors.tzMin'})
        .max(12, {message: 'users.modalUsers.formFieldsErrors.tzMax'}),
    firstname: z.string().min(2, {message: 'users.modalUsers.formFieldsErrors.firstname'}),
    lastname: z.string().min(2, {message: 'users.modalUsers.formFieldsErrors.lastname'}),
    email: z.string().email({message: 'users.modalUsers.formFieldsErrors.email'}).min(2, {message: 'users.modalUsers.formFieldsErrors.emailLength'}),
    phone: z.string().refine((phone) => {
        return phone === '' || /^\+?\d{1,4}?[-.\s]?\(?\d{1,3}?\)?[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/.test(phone);
    }, 'users.modalUsers.formFieldsErrors.phone'),
    password: z.string().refine((password) => {
        return (
            password === '' || (password.length > 5 && /\d/.test(password) && /[a-zA-Z]/.test(password))
        );
    }, 'users.modalUsers.formFieldsErrors.passwordToUpdate')
});

export const userSchemaForCreatingUser = z.object({
    tz: z
        .string()
        .min(6, {message: 'users.modalUsers.formFieldsErrors.tzMin'})
        .max(12, {message: 'users.modalUsers.formFieldsErrors.tzMax'}),
    firstname: z.string().min(2, {message: 'users.modalUsers.formFieldsErrors.firstname'}),
    lastname: z.string().min(2, {message: 'users.modalUsers.formFieldsErrors.lastname'}),
    email: z.string().email({message: 'users.modalUsers.formFieldsErrors.email'}).min(2, {message: 'users.modalUsers.formFieldsErrors.emailLength'}),
    phone: z.string().refine((phone) => {
        return phone === '' || /^\+?\d{1,4}?[-.\s]?\(?\d{1,3}?\)?[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,9}$/.test(phone);
    }, 'users.modalUsers.formFieldsErrors.phone'),
    password: z
        .string()
        .min(5, {message: 'users.modalUsers.formFieldsErrors.passwordMin'})
        .refine((value) => /[a-zA-Z]/i.test(value), 'users.modalUsers.formFieldsErrors.passwordLetter')
        .refine((value) => /[0-9]/.test(value), 'users.modalUsers.formFieldsErrors.passwordDigit')
});

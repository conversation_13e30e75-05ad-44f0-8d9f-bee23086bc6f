<script lang="ts">
	import {
		IconClockHour9,
		IconEar,
		IconEyeOff,
		IconLanguage,
		IconHeadphones,
		IconMicrophone

	} from '@tabler/icons-svelte';
	import { TaskMode } from '$common/models/enums';

	export let mode = TaskMode.translation;
	export let className = '';
</script>

<span class={className}>
	{#if mode === TaskMode.translation}
		<IconLanguage />
	{:else if mode === TaskMode.listen}
		<IconHeadphones />
	{:else if mode === TaskMode.bytime}
		<IconClockHour9 />
	{:else if mode === TaskMode.phantom}
		<IconEyeOff />
	{:else if mode === TaskMode.audiodic}
		<IconEar />
	{:else if mode === TaskMode.voice}
		<IconMicrophone />
	{:else}
		<IconLanguage />
	{/if}
</span>

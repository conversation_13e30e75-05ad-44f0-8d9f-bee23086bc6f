import { ApplicationInsights } from '@microsoft/applicationinsights-web';

const isProd = import.meta.env.MODE === 'production';

const appInsights = isProd ? new ApplicationInsights({
    config: {
		instrumentationKey: import.meta.env.VITE_APPINSIGHTS_INSTRUMENTATION_KEY || process.env.APPINSIGHTS_INSTRUMENTATION_KEY,
    }
  }) : undefined;
  
  if (isProd) {
  	appInsights?.loadAppInsights();
  }

export const handleError = isProd ? ({ error, event }) => {
	if (appInsights) {
		appInsights.trackException({
			exception: error,
			properties: {
				url: event.url.href,
				method: event.request.method,
				user: event?.locals?.user
			},
		});
	}
	console.error(error);
} : undefined;

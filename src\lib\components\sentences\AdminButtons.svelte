<script lang="ts">
    import {IconStar, IconStarFilled, IconX} from "@tabler/icons-svelte";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {Permission} from "$common/models/enums";

    export let isFavorite: boolean;
    export let isNew: boolean;
    export let changeFavorite: () => void;
    export let deleteSentence: () => void;
</script>


<div class="flex flex-col justify-around  border-r-[1px]  pr-2">
    <OnlyForRole permission={Permission.editFavorites}>
        {#if isFavorite}
            <button on:click={changeFavorite} class="cursor-pointer text-warning-600">
                <IconStarFilled size="28"/>
            </button>
        {:else}
            <button on:click={changeFavorite} class="cursor-pointer text-warning-600">
                <IconStar size="28"/>
            </button>
        {/if}
    </OnlyForRole>
    <OnlyForRole permission={Permission.deleteNonFavSentences} additionalCondition={!isFavorite}>
        {#if !isNew}
				<span on:click={deleteSentence} class="cursor-pointer text-error-600">
					<IconX size="28"/>
				</span>
        {/if}
    </OnlyForRole>

</div>

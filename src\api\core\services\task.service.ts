import {db} from '../service-clients/db';
import {mapper, objectWithoutKey} from '$common/core/mapper';
import type {
    CompletionTaskDto,
    FileUploadDto,
    TaskDto,
    TaskResultDto
} from '$common/models/dtos/task.dto';
import type {TaskFilterDto} from '$common/models/filters/task-filter.dto';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3';
import _ from 'lodash';
import {config} from '$api/core/config';
import {addDays, differenceInMinutes, min} from 'date-fns';
import zlib from "zlib";
import {TaskSpeedMode} from "$common/models/enums";
import {generateGuid} from "$common/core/utils";


export const getStudentResultsByTaskId = async (id: string, studentId: string) => {

    const data = await db.student_results.findFirst({
        where: {
            taskId: id,
            studentId: studentId
        },
        select: {
            id: true,
            taskId: true,
            studentId: true,
            resultsDeflate: true,
            currentScore: true,
            currentScoreAbsolute: true,
            name: true,
            whatsapp: true,
            taskReleaseDate: true
        }
    });

    return data ? {
        ...data,
        results: data.resultsDeflate ? JSON.parse(zlib.inflateSync(Buffer.from(data?.resultsDeflate!, 'base64')).toString()) : [],
        fromDeflated: data?.resultsDeflate?.length && data?.resultsDeflate?.length > 0
    } : null
};


export const switchUserNameToId = async () => {

    await db.$transaction(async (tx) => {
        const task = await tx.task.findMany({
            where: {
                createdBy: {
                    not: null
                }
            },
            select: {
                createdBy: true
            }
        });
        const taskWithoutUuid = task.filter((t) => t?.createdBy?.length !== 36);
        const uniqueUserNames = [...new Set(taskWithoutUuid.map((el) => el.createdBy))] as string[];
        const foundedUser = [];
        for (const userName of uniqueUserNames) {
            const trimName = userName.split(' ');
            if (trimName.length > 1) {
                const user = await tx.users.findFirst({
                    where: {
                        OR: [
                            {
                                firstname: trimName[0],
                                lastname: trimName[1]
                            },
                            {
                                firstname: trimName[1],
                                lastname: trimName[0]
                            }
                        ]
                    }
                });
                if (user) {
                    foundedUser.push(user);
                }
            }
        }

        for (const userElement of foundedUser) {
            await tx.task.updateMany({
                where: {
                    AND: [
                        {
                            OR: [
                                {createdBy: `${userElement.firstname} ${userElement.lastname}`},
                                {createdBy: `${userElement.lastname} ${userElement.firstname}`}
                            ]
                        },
                        {
                            createdBy: {not: null}
                        }
                    ]
                },
                data: {
                    createdBy: userElement.id
                }
            });
        }
    })

}

export const createUpdateResult = async (dto: CompletionTaskDto, studentId: string) => {

    const deflatedResults = zlib.deflateSync(JSON.stringify(dto.results)).toString('base64');

    await db.student_results.upsert({
        where: {
            id: dto.id
        },
        create: {
            ...dto,
            studentId,
            results: {} as never,
            resultsDeflate: deflatedResults
        },
        update: {
            ...dto,
            studentId,
            results: {} as never,
            resultsDeflate: deflatedResults
        }
    });

    const updatedResultTask = await db.task.findFirst({where: {id: dto.taskId}});

    if (updatedResultTask?.type === 'home') {
        const allHomeTasksResultRecords = await db.student_results.findMany({
            where: {studentId, task: {type: 'home'}}
        });

        const lastTaskResultRecord = allHomeTasksResultRecords
            .filter((x) => x.taskId === dto.taskId && x.studentId === studentId)
            .at(0);

        const lastTaskResult: Array<TaskResultDto> = lastTaskResultRecord?.resultsDeflate ?
            JSON.parse(zlib.inflateSync(Buffer.from(lastTaskResultRecord.resultsDeflate, 'base64')).toString()) : []


        if (Array.isArray(lastTaskResult)) {
            const lastTaskTranslationScores = lastTaskResult?.filter((x) => x.mode === 'translation')?.map((x) => x.scorePercent);

            if (lastTaskResult.length > 0 && lastTaskResultRecord && lastTaskTranslationScores && lastTaskTranslationScores.length > 0) {
                // delays processing
                const minimumLastTaskTranslationStartDate = min(
                    lastTaskResult?.filter((x) => x.mode === 'translation').map((x) => new Date(x.startedAt))
                );

                const lastTaskCompletionDelay = Math.abs(differenceInMinutes(
                    minimumLastTaskTranslationStartDate,
                    lastTaskResultRecord.taskReleaseDate,
                ));

                const minDelays: number[] = [];
                allHomeTasksResultRecords.forEach((x) => {
                    const parsed: Array<TaskResultDto> = lastTaskResultRecord?.resultsDeflate ? JSON.parse(zlib.inflateSync(Buffer.from(lastTaskResultRecord.resultsDeflate, 'base64')).toString()) : []
                    if (Array.isArray(parsed)) {
                        const minimumTaskTranslationStartDate = min(
                            parsed?.filter((x) => x.mode === 'translation').map((x) => new Date(x.startedAt))
                        );
                        minDelays.push(
                            Math.abs(differenceInMinutes(minimumTaskTranslationStartDate, new Date(x.taskReleaseDate)))
                        );
                    }
                });
                const averageTaskCompletionDelay = +_.mean(minDelays)?.toFixed();
                const sanitizedAverageTaskCompletionDelay = isNaN(averageTaskCompletionDelay) ? 0 : averageTaskCompletionDelay;


                // scores processing
                const lastTaskBestTranslationScore = _.max(lastTaskTranslationScores) ?? 0;
                const allResults: Array<TaskResultDto> = _.flatten(
                    JSON.parse(JSON.stringify(allHomeTasksResultRecords.map((x) => lastTaskResultRecord?.resultsDeflate ? JSON.parse(zlib.inflateSync(Buffer.from(lastTaskResultRecord.resultsDeflate, 'base64')).toString()) : [])))
                );
                const allTranslationScores = allResults
                    .filter((x) => x.mode === 'translation')
                    .map((x) => x.scorePercent);
                const meanTranslationScore = _.mean(allTranslationScores) ?? lastTaskBestTranslationScore;

                await db.students.update({
                    where: {
                        id: studentId
                    },
                    data: {
                        lastTaskScore: lastTaskBestTranslationScore,
                        averageTaskScore: meanTranslationScore,
                        lastTaskDelay: lastTaskCompletionDelay <= 0 ? 0 : lastTaskCompletionDelay,
                        averageTaskDelay: sanitizedAverageTaskCompletionDelay <= 0 ? 0 : sanitizedAverageTaskCompletionDelay
                    }
                });
            }
        }
    }
};

export const getStudentTasksByGroupId = async (groupId: string, studentId: string): Promise<TableDataDto<TaskDto>> => {
    const studentGroups = await db.students_groups.findMany({
        where: {
            groupId,
            studentId
        }
    });
    const settings = await db.settings.findMany();
    const sg = studentGroups.map((x) => {
        return {
            groupId: x.groupId,
            dateStartTasks: x.dateStartTasks,
            dateStartActual: x.dateStartActual!,
            dateEndTasks: x.dateStartActual,
            dateEndActual: x.dateEndActual
        };
    });
    const where = composeGroupTaskTimeWhere(sg);
    const count = await db.task.count({where});
    const data = await db.task.findMany({
        where,
        include: {
            results: {
                where: {
                    studentId: studentId
                },
                select: {
                    id: true,
                    taskId: true,
                    studentId: true,
                    resultsDeflate: true,
                    currentScore: true,
                    currentScoreAbsolute: true,
                    name: true,
                    whatsapp: true,
                    taskReleaseDate: true
                }
            },
            additionalTasks: true,
            task_sentences: {
                include: {
                    sentence: {
                        include: {
                            translations: true
                        }
                    }
                }
            }
        },
        orderBy: [
            {
                date: 'desc',
            },
            {
                time: 'desc',
            }
        ],
    });

    const dtos = data.map((x) => mapper<TaskDto, unknown>(
        {
            ...x,
            results: x.results.map(xx => ({
                ...xx,
                results: xx.resultsDeflate ? JSON.parse(zlib.inflateSync(Buffer.from(xx.resultsDeflate!, 'base64')).toString()) : [],
                fromDeflated: xx?.resultsDeflate?.length && xx?.resultsDeflate?.length > 0
            })),
        }
    ));

    return {data: dtos, count};
};

export const getAllTasksByStudentId = async (studentId: string) => {
    const studentGroups = await db.students_groups.findMany({where: {studentId}});
    const sg = studentGroups.map((x) => {
        return {
            groupId: x.groupId,
            dateStartTasks: x.dateStartTasks,
            dateStartActual: x.dateStartActual!,
            dateEndTasks: x.dateStartActual,
            dateEndActual: x.dateEndActual
        };
    });
    const where = composeGroupTaskTimeWhere(sg);
    const count = await db.task.count({where});
    const data = await db.task.findMany({
        where,
        include: {
            results: {
                where: {
                    studentId: studentId
                },
                select: {
                    id: true,
                    taskId: true,
                    studentId: true,
                    resultsDeflate: true,
                    currentScore: true,
                    currentScoreAbsolute: true,
                    name: true,
                    whatsapp: true,
                    taskReleaseDate: true
                }
            },
            additionalTasks: true,
            task_sentences: {
                include: {
                    sentence: {
                        include: {
                            translations: true
                        }
                    }
                }
            }
        },
        orderBy: [
            {
                date: 'desc',
            },
            {
                time: 'desc',
            }
        ],
    });


    //const dtos = data.map((x) => mapper<TaskDto, unknown>(x));
    const dtos = data.map((x) => mapper<TaskDto, unknown>(
        {
            ...x,
            results: x.results.map(xx => ({
                ...xx,
                results: xx.resultsDeflate ? JSON.parse(zlib.inflateSync(Buffer.from(xx.resultsDeflate!, 'base64')).toString()) : [],
                fromDeflated: xx?.resultsDeflate?.length && xx?.resultsDeflate?.length > 0
            })),
        }
    ));
    return {data: dtos, count};
};

const composeGroupTaskTimeWhere = (
    studentGroups: {
        groupId: string;
        dateStartTasks: Date | null;
        dateStartActual: Date;
        dateEndTasks: Date | null;
        dateEndActual: Date | null;
    }[]
) => {
    const or = studentGroups.map((x) => {
        const lteDate = (x.dateEndActual && new Date(x.dateEndActual) < new Date()) ? addDays(new Date(x.dateEndActual), 1) : new Date();
        return {
            groupId: x.groupId,
            dateTime: {
                gte: new Date(
                    x.dateStartTasks && x.dateStartTasks < x.dateStartActual
                        ? x.dateStartTasks
                        : x.dateStartActual
                ).toISOString(),
                lte: lteDate.toISOString()
            }
        };
    });

    return {
        OR: [...or],
        isActive: true
    };
};

export const getStudentTaskById = async (id: string, studentId: string) => {
    const data = await db.task.findFirst({
        where: {
            id
        },
        include: {
            task_sentences: {
                include: {
                    sentence: {
                        include: {
                            translations: true
                        }
                    }
                }
            },
            additionalTasks: true,
            task_incentive_content: {
                include: {
                    incentive_content: {
                        include: {
                            page_content: {
                                include: {
                                    content_items: {
                                        orderBy: {
                                            position: 'asc'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            pageContent: {
                include: {
                    content_items: {
                        orderBy: {
                            position: 'asc'
                        }
                    }
                }
            }
        }
    });

    if (!data) throw new Error('no such task');
    //
    // try {
    // 	await db.students_groups.findFirstOrThrow({
    // 		where: {
    // 			groupId: data.groupId,
    // 			studentId,
    // 			OR: [
    // 				{
    // 					dateStartTasks: {
    // 						lte: data.date
    // 					}
    // 				},
    // 				{
    // 					dateStartActual: {
    // 						lte: data.date
    // 					}
    // 				}
    // 			]
    // 		}
    // 	});
    // } catch {
    // 	throw new Error('student is not authorized to see this task');
    // }

    return mapper<TaskDto, unknown>({
        ...objectWithoutKey(data, 'task_sentences'),
        sentences: data.task_sentences.map((x) => {
            return {
                ...x.sentence,
                displayAsText: x.defaultTaskView === 'text',
                index: x.indexN
            };
        }).sort((a, b) => a.index - b.index),
        task_incentive_content: data?.task_incentive_content?.map((tic) => ({
            ...tic,
            incentive_content: {
                ...tic?.incentive_content,
                page_content: {
                    ...tic?.incentive_content?.page_content,
                    content_items: tic?.incentive_content?.page_content?.content_items?.map((ci) => ({
                        ...ci,
                        content: ci?.content && ci?.type !== 'lexical' ? JSON.parse(ci.content) : ci?.content
                    }))
                }
            }
        })),
        additionalTasks: _.keyBy(data.additionalTasks, 'mode')
    });
};

export const getPublicTaskById = async (id: string) => {
    const data = await db.task.findFirst({
        where: {
            id
        },
        include: {
            task_sentences: {
                include: {
                    sentence: {
                        include: {
                            translations: true
                        }
                    }
                }
            },
            additionalTasks: true,
            task_incentive_content: {
                include: {
                    incentive_content: {
                        include: {
                            page_content: {
                                include: {
                                    content_items: {
                                        orderBy: {
                                            position: 'asc'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            pageContent: {
                include: {
                    content_items: {
                        orderBy: {
                            position: 'asc'
                        }
                    }
                }
            }
        }
    });

    if (!data) throw new Error('no such task');

    return mapper<TaskDto, unknown>({
        ...objectWithoutKey(data, 'task_sentences'),
        sentences: data.task_sentences.map((x) => {
            return {
                ...x.sentence,
                displayAsText: x.defaultTaskView === 'text',
                index: x.indexN
            };
        }).sort((a, b) => a.index - b.index),
        additionalTasks: _.keyBy(data.additionalTasks, 'mode')
    });
};

export const getTaskById = async (id: string): Promise<TaskDto> => {

    const settings = await db.settings.findMany()
    const data = await db.task.findFirst({
        where: {
            id
        },
        include: {
            pageContent: {
                include: {
                    content_items: {
                        orderBy: {
                            position: 'asc'
                        }
                    }
                }
            },
            task_sentences: {
                include: {
                    sentence: {
                        include: {
                            translations: true,
                            createdByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            },
                            updatedByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            }
                        }
                    },

                }
            },
            additionalTasks: true,
            task_incentive_content: {
                include: {
                    incentive_content: {
                        include: {
                            page_content: {
                                include: {
                                    content_items: true
                                }
                            }
                        }
                    }
                }
            },
            createdByUser: {
                select: {
                    firstname: true,
                    lastname: true
                }
            },
            updatedByUser: {
                select: {
                    firstname: true,
                    lastname: true
                }
            }
        }
    });

    console.log('DATA', data);


    return mapper<TaskDto, unknown>({
        ..._.omit(data, ['task_sentences', 'additionalTasks']),
        sentences: data?.task_sentences.map((x) => {
            return {
                ...x.sentence,
                displayAsText: x.defaultTaskView === 'text',
                index: x.indexN
            };
        }).sort((a, b) => a.index - b.index),
        task_incentive_content: data?.task_incentive_content?.map((tic) => ({
            ...tic,
            incentive_content: {
                ...tic?.incentive_content,
                page_content: {
                    ...tic?.incentive_content?.page_content,
                    content_items: tic?.incentive_content?.page_content?.content_items?.map((ci) => ({
                        ...ci,
                        content: ci?.content && ci?.type !== 'lexical' ? JSON.parse(ci.content) : ci?.content
                    }))
                }
            }
        })),
        additionalTasks: _.keyBy(data?.additionalTasks, 'mode'),
        createdBy: data?.createdByUser?.firstname && data?.createdByUser?.lastname ? `${data?.createdByUser?.firstname} ${data?.createdByUser?.lastname}` : '',
        updatedBy: data?.updatedByUser?.firstname && data?.updatedByUser?.lastname ? `${data?.updatedByUser?.firstname} ${data?.updatedByUser?.lastname}` : '',
    });
};

export const deleteTask = async (id: string) => {

    const studentResultDelete = db.student_results.deleteMany({
        where: {
            taskId: id
        }
    })

    const taskAdditionalDelete = db.task_additional.deleteMany({
        where: {
            taskId: id
        }
    })

    const taskIncentiveContentDelete = db.task_incentive_content.deleteMany({
        where: {
            taskId: id
        }
    })

    const taskSentenceDelete = db.task_sentences.deleteMany({
        where: {
            taskId: id
        }
    })

    const taskDelete = db.task.delete({
        where: {
            id
        }
    });

    await db.$transaction(
        [studentResultDelete,
            taskAdditionalDelete,
            taskSentenceDelete,
            taskIncentiveContentDelete,
            taskDelete]
    );

    return {success: true};
}

export const getTasks = async (filter: TaskFilterDto, getFullData = true): Promise<TableDataDto<TaskDto>> => {


    const where = composeWhereClause(filter);
    const count = await db.task.count({where});

    // await switchUserNameToId();

    const data = await db.task.findMany({
        take: filter.take,
        skip: filter.skip,
        where,
        include: {
            task_sentences: {
                include: {
                    sentence: {
                        include: {
                            translations: true,
                            createdByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            },
                            updatedByUser: {
                                select: {
                                    id: true,
                                    firstname: true,
                                    lastname: true
                                }
                            }
                        },
                    }
                }
            },
            results: getFullData ? {
                select: {
                    id: true,
                    taskId: true,
                    studentId: true,
                    resultsDeflate: true,
                    currentScore: true,
                    currentScoreAbsolute: true,
                    name: true,
                    whatsapp: true,
                    taskReleaseDate: true
                }
            } : false,
            additionalTasks: getFullData ? getFullData : {
                where: {
                    enabled: true,
                },
                select: {
                    mode: true,
                    enabled: true
                }
            },
            createdByUser: {
                select: {
                    firstname: true,
                    lastname: true
                }
            },
            updatedByUser: {
                select: {
                    firstname: true,
                    lastname: true
                }
            }
        },
        orderBy: [
            {
                date: 'desc',
            },
            {
                time: 'desc',
            }
        ],
    });


    const dtos = data.map((x) =>
        mapper<TaskDto, unknown>({
            ...objectWithoutKey(x, 'task_sentences'),
            results: x?.results?.map(xx => ({
                ...xx,
                results: xx?.resultsDeflate ? JSON.parse(zlib.inflateSync(Buffer.from(xx?.resultsDeflate!, 'base64')).toString()) : [],
                fromDeflated: xx?.resultsDeflate?.length && xx?.resultsDeflate?.length > 0
            })),
            sentences: x.task_sentences.map((y) => {
                return {
                    ...y.sentence,
                    displayAsText: y.defaultTaskView === 'text',
                    index: y.indexN
                };
            }).sort((a, b) => a.index - b.index),
            createdBy: `${x?.createdByUser?.firstname || ''} ${x?.createdByUser?.lastname || ''}`,
            updatedBy: `${x?.updatedByUser?.firstname || ''} ${x?.updatedByUser?.lastname || ''}`,
        })
    );

    return {data: dtos, count};
};

const makeSureIdsExisting = async (ids: string[]): Promise<string[]> => {
    const existingRecords = await db.sentences.findMany({
        where: {
            id: {
                in: ids,
            },
        },
        select: {
            id: true,
        },
    });

    return existingRecords.map(record => record.id);
}

export const createTask = async (task: TaskDto, userId: string) => {


    const existingSentencesIds = await makeSureIdsExisting(task.sentences.map(x => x.id))

    const task_sentences = task.sentences.map((x) => {
        return {
            taskId: task.id,
            sentenceId: x.id,
            defaultTaskView: !x.displayAsText ? 'audio' : ('text' as 'text' | 'audio'),
            indexN: x.index,
            createdBy: userId
        };
    }).filter(x => existingSentencesIds.includes(x.sentenceId));

    const incentiveContent = task.task_incentive_content
        ?.map((ic) => _.omit({...ic, scoreThreshold: ic.scoreThreshold || 0, taskId: task.id}, ['incentive_content']))
        ?.filter((ic) => ic.incentiveContentId);

    let speedValue: keyof typeof TaskSpeedMode = 'slow';

    if (task.additionalTasks?.phantom)
    {
        for (const key in TaskSpeedMode) {
            if (TaskSpeedMode[key] === task.additionalTasks.phantom.speedMode) {
                speedValue = key as keyof typeof TaskSpeedMode;
            }
        }
    }

    const additionalTasks = _.values(task.additionalTasks).map((at) => {

        return {
            ...at,
            taskId: task.id,
            uniqueHash: _.uniqueId(),
            delay: +at.delay,
            time: +at.time,
            speedMode: speedValue,
            maxScore: +at.maxScore
        };
    });

    if (task.commentPublic) {
        task.commentPrivate = task.commentPublic.replace(/([\u05B0-\u05BD]|[\u05BF-\u05C7])/g, "")
    }


    return await db.$transaction(
        async (tx) => {
            const existingPageContent = await tx.page_content.findFirst({
                where: { id: task.page_content?.id }
            });

            await tx.task.upsert({
                where: {
                    id: task.id
                },
                create: {
                    ..._.omit(task, ['sentences', 'additionalTasks', 'task_incentive_content', 'createdByUser', 'updatedByUser', 'updatedBy', 'page_content']),
                    pageContent: {
                        create: {
                            id: generateGuid(),
                            incentiveContentId: undefined,
                            content_items: {
                                createMany: {
                                    data: task.page_content.content_items.map((ci) => ({
                                        type: ci.type,
                                        position: ci.position,
                                        content: ci.content
                                    }))
                                }
                            }
                        }
                    },
                    createdBy: userId,
                    updatedBy: userId,
                },
                update: {
                    ..._.omit(task, ['sentences', 'additionalTasks', 'task_incentive_content', 'createdByUser', 'updatedByUser', 'createdBy', 'page_content']),
                    updatedBy: userId,
                    pageContent: existingPageContent
                      ? {
                        update: {
                            content_items: {
                                deleteMany: {},
                                createMany: {
                                    data: task.page_content.content_items.map((ci) => ({
                                        type: ci.type,
                                        position: ci.position,
                                        content: ci.content
                                    }))
                                }
                            }
                        }
                    }
                    : undefined
                }
            });

            await tx.task_sentences.deleteMany({
                where: {
                    taskId: task.id
                }
            });

            await tx.task_sentences.createMany({
                data: task_sentences
            });

            await tx.task_additional.deleteMany({
                where: {
                    taskId: task.id
                }
            });

            await tx.task_incentive_content.deleteMany({
                where: {
                    taskId: task.id
                }
            });
            await tx.task_incentive_content.createMany({
                data: incentiveContent
            });

            await tx.task_additional.createMany({
                data: additionalTasks
            });
        },
        {
            maxWait: 10000, // default: 2000
            timeout: 20000 // default: 5000
        }
    );
};


const composeWhereClause = (filter: TaskFilterDto): Record<string, any> => {
    const whereClause: Record<string, any> = {};
    if (filter.search) {
        whereClause.commentPrivate = {
            contains: filter.search
        }
    }
    if (filter.groupId) {
        whereClause.groupId = filter.groupId;
    }
    if (filter.type) {
        whereClause.type = filter.type;
    }
    if (filter.createdBy) {
        whereClause.createdBy = filter.createdBy;
    }
    return whereClause;
};

// const composeUpsertSentencesQuery = (
//     task: TaskDto
// ): {
//     create: { id: string; value: string; lang: string };
//     update: { id: string; value: string; lang: string };
//     where: { id: string };
// }[] => {
//     return task.translations.map((x) => ({
//         create: {
//             id: x.id,
//             value: x.value,
//             lang: x.lang
//         },
//         update: {
//             id: x.id,
//             value: x.value,
//             lang: x.lang
//         },
//         where: {
//             id: x.id
//         }
//     }));
// };

export const uploadFile = async (file: File): Promise<FileUploadDto> => {
    try {
        const sizeInMegabytes = file.size / (1024 * 1024);
        if (sizeInMegabytes > config.s3.ATTACHMENT_FILE_LIMIT_SIZE_MB) {
            throw new Error('File size is too large');
        }
        const fileBuffer = await file.arrayBuffer();
        const s3 = new S3Client({
            region: 'eu-central-1',
            credentials: {
                accessKeyId: config.s3.ACCESS,
                secretAccessKey: config.s3.SECRET,
            }
        });

        const params = {
            Bucket: process.env.VITE_S3_BUCKET,
            Key: `${process.env.VITE_TASKFILE_FOLDERNAME}/${file.name}`,
            ContentType: file.type,
            Body: fileBuffer as Buffer,
            ACL: 'public-read'
        };

        const command = new PutObjectCommand(params);
        const response = await s3.send(command);

        if (response && response?.$metadata?.httpStatusCode === 200) {
            return mapper<FileUploadDto, unknown>({
                success: 1,
                file: {
                    name: file.name,
                    title: file.name,
                    size: file.size,
                    type: file.type,
                    url: `https://${process.env.VITE_S3_BUCKET}.s3.${process.env.VITE_S3_REGION}.amazonaws.com/${process.env.VITE_TASKFILE_FOLDERNAME}/${file.name}`
                }
            });
        }
        return mapper<FileUploadDto, unknown>({
            success: 0
        });
    } catch (e) {
        throw new Error('Error uploading content to the storage...');
    }
};

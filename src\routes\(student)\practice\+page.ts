import { CampaignApiClient } from '$lib/core/api-clients/campaign-api.client';
import type { PageLoad } from '../../../../.svelte-kit/types/src/routes/(student)/notifications/$types';

export const ssr = false;

export const load: PageLoad = async ({ depends }) => {
	try {
		depends('load:notifications');
		return {
			notifications: new CampaignApiClient().getAllNotificationsByStudentId()
		};
	} catch (error) {
		return error;
	}
};

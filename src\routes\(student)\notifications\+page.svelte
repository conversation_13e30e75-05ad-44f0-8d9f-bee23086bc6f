<script lang="ts">
    import NotificationItem from '$components/students-home/NotificationItem.svelte';
    import {t} from '$lib/i18n/config';
    import {CampaignApiClient} from '$lib/core/api-clients/campaign-api.client';
    import {invalidate} from '$app/navigation';

    export let data;

    $: notifications = data.notifications;
    const markAsReadNotification = async (e) => {
        const {id} = e.detail;
        const data = await new CampaignApiClient().markNotificationAsReadById(id);
        if (data) await invalidate('load:notifications');
    };
</script>

<div class="flex justify-center" dir="ltr">
    <div class="p-4 max-w-[760px] flex flex-col gap-5 w-full mt-5 mb-5">
        <h1 class="text-xl font-bold mb-5">{$t('notifications.notifications')}</h1>
        <div class="flex flex-col gap-5">
            {#if notifications?.length > 0}
                {#each notifications as notification}
                    <NotificationItem {notification} on:markAsRead={markAsReadNotification}/>
                    <hr/>
                {/each}
            {:else}
                <div class="p-4 card mt-5">
                    <p class="text-lg italic font-semibold">{$t('home.noNotifications')}</p>
                </div>
            {/if}

        </div>
    </div>
</div>

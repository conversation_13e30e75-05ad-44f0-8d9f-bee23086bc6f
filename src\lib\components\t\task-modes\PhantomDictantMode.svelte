<script lang="ts">
    import type {TaskResultDto} from '$common/models/dtos/task.dto';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {createEventDispatcher} from 'svelte';
    import {initialDate} from '$lib/state/task-current-completion.state';
    import {checkTranslationResponse, isInitialDate} from '$lib/common/task-helpers';
    import type {AdditionalTaskDto} from '$common/models/dtos/task.dto';
    import {t} from '$lib/i18n/config';
    import SentenceInPhantomTask from "$components/t/SentenceInPhantomTask.svelte";
    import {TaskSpeedMode} from "$common/models/enums.js";

    const dispatcher = createEventDispatcher();

    export let model: TaskResultDto;
    export let task: AdditionalTaskDto;

    export let disabled = false;

    $: completions = model?.completions;
    $: revealMode = !isInitialDate(model.finishedAt, initialDate);

    const check = () => {
        checkTranslationResponse(model, task.maxScore);
        dispatcher('submitTask');
    };

</script>

{#each completions as sentence, i}
    {@const time = +TaskSpeedMode[task.speedMode] * sentence?.taskValue?.length  }
    <SentenceInPhantomTask
            index={i + 1}
            {revealMode}
            time={time}
            hintEnabled={task.hintEnabled}
            bind:sentence
            on:inputStarted|once={() => {
			if (i === 0) model.startedAt = new Date();
		}}
    />
{/each}

{#if !revealMode}
    <BaseButton on:click={check}>{$t('t.button')}</BaseButton>
{/if}

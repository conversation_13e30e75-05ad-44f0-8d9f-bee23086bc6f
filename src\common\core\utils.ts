import {v4 as uuidv4} from 'uuid';
import {ExclusionsState} from "$lib/state/exclusions-state";
import {get} from "svelte/store";
import type {ExclusionsDto} from "$common/models/dtos/Exclusions.dto";


export const generateGuid = (): string => uuidv4();


const getStudentUsedExclusions =
  (words: string[], exclusions: string[][]): {word: string, excl: string[], index: number}[] => {
    const result = words.map((word) => {
        for (const excl in exclusions) {
            if (exclusions[excl]?.includes(word)) {
                const index = exclusions[excl].indexOf(word);
                return {word, excl: exclusions[excl], index}
            }
        }
    })
      return result && result.length
        ? result.filter(x => x) as {word: string, excl: string[], index: number}[]
        : []
}

const applyStudentsExclusions =
  (
    words: string[],
   studentAppliedExclusions: {word: string, excl: string[], index: number}[]
  ) => {
    if (!studentAppliedExclusions || studentAppliedExclusions.length === 0) return words;
    return words.map((word) => {
        let result = word
        for (let i = 0; i < studentAppliedExclusions.length; i++) {
            if (studentAppliedExclusions[i].excl.find(x => x === word)) {
                result = studentAppliedExclusions[i].excl[studentAppliedExclusions[i].index]
                break;
            }
        }
        return result;
    })
}

const normalizeHebrewValueForComparison = (value: string) => {

    // remove invisible characters
    const normalizeString = (str: string): string =>
      str.replace(/\u200B/g, '').replace(/\u00A0/g, ' ').trim();

    // remove embedding characters
    const normalizeRTL = (str: string): string =>
      str.replace(/[\u200E\u200F]/g, '');

    const normalizePunctuation = (str: string): string =>
      str.replace(/[.,/#!$%^&*\-;:"{}=_`~()—–−?]/g, '').trim()

    return normalizeString(normalizeRTL(normalizePunctuation(value)));
}

export const compareFn = (task: string, answer: string) => {
    if (!task || !answer || answer == '') return '-';
    const hebrewTaskFiltered = task
        .replace(/([\u05B0-\u05BD]|[\u05BF-\u05C7]|\d)/g, "")
        .replace(/\\/g, '')
        .replace(/(\uFB2B)/g, 'ש')
        .replace(/(\uFB44)/g, 'פ')
        .replace(/(\uFB3B)/g, 'כ')
        .replace(/(\uFB31)/g, 'ב')
        .replace(/(\u05d9)/g, 'י')
        .replace(/(\uFB39)/g, 'י')
        .replace(/(\u2026)/g, '')
        .replace(/(\u20AA)/g, ' ')
        .replace('\'', ' ')
        .replace('׳', ' ')
        .replace(/[.,/#!$%^&*\-;:"{}=_`~()—–−?]/g, ' ')
        .replace(/\s+/g, ' ')
        .split(' ')
        .filter(String);


    const answerFiltered = answer
      .replace(/\\/g, ' ')
      .replace(/[^\u05D0-\u05EA '/\\]/g, ' ')
      .replace('\'', ' ')
      .replace('׳', ' ')
      .replace(/(\u2026)/g, '')
      .replace(/(\u20AA)/g, ' ')
      .replace(/[.,/#!$%^&*\-;:"{}=_`~()—–−?]/g, ' ')
      .replace(/\s+/g, ' ')
      .split(' ')
      .filter(String);

    const fullyNormalizedTaskWords = hebrewTaskFiltered.map((word) => normalizeHebrewValueForComparison(word));
    const fullyNormalizedAnswerWords = answerFiltered.map((word) => normalizeHebrewValueForComparison(word));

    const exclusionsList: ExclusionsDto[] = get(ExclusionsState);
    const preparedExclusions = exclusionsList.map((excl) => excl.exclusions.split(',').map((ex) => ex.trim()))
    const studentAppliedExclusions = getStudentUsedExclusions(fullyNormalizedAnswerWords, preparedExclusions);
    const taskWithHandledExclusions = applyStudentsExclusions(fullyNormalizedTaskWords, studentAppliedExclusions);
    const intersection = taskWithHandledExclusions.filter((x) => !fullyNormalizedAnswerWords.includes(x));

    return intersection.join(' ');
};

export const composeUserAlias = (user: any) =>
    `${user.firstname} ${user.lastname}${user.role ? `, ${user.role}` : ''} ${
        user.email ? `(${user.email})` : ''
    }`;

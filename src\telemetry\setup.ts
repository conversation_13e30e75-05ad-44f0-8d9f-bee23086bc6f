// OpenTelemetry setup - MUST be imported before any other modules
// This file initializes OpenTelemetry instrumentation before other modules are loaded

const isProd = process.env.NODE_ENV === 'production';

if (isProd) {
    // Only initialize OpenTelemetry in production to avoid conflicts
    console.log('Initializing OpenTelemetry instrumentation...');

    try {
        // Check if OpenTelemetry API is already registered to avoid duplicate registration
        const api = require('@opentelemetry/api');

        // Only proceed if propagation API hasn't been registered yet
        if (!api.propagation._getGlobalPropagator || !api.propagation._getGlobalPropagator()) {
            // Import and initialize OpenTelemetry instrumentation BEFORE other modules
            const { NodeSDK } = require('@opentelemetry/sdk-node');
            const { Resource } = require('@opentelemetry/resources');
            const { ATTR_SERVICE_NAME, ATTR_SERVICE_VERSION } = require('@opentelemetry/semantic-conventions');

            // Import specific instrumentations that we want to enable
            const { RedisInstrumentation } = require('@opentelemetry/instrumentation-redis');
            const { Redis4Instrumentation } = require('@opentelemetry/instrumentation-redis-4');

            // Create resource with service information
            const resource = new Resource({
                [ATTR_SERVICE_NAME]: 'hebreway-server',
                [ATTR_SERVICE_VERSION]: '1.0.0',
            });

            // Initialize the SDK with specific instrumentations
            const sdk = new NodeSDK({
                resource,
                // Configure specific instrumentations we want
                instrumentations: [
                    new RedisInstrumentation(),
                    new Redis4Instrumentation(),
                ],
                // Don't configure exporters here - let Application Insights handle it
            });

            // Start the SDK
            sdk.start();

            console.log('OpenTelemetry SDK initialized successfully with Redis instrumentation');
        } else {
            console.log('OpenTelemetry API already registered, skipping SDK initialization');
        }
    } catch (error) {
        console.warn('Failed to initialize OpenTelemetry SDK:', error.message);
        // Don't throw - allow the application to continue without OpenTelemetry
    }
} else {
    console.log('Skipping OpenTelemetry initialization in development mode');
}

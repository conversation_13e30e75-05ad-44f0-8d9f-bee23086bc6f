<script lang="ts">
    import {loadingWrap} from "$lib/common/utils";
    import {ComplaintFilterState} from "$lib/state/complaint-filter-state";
    import {beforeNavigate, invalidate} from "$app/navigation";
    import {onDestroy, onMount} from "svelte";
    import {initialComplaintFilter} from "$common/models/filters/complaint-filter.dto";
    import _ from "lodash";
    import {ComplaintsPagingState, initialComplaintsPaging} from "$lib/state/complaints-paging-state";
    import {Tab, TabGroup} from "@skeletonlabs/skeleton";
    import ComplaintsTab from "$components/complaints/ComplaintsTab.svelte";
    import ComplaintsStatisticTab from "$components/complaints/ComplaintsStatisticTab.svelte";
    import {ComplaintsStatisticFilter} from "$lib/state/complaints-statistic-state";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {t} from "$lib/i18n/config";
    import {loadFfmpeg} from "$lib/common/ffmpeg-helpers";

    export let data;


    $:count = data?.complaints?.count;


    let tabValue = 0;

    $: complaints = data?.complaints?.data?.reduce((acc, item) => {
        const existItem = acc.find(({sentenceId, taskId}) => sentenceId === item.sentenceId && taskId === item.taskId);
        if (existItem) {
            existItem.complaints.push({name: item.createdBy, comment: item.comment});
            existItem.count += 1;
        } else {
            acc.push({
                id: item.id,
                sentenceId: item.sentenceId,
                taskId: item.taskId,
                isHandled: item.isHandled,
                complaints: [{name: item.createdBy, comment: item.comment}],
                task_sentences: item.task_sentences,
                count: 1
            });
        }
        return acc;
    }, []);


    const unsubscribeComplaintFilter = ComplaintFilterState.subscribe(async () => {
        $ComplaintsPagingState = _.cloneDeep(initialComplaintsPaging)
        await loadingWrap(async () => {
            await invalidate('load:complaints')
        })
    })

    const unsubscribeComplaintsStatisticFilter = ComplaintsStatisticFilter.subscribe(async () => {
        await loadingWrap(async () => {
            await invalidate('load:complaints')
        })
    })

    onMount(async () => {
        await loadFfmpeg();
    });

    beforeNavigate(() => {
        unsubscribeComplaintFilter();
        unsubscribeComplaintsStatisticFilter();
    })

    onDestroy(() => {
        $ComplaintFilterState = _.cloneDeep(initialComplaintFilter)
    })
</script>


<div class="h-[calc(100vh-85px)] overflow-hidden flex flex-col px-6 pt-3">

    <TabGroup>

        <Tab on:click={null} bind:group={tabValue} name="requests-tab" value={0}>
            <b
            >
                {$t('complaints.tabs.complaints')}
            </b>
        </Tab>

        <OnlyForRole>

            <Tab on:click={null} bind:group={tabValue} name="requests-tab" value={1}>
                <b>
                    {$t('complaints.tabs.statistic')}
                </b>

            </Tab>
        </OnlyForRole>


        <svelte:fragment slot="panel">
            {#if tabValue === 0}
                <ComplaintsTab {count} {complaints} updaters={data?.updaters}/>
            {:else if tabValue === 1}
                <ComplaintsStatisticTab statisticComplaints={data?.statisticComplaints} updaters={data?.updaters}/>
            {:else}
                Loading...
            {/if}
        </svelte:fragment>
    </TabGroup>
</div>

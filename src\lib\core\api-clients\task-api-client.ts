import {get} from 'svelte/store';
import {BaseApiClient} from './base-api-client';
import type {TableDataDto} from '$common/models/dtos/table-data.dto';
import type {CompletionTaskDto, TaskDto} from '$common/models/dtos/task.dto';
import {TaskFilterState} from '$lib/state/task-filter-state';
import {TaskPagingState} from '$lib/state/task-paging-state';

export class TaskApiClient extends BaseApiClient {
    constructor() {
        super();
    }

    public getStudentTaskById = async (id: string) =>
        await this.getDataOrThrow(`/api/s/tasks?id=${id}`);

    public getPublicTaskById = async (id: string) =>
        await this.getDataOrThrow(`/api/s/public-tasks?id=${id}`);

    public getStudentTasksByGroupId = async () => {
        const {groupId} = get(TaskFilterState);

        return await this.getDataOrThrow(`/api/s/group-tasks?groupId=${groupId || ''}`);
    }

    public async getTasks(): Promise<TableDataDto<TaskDto>> {
        const {groupId, search, type, createdBy} = get(TaskFilterState);
        const currentPagingState = get(TaskPagingState);

        return await this.getDataOrThrow(
            `/api/tasks?groupId=${groupId}&search=${search}&type=${type}&take=${currentPagingState.take}&skip=${currentPagingState.skip}&createdBy=${createdBy}`
        );
    }


    public getTaskById = async (id: string): Promise<TaskDto | null> =>
        await this.getDataOrThrow(`/api/tasks?id=${id}`);

    public updateTask = async (task: TaskDto): Promise<TaskDto> =>
        await this.putDataOrThrow<TaskDto>('/api/tasks', task);

    public createTask = async (task: TaskDto) =>
        await this.postDataOrThrow<TaskDto>('/api/tasks', task, true);

    public getTaskResults = async (id: string): Promise<TaskDto[]> =>
        await this.getDataOrThrow(`/api/s/results?id=${id}`);

    public createTaskResult = async (result: CompletionTaskDto, userId: string): Promise<TaskDto> =>
        await this.postDataOrThrow<{ result: CompletionTaskDto, userId: string }>('/api/s/results', {
            result,
            userId
        });

    public deleteTask = async (id: string) =>
        await this.deleteOrThrow(`/api/tasks?id=${id}`, true)

}

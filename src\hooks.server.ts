import appInsights from 'applicationinsights';
import {authenticationMiddleware} from "$api/core/middlewares/authentication-middleware";
import {sequence} from "@sveltejs/kit/hooks";
import {authorizationMiddleware} from "$api/core/middlewares/authorization-middleware";
import type {Handle} from "@sveltejs/kit";
import { dataMiddleware } from '$api/core/middlewares/data-middleware';
import type { TelemetryItem } from 'applicationinsights/out/src/declarations/generated';

process.env.TZ = 'Asia/Jerusalem';
// process.env.TZ = 'UTC';

(BigInt.prototype as any).toJSON = function () {
    return this.toString();
};

const isProd = process.env.NODE_ENV === 'production';

if (isProd && !appInsights.defaultClient) {
    // Get connection string from environment variables
    const connectionString = process.env.APPLICATIONINSIGHTS_CONNECTION_STRING || 
                            `InstrumentationKey=${process.env.APPINSIGHTS_INSTRUMENTATIONKEY || process.env.VITE_APPINSIGHTS_INSTRUMENTATION_KEY}`;
    
    console.log('App Insights starting with connection string:', connectionString);
    
    appInsights.setup(connectionString)
        .setAutoCollectExceptions(true)
        .setAutoCollectRequests(true)
        .setAutoCollectDependencies(true)  // Enable dependency tracking
        .setAutoCollectPerformance(true, true)  // More comprehensive performance monitoring
        .setAutoDependencyCorrelation(true)
        .setSendLiveMetrics(true)
        .setDistributedTracingMode(appInsights.DistributedTracingModes.AI_AND_W3C)  // Enable W3C tracing for better Azure integration
        .start();
    
    // Set cloud role name to better identify this service in the Application Insights portal
    if (appInsights.defaultClient) {
        appInsights.defaultClient.context.tags[appInsights.defaultClient.context.keys.cloudRole] = "hebreway-server";
        console.log('App Insights initialized successfully');
    }
}

export const enrichAppInsightsWithEventData: Handle = async ({ event, resolve }) => {
    if (appInsights.defaultClient && event?.locals?.user) {
        appInsights.defaultClient.addTelemetryProcessor((envelope: TelemetryItem) => {
            if (envelope) {
                envelope.tags['ai.user.id'] = event.locals.user.id;
                envelope.tags['ai.user.authUserId'] = event.locals.user.email;
            }
        });
    }

    return resolve(event);
};

export const optionsHandler: Handle = async ({ event, resolve }) => {
    // Handle preflight OPTIONS requests
    if (event.request.method === 'OPTIONS') {
        return new Response(null, {
            status: 200,
            headers: {
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS, PATCH, DELETE, POST, PUT',
                'Access-Control-Allow-Headers': 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, request-id, request-context',
                'Access-Control-Allow-Credentials': 'true',
                'X-Content-Type-Options': 'nosniff',
                'X-XSS-Protection': '1; mode=block'
            },
        });
    }

    // Process the request normally but add CORS headers to the response
    const response = await resolve(event);
    
    // Clone the response to add CORS headers
    const newResponse = new Response(response.body, response);
    
    // Add CORS headers to all responses, not just OPTIONS
    newResponse.headers.set('Access-Control-Allow-Origin', '*');
    newResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS, PATCH, DELETE, POST, PUT');
    newResponse.headers.set('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, request-id, request-context');
    newResponse.headers.set('Access-Control-Allow-Credentials', 'true');
    newResponse.headers.set('X-Content-Type-Options', 'nosniff');
    newResponse.headers.set('X-XSS-Protection', '1; mode=block');
    
    return newResponse;
};

const pipeline = [enrichAppInsightsWithEventData, optionsHandler, authenticationMiddleware, authorizationMiddleware, dataMiddleware]

export const handle = sequence(...pipeline)

export const handleError = ({ error, event }) => {
    if (isProd && appInsights.defaultClient) {
        appInsights.defaultClient.trackException({
            exception: error as Error,
            properties: {
                url: event.url.href,
                method: event.request.method,
                user: event.locals.user
            },
        });
    }
    console.error(error);
};
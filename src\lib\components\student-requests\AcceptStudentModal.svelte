<script lang="ts">
    import {getModalStore} from '@skeletonlabs/skeleton';
    import {deserialize} from '$app/forms';
    import {invalidate} from '$app/navigation';
    import {t} from '$lib/i18n/config';
    import {StudentAcceptModalState} from '$lib/state/student-accept-state';
    import {LevelFilter, NotificationType} from '$common/models/enums';
    import {onDestroy, onMount} from 'svelte';
    import StudentModalForm from '$components/students/StudentModalForm.svelte';
    import GroupSettingsForm from '$components/student-requests/GroupSettingsForm.svelte';
    import {StudentApiClient} from '$lib/core/api-clients/student-api-client';
    import GroupsHistory from '$components/students/GroupsHistory.svelte';
    import {GroupHistoryState} from '$lib/state/group-history-state';
    import {loadingWrap} from '$lib/common/utils';
    import {StudentEditModalState} from '$lib/state/student-edit-state';
    import NotificationStore from "$lib/state/notification-state";
    import {differenceInMilliseconds} from "date-fns";

    let modalErrors:
        | {
        field: string | number;
        message: string;
    }[]
        | undefined;
    const modalStore = getModalStore();

    const formData = $StudentAcceptModalState;
    const studentLevel = LevelFilter[formData.groupLevel[0]];
    const studentLangGroup =
        formData.groupLang.toLowerCase() === 'english' ? 'EN' : 'RU';
    let sortedArrayGroups = [];
    let studentId: string;
    let isValuesFromGroupSettingFormEmpty = true;
    $: isUnfinishedGroupExists = $GroupHistoryState?.groupsHistory?.some(
        (element) => !element.dateEndActual
    );
    $: disableSave = isValuesFromGroupSettingFormEmpty || isUnfinishedGroupExists;

    const checkValueFromGroupSettingForm = (e: CustomEvent) => {
        const {learnStartDate, groupId, dateStartTasks} = e.detail;
        isValuesFromGroupSettingFormEmpty = !(learnStartDate && groupId && dateStartTasks);
    };

    const findDifferentField = (obj1, obj2) => {
        const resultObj = {};
        const verifiableKeys = [
            'tz',
            'email',
            'firstname',
            'lastname',
            'phone',
            'dob',
            'whatsapp',
            'comment'
        ];
        for (const key in obj1) {
            if (
                obj2.hasOwnProperty(key) &&
                obj1[key] !== obj2[key] &&
                obj1[key] &&
                verifiableKeys.includes(key)
            ) {
                resultObj[key] = obj2[key];
            }
        }
        return resultObj;
    };

    onMount(async () => {
        const data = await new StudentApiClient().getStudentByTZ($StudentAcceptModalState.tz);
        $GroupHistoryState = {};
        if (data) {
            studentId = data?.id;
            $StudentEditModalState.id = studentId;
            $GroupHistoryState.previousModalName = $t('students.requests.modal.previousmodalname');
            $StudentAcceptModalState.studentPastData = {
                ...findDifferentField(formData, data)
            };
        }
        sortedArrayGroups = groupSortingByStudentLevelAndLang($StudentAcceptModalState.groups);
    });

    onDestroy(() => (studentId = ''));


    const sortingArrayByProximityToDate = (array, date) => {
        return array.sort((a, b) => {
            const dateA = new Date(a.dateStart);
            const dateB = new Date(b.dateStart);
            const diffA = Math.abs(dateA - new Date(date));
            const diffB = Math.abs(dateB - new Date(date));

            return diffA - diffB;
        });
    };

    const groupSortingByStudentLevelAndLang = (arrayOfGroups) => {
        let arrayWithStudentLevelAndLang = [];
        const arrayWithDifferentStudentLevelAndLang = arrayOfGroups.filter((element) => {
            if (element.level !== studentLevel || element.lang !== studentLangGroup) {
                return element;
            } else {
                arrayWithStudentLevelAndLang.push(element);
            }
        });
        return [
            ...sortingArrayByProximityToDate(
                arrayWithStudentLevelAndLang,
                $StudentAcceptModalState?.groupStartDate
            ),
            ...sortingArrayByProximityToDate(
                arrayWithDifferentStudentLevelAndLang,
                $StudentAcceptModalState?.groupStartDate
            )
        ];
    };

    $: groupOptions = sortedArrayGroups?.filter((g)=>g.isActive)?.map((g) => {
        return {value: g.id, displayValue: g.name};
    });

    async function handleSubmit(event) {
        const data = new FormData(this);

        const response = await fetch(this.action, {
            method: 'POST',
            body: data,
            headers: {
                'x-sveltekit-action': 'true'
            }
        });

        const result = deserialize(await response.text());
        if (result.type === 'failure') {
            modalErrors = result.data.errors;
        } else {
            NotificationStore.push(
                {
                    type: NotificationType.success,
                    message: t.get('students.requests.notifications.handle.success')
                }, 5);
            await loadingWrap(async () => {
                await invalidate('load:students');
            });
            modalStore.close();
        }
    }
</script>

{#if $modalStore[0]}
    <div class="modal max-h-[600px] overflow-hidden card py-5 px-5 w-1/2 shadow-xl space-y-4">
        <header class="text-xl font-bold">{$t('students.requests.modal.studentTitle')}</header>

        <StudentModalForm
                action="handleRequest"
                {studentId}
                disableSave={disableSave || isUnfinishedGroupExists}
                {handleSubmit}
                {modalErrors}
                {formData}
        >
            <svelte:fragment slot="groupSettingsForm">
                <GroupSettingsForm
                        on:changeFieldValue={checkValueFromGroupSettingForm}
                        {modalErrors}
                        {groupOptions}
                        {formData}
                />
                {#if studentId}
                    <GroupsHistory {studentId} isAcceptingModal={true}/>
                    {#if isUnfinishedGroupExists}
                        <span class="text-warning-500">{$t('students.requests.modal.incompletegroup')}</span>
                    {/if}
                {/if}
            </svelte:fragment>
        </StudentModalForm>
    </div>
{/if}

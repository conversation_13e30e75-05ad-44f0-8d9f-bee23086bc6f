<script lang="ts">
    import {getModalStore} from "@skeletonlabs/skeleton";
    import {t} from "$lib/i18n/config";
    import {Constants} from "$api/core/constants";
    import OnlyForRole from "$components/common/OnlyForRole.svelte";
    import {Permission} from "$common/models/enums";

    const modalStore = getModalStore();


    let isFavorite = $modalStore[0]?.meta.isFavorite;
    let role = $modalStore[0]?.meta.role;


    const handleSubmitModal = (action: 'create' | 'saveThis' | 'cancel') => {
        switch (action) {
            case "create":
                $modalStore[0].response('create');
                modalStore.close();
                break;
            case 'saveThis':
                $modalStore[0].response('saveThis');
                modalStore.close();
                break;
            case "cancel":
                $modalStore[0].response('close');
                modalStore.close();
                break;
        }
    }

</script>


<div dir="auto"
     class="flex flex-col gap-10 rounded-xl modal card  sm:w-full md:w-2/3 lg:w-2/5 shadow-xl space-y-4 p-5 ">
    <div class="flex flex-col gap-4">

        <h1 class="text-3xl font-bold">
            <OnlyForRole permission={Permission.editFavorites} additionalCondition={isFavorite} bypassCondition={!isFavorite}>
                <svelte:fragment slot="authorized">{$t('sentences.modalTaskSentencesNotFavorite.title')}</svelte:fragment>
                <svelte:fragment slot="forbidden">{$t('sentences.modalTaskSentencesFavorite.title')}</svelte:fragment>
            </OnlyForRole>
        </h1>
        <p class="text-lg">
            <OnlyForRole permission={Permission.editFavorites} additionalCondition={isFavorite} bypassCondition={!isFavorite}>
                <svelte:fragment slot="authorized">{$t('sentences.modalTaskSentencesNotFavorite.body')}</svelte:fragment>
                <svelte:fragment slot="forbidden">{$t('sentences.modalTaskSentencesFavorite.body')}</svelte:fragment>
            </OnlyForRole>
        </p>
    </div>
    <div class="w-full flex justify-between">
        <div class="flex gap-2">
            <button class="btn variant-filled rounded-none" on:click={()=>handleSubmitModal('create')}>
                {$t('sentences.modalTaskSentencesNotFavorite.buttonTextConfirm')}
            </button>
            <OnlyForRole permission={Permission.editFavorites} additionalCondition={isFavorite} bypassCondition={!isFavorite}>
                <button class="btn variant-filled rounded-none" on:click={()=>handleSubmitModal('saveThis')}>
                    {$t('sentences.modalTaskSentencesNotFavorite.buttonTextCancel')}
                </button>
            </OnlyForRole>
        </div>
        <button class="btn variant-ghost-surface rounded-none" on:click={()=>handleSubmitModal('cancel')}>
            { $t('sentences.modalTaskSentencesFavorite.buttonTextCancel')}
        </button>
    </div>
</div>

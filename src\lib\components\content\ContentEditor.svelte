<script lang="ts">
    import jQuery from 'jquery';
    import '../../../../node_modules/trumbowyg/dist/ui/trumbowyg.min.css';
    import '../../../../node_modules/trumbowyg/dist/plugins/colors/ui/trumbowyg.colors.min.css';
    import '../../../../node_modules/trumbowyg/dist/plugins/table/ui/trumbowyg.table.min.css';
    import {createEventDispatcher, onMount} from 'svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {ContentApiClient} from '$lib/core/api-clients/content-api.cient';
    import {getCurrentDateTime} from '$lib/common/utils';
    import type {FileDto} from '$common/models/dtos/task.dto';
    import ContentEditorSkeleton from '$components/content/ContentEditorSkeleton.svelte';
    import {browser} from "$app/environment";
    import {generateGuid} from "$common/core/utils";

    const dispatcher = createEventDispatcher();

    export let readMode = false;
    export let content = '';
    export let id: string;
    let isLoading = true;


    let div;
    let stream;
    let mediaRecorder: MediaRecorder | null = null;
    let audioBlobToSave: Blob;
    let isRunning = false;
    let interval;
    let time = 0;
    let stopRecordingButton: HTMLElement;
    let startRecordingButton: HTMLElement;
    let groupButton: HTMLElement;
    let timer;
    let timerElement: HTMLElement;

    const iconPaperClip = `<svg style="fill:none;width: 18px;" xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-paperclip" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                 <path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5"></path>
				</svg>`;

    const iconPlayerRecord = `<svg style="fill:none;width: 18px;" xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-player-record-filled" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M8 5.072a8 8 0 1 1 -3.995 7.213l-.005 -.285l.005 -.285a8 8 0 0 1 3.995 -6.643z" stroke-width="0" fill="currentColor"></path>
				</svg>`;

    const iconPlayerStop = `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-player-stop-filled" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
             <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
             <path d="M17 4h-10a3 3 0 0 0 -3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3 -3v-10a3 3 0 0 0 -3 -3z" stroke-width="0" fill="currentColor"></path>
			</svg>`;

    const uploadFile = async (file: File | Blob) => {
        let formData = fileToFormData(file);
        const data = await new ContentApiClient().uploadFile(formData);
        const newHtml = generatingHtml(data.file);
        let oldData = div.innerHTML;
        window.jQuery(div).trumbowyg('html', `${oldData} ${newHtml}`);
    };

    const fileToFormData = (file: File | Blob) => {
        const formData = new FormData();

        file.type.includes('audio/webm')
            ? formData.append('file', file, `${id}_${getCurrentDateTime()}_${generateGuid()}.mp3`)
            : formData.append('file', file);

        return formData;
    };

    // <audio preload="auto" class="w-full" controls src=${encodeURI(file?.url ?? '')}></audio>

    const generatingHtml = (file: FileDto) => {
        const audioFormat = /audio\/webm/;
        const imageFormat = /^image\//;
        const docFormat =
            /text\/plain|application\/rtf|application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/;
        if (audioFormat.test(file.type)) {
            return `
				 <p class="w-full">
                    <audio preload="auto" class="w-full" controls src=${encodeURI(file?.url ?? '')}></audio>
                 </p>
                 <p>\u00A0</p>
					`;
        } else if (imageFormat.test(file.type)) {
            return `
					<p>
						<img class="inline"  src=${encodeURI(file?.url ?? '')} alt={data.file.name}>
					</p>
						`;
        } else if (docFormat.test(file.type)) {
            return `
					<span >
						 <a class="doc" href=${encodeURI(file?.url ?? '')}  target="_blank">
						 <svg style="fill:none;width: 20px;" xmlns="http://www.w3.org/2000/svg" class="inline-block icon icon-tabler icon-tabler-paperclip" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
			                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
			                    <path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5"></path>
							</svg>
							<span class="text-lg" >${file?.title}</span>
						 </a>
					</span>
			`;
        } else {
            return `
					<span >
						 <a class="file" href=${encodeURI(file?.url ?? '')}  target="_blank">
						 <svg style="fill:none;width: 20px;" xmlns="http://www.w3.org/2000/svg" class="inline-block icon icon-tabler icon-tabler-paperclip" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
			                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
			                    <path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 3 3l6.5 -6.5a3 3 0 0 0 -6 -6l-6.5 6.5a4.5 4.5 0 0 0 9 9l6.5 -6.5"></path>
							</svg>
							<span class="text-lg" >${file?.title}</span>
						 </a>
					</span>
					`;
        }
    };

    const addTimer = () => {
        groupButton = document.querySelector('.trumbowyg-button-group');
        timerElement = document.createElement('div');
        timerElement.classList.add('timer', 'flex', 'items-center', 'justify-center');
        document.querySelector('.trumbowyg-button-group')?.appendChild(timerElement);
        startTimer();
    };

    const formatTime = (timeInSeconds: number) => {
        const minutes = Math.floor(timeInSeconds / 60)
            .toString()
            .padStart(2, '0');
        const seconds = (timeInSeconds % 60).toString().padStart(2, '0');
        return `${minutes}:${seconds}`;
    };

    function startTimer() {
        timer = setInterval(() => {
            time += 1;
            document.querySelector('.timer').textContent = formatTime(time);
        }, 1000);
    }

    const removeTimer = () => {
        groupButton.removeChild(timerElement);
        clearInterval(timer);
    };

    const startRecording = async () => {
        stream = await navigator.mediaDevices.getUserMedia({audio: true});
        mediaRecorder = new MediaRecorder(stream);
        mediaRecorder.addEventListener('dataavailable', ({data}) => {
            audioBlobToSave = data;
            uploadFile(audioBlobToSave);
        });
        mediaRecorder.start();
        isRunning = true;
        addTimer();
        stopRecordingButton.style.display = 'block';
        startRecordingButton.style.display = 'none';
    };


    const addParagraphAfterTag = (mutationsList, tag: string) => {
        mutationsList.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.tagName && node.tagName === tag) {
                        const paragraph = document.createElement('p');
                        paragraph.appendChild(document.createTextNode('\u00A0'));
                        const paragraph2 = document.createElement('p');
                        paragraph2.appendChild(document.createTextNode('\u00A0'));
                        node.insertAdjacentElement('beforebegin', paragraph);
                        node.insertAdjacentElement('afterend', paragraph2);
                    }
                });
            }
        });
    };


    const stopRecording = () => {
        if (mediaRecorder && isRunning) {
            mediaRecorder.stop();
            clearInterval(interval);
            time = 0;
            isRunning = false;
            stopRecordingButton.style.display = 'none';
            startRecordingButton.style.display = 'block';
            removeTimer();
        }
    };

    const setAllElementDirAuto = () => {
        const elements = div.querySelectorAll('*');
        for (let i = 0; i < elements.length; i++) {
            elements[i].dir = 'auto';
        }
    };

    let clickLinkHandler = (event) => {
        event.preventDefault();
        const url = event.currentTarget.getAttribute('href');
        window.open(url, '_blank');
    };

    const importDependency = async () => {
        await import(
            '../../../../node_modules/trumbowyg/dist/plugins/noembed/trumbowyg.noembed.min.js'
            );
        await import('../../../../node_modules/trumbowyg/plugins/insertaudio/trumbowyg.insertaudio.js');
        await import(
            '../../../../node_modules/trumbowyg/dist/plugins/fontfamily/trumbowyg.fontfamily.min.js'
            );
        await import('../../../../node_modules/trumbowyg/dist/plugins/fontsize/trumbowyg.fontsize.min');
        await import(
            '../../../../node_modules/trumbowyg/dist/plugins/allowtagsfrompaste/trumbowyg.allowtagsfrompaste.min'
            );
        await import('../../../../node_modules/trumbowyg/dist/plugins/base64/trumbowyg.base64.min');
        await import('../../../../node_modules/trumbowyg/dist/plugins/colors/trumbowyg.colors.min');
        await import('../../../../node_modules/trumbowyg/dist/plugins/table/trumbowyg.table.min');
        await import('../../../../node_modules/jquery-resizable-dom/dist/jquery-resizable.min');
        await import('../../../../node_modules/trumbowyg/dist/plugins/resizimg/trumbowyg.resizimg.min');
        await import('../../../../node_modules/trumbowyg/dist/plugins/lineheight/trumbowyg.lineheight.min')
    };

    onMount(async () => {
        if (browser) {
            if (!window.jQuery) window.jQuery = jQuery;

            await import('trumbowyg');
            await import('../../../../node_modules/trumbowyg/dist/langs/he');
            await importDependency();
            isLoading = false;

            window.jQuery(div).trumbowyg({
                tagClasses: {
                    a: 'link',
                    table: 'table contentTable',
                },
                lang: 'he',
                resetCss: true,
                disabled: readMode,
                removeformatPasted: true,
                btnsDef: {
                    addfile: {
                        fn: () => document.getElementById('myFile').click(),
                        tag: 'Add file',
                        title: 'Add file',
                        text: iconPaperClip,
                        isSupported: function () {
                            return true;
                        },
                        key: 'K',
                        param: '',
                        forceCSS: false,
                        class: '',
                        hasIcon: false
                    },
                    addimage: {
                        fn: () => document.getElementById('myFile').click(),
                        tag: 'Add image',
                        title: 'Add file',
                        text: 'Add image',
                        key: 'K',
                        param: '',
                        forceCSS: false,
                        class: '',
                        ico: 'insertImage',
                        hasIcon: true
                    },
                    startrecording: {
                        fn: () => startRecording(),
                        tag: 'Start recording',
                        title: 'Start recording',
                        text: iconPlayerRecord,
                        isSupported: function () {
                            return true;
                        },
                        forceCSS: false,
                        hasIcon: false
                    },
                    stoprecording: {
                        fn: () => stopRecording(),
                        tag: 'Stop recording',
                        title: 'Stop recording',
                        text: iconPlayerStop,
                        isSupported: function () {
                            return true;
                        },
                        forceCss: false,
                        hasIcon: false
                    },
                    align: {
                        dropdown: ['justifyLeft', 'justifyCenter', 'justifyRight'],
                        ico: 'justifyLeft'
                    }
                },
                btns: [
                    ['startrecording', 'stoprecording'],
                    ['table', 'tableCellBackgroundColor', 'tableBorderColor'],
                    ['formatting'],
                    ['foreColor', 'backColor'],
                    'align',
                    'image',
                    ['link', 'addfile', 'addimage'],
                    'fontfamily',
                    'noembed',
                    'insertAudio',
                    'fontsize',
                    ['lineheight']
                ],
                plugins: {
                    fontfamily: {
                        fontList: [
                            {name: 'DanaYadAlefAlefAlef', family: 'DanaYadAlefAlefAlef'},
                            {name: 'Arial', family: 'Arial, Helvetica, sans-serif'},
                            {name: 'Arial Black', family: 'Arial Black, Gadget, sans-serif'},
                            {name: 'Comic Sans', family: 'Comic Sans MS, Textile, cursive, sans-serif'},
                            {name: 'Courier New', family: 'Courier New, Courier, monospace'},
                            {name: 'Georgia', family: 'Georgia, serif'},
                            {name: 'Impact', family: 'Impact, Charcoal, sans-serif'},
                            {name: 'Lucida Console', family: 'Lucida Console, Monaco, monospace'},
                            {name: 'Lucida Sans', family: 'Lucida Sans Uncide, Lucida Grande, sans-serif'},
                            {name: 'Palatino', family: 'Palatino Linotype, Book Antiqua, Palatino, serif'},
                            {name: 'Tahoma', family: 'Tahoma, Geneva, sans-serif'},
                            {name: 'Times New Roman', family: 'Times New Roman, Times, serif'},
                            {name: 'Trebuchet', family: 'Trebuchet MS, Helvetica, sans-serif'},
                            {name: 'Verdana', family: 'Verdana, Geneva, sans-serif'}
                        ]
                    },
                    resizimg: {
                        minSize: 200,
                        step: 16
                    },
                    fontsize: {
                        sizeList: ['10px', '14px', '16px', '24px', '36px', '48px'],
                        allowCustomSize: false
                    }
                }
            });
            window.jQuery(div).trumbowyg('html', content);
            div.dir = 'auto';
            let editorKeyDownListener = div.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    setAllElementDirAuto();
                }
            });
            const observerVideo = new MutationObserver((mutationsList) => addParagraphAfterTag(mutationsList, 'IFRAME'));
            observerVideo.observe(div, {childList: true, subtree: true})
            const observerTable = new MutationObserver((mutationsList) => addParagraphAfterTag(mutationsList, 'TABLE'));
            observerTable.observe(div, {childList: true, subtree: true})
            const links = div.getElementsByTagName('a');
            for (let i = 0; i < links.length; i++) {
                links[i].addEventListener('click', clickLinkHandler);
            }
            stopRecordingButton = document.querySelector('.trumbowyg-stoprecording-button ');
            startRecordingButton = document.querySelector('.trumbowyg-startrecording-button');
            stopRecordingButton.style.display = 'none';
            if (readMode) {
                let buttonPane = document.getElementsByClassName('trumbowyg-button-pane');
                let files = document.getElementsByClassName('file');
                let doc = document.getElementsByClassName('doc');

                for (let item of buttonPane) {
                    const element = item as HTMLElement;
                    element.style.display = 'none';
                }
                for (let item of files) {
                    const element = item as HTMLElement;
                    const href = element.getAttribute('href');
                    element.addEventListener('click', (e) => {
                        window.open(href, '_blank');
                    });
                }
                for (let item of doc) {
                    const element = item as HTMLElement;
                    element.addEventListener('click', () => {
                        const fileUrl = element.getAttribute('href');
                        const fileName = fileUrl.split('/').pop() || 'document';
                        let downloadLink = document.createElement('a');
                        downloadLink.href = fileUrl;
                        downloadLink.download = fileName;
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    });
                }
            }
            return () => {
                const links = div.getElementsByTagName('a');
                links.forEach(link => {
                    link.removeEventListener('click', clickLinkHandler);
                });
                removeEventListener('keydown', editorKeyDownListener);
                observerVideo.disconnect()
            };
        }
    });
</script>

{@html '<script src="https://cdnjs.cloudflare.com/ajax/libs/Trumbowyg/2.27.3/trumbowyg.min.js"></script>'}

{#if isLoading}
    <div class="flex w-full">
        <ContentEditorSkeleton/>
    </div>
{/if}

<div class="w-full h-full {isLoading ? 'hidden' : ''}">
    <div class="">
        <main class="flex flex-col">
            <div
                    class="w-full h-full "
                    style="min-height:500px!important; background: none !important;border:none!important;padding:0 !important; "
                    bind:this={div}
            />
            {#if !readMode}
                <div class=" w-40 p-5 self-end">
                    <BaseButton
                            on:click={() =>
							dispatcher('contentUpdated', { content: window.jQuery(div).trumbowyg('html') })}
                    >
                        Save
                    </BaseButton>
                </div>
            {/if}
        </main>
    </div>

    <input
            on:change={(e) => {
			if (e.currentTarget.files[0]) {
				uploadFile(e.currentTarget.files[0]);
			}
		}}
            type="file"
            id="myFile"
            style="display: none"
    />
</div>


<style lang="scss">

  :global(.contentTable ) {
    margin: 0 auto;
    width: 90%;
    border-radius: 0 !important;
    direction: ltr;
  }

</style>




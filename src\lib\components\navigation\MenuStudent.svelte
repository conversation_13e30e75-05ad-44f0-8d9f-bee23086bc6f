<script lang="ts">
    import {t} from '$lib/i18n/config';
    import {getDrawerStore, LightSwitch, ListBox, ListBoxItem} from '@skeletonlabs/skeleton';
    import LanguageSelect from '../common/LanguageSelect.svelte';
    import {page} from '$app/stores';
    import {IconHome, IconLogout2} from '@tabler/icons-svelte';
    import BaseButton from '$components/common/BaseButton.svelte';
    import {HebrewFontState} from '$lib/state/hebrew-font-state';
    import BaseSwitchHebrew from '$components/common/BaseSwitchHebrew.svelte';

    const drawerStore = getDrawerStore();

    const menuItems = [
        {translationKey: 'practice', url: '/practice', prefetch: false},
        {translationKey: 'notifications', url: '/notifications', prefetch: true},
        {translationKey: 'about', url: '/about', prefetch: true},
    ];
    $: classesActive = (href: string) =>
        href === $page.url.pathname ? '!bg-primary-300 dark:!bg-transparent dark:border-2 dark:border-primary-500  font-bold' : '';
</script>

<div id="aside-bar" class="p-2 h-full bg-surface-200-700-token shadow-lg !rounded-none w-full">
    <ListBox class="h-full">
        <div class="flex flex-col h-full">
            {#if $page?.data?.user?.firstname?.length > 1}
                <div class="mt-5 mb-5 break-words w-full xs:flex flex-wrap lg:hidden px-3" dir="auto">
                    <pre class="mx-2">👋</pre>
                    <pre>{$page?.data?.user?.firstname} {$page?.data?.user?.lastname}</pre>
                </div>
                <div class="sm:flex lg:hidden">
                    <hr/>
                </div>
            {/if}
            <div class="sm:flex lg:hidden w-full flex-row justify-center gap-x-2 my-5" dir="ltr">
                <LightSwitch width="w-[70px]" class="z-[999]" dir="ltr"/>
                <BaseSwitchHebrew bind:state={$HebrewFontState}/>
            </div>
            <div class="lg:hidden md:flex">
                <hr/>
            </div>
            <div class="flex-1 flex flex-col gap-1 mt-5">
                <a href="/home" on:click={() => drawerStore.close()}>
                    <ListBoxItem
                            class="!p-2 bg-transparent font-semibold dark:!bg-transparent border-2 border-transparent  !text-primary hover:bg-primary-300 dark:hover:border-2 dark:hover:border-primary-500 {classesActive(
							'/home'
						)}"
                            rounded="rounded"
                    >
                        <span class="flex flex-row gap-2 items-center">
                            <IconHome size={22} style="display: initial"/>
                            <span class="text-base">{$t(`common.menu.home`)}</span>
                        </span>
                    </ListBoxItem>
                </a>

                {#each menuItems as menuItem}
                    <a href={menuItem.url} on:click={() => drawerStore.close()}>
                        <ListBoxItem
                                class="!p-2 bg-transparent  dark:!bg-transparent font-semibold !text-primary  hover:bg-primary-300 border-2 border-transparent dark:hover:border-2 dark:hover:border-primary-500 {classesActive(
								menuItem.url
							)}"
                                rounded="rounded"
                        >
                            <span class="text-base">{$t(`common.menu.${menuItem.translationKey}`)}</span>
                        </ListBoxItem>
                    </a>
                {/each}
            </div>
            <div class="flex flex-col gap-2">
                <LanguageSelect/>
                <hr class="my-5"/>
                <div class="">
                    <form class="self-end" method="post" action="/api/logOut">
                        <BaseButton type="submit" className=" rounded p-0 h-7 w-full bg-none">
							<span>
								<IconLogout2 size={20}/>
							</span>
                        </BaseButton>
                    </form>
                    <div class="flex flex-col  w-full items-center justify-center text-xs ">
                        <p>All rights reserved</p>
                        <p> © מרשת שרה</p>
                    </div>
                </div>
            </div>
        </div>
    </ListBox>
</div>

<style>
</style>

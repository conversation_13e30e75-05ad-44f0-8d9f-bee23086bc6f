import type {AudioFilter, LevelFilter} from '$common/models/enums';
import type {BaseFilterDto} from '$common/models/filters/base-filter.dto';

export interface SentenceFilterAfterFulltextDto extends SentenceFilterDto {
    ids: string[];
}
export interface SentenceFilterDto extends BaseFilterDto {
    audio: AudioFilter | -1;
    level: LevelFilter | -1;
    lang: 'EN' | 'RU' | -1;
    search: string;
    isStrict: boolean;
    isFavorite: boolean;
    updatedBy: string;
}

export const initialSentenceFilter: SentenceFilterDto = {
    audio: -1,
    level: -1,
    lang: 'RU',
    search: '',
    isStrict: false,
    isFavorite: false,
    take: 25,
    skip: 0,
    updatedBy: ''
}
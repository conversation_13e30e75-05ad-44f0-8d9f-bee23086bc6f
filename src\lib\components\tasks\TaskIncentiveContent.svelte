<script lang="ts">
    import type {IncentiveContentDto} from '$common/models/dtos/incentive-content.dto';
    import {IconPlus} from '@tabler/icons-svelte';
    import {t} from '$lib/i18n/config';
    import BaseButton from '$components/common/BaseButton.svelte';
    import TaskIncentiveContentItem from '$components/tasks/TaskIncentiveContentItem.svelte';
    import {CurrentEditableState} from '$lib/state/task-current-editable-state';
    import {page} from '$app/stores';

    export let incentiveContent: IncentiveContentDto[] = [];
    const {id}: { id: string } = $page.params;

    const addNew = () => {
        const newObj = {
            incentive_content: {
                isNew: true,
                value: '',
                name: '',
                id: '',
                comment: ''
            },
            scoreThreshold: 0,
            incentiveContentId: '',
            isActive: false,
            taskId: id
        };
        $CurrentEditableState.task_incentive_content = $CurrentEditableState.task_incentive_content
            ? [newObj, ...$CurrentEditableState.task_incentive_content]
            : [newObj];
    };

    const deleteItem = (index: number) => {
        const newArray = [...$CurrentEditableState.task_incentive_content];
        newArray.splice(index, 1);
        $CurrentEditableState.task_incentive_content = [...newArray];
    };

</script>

{#if incentiveContent && incentiveContent.length > 0}
    <div class="flex flex-col gap-10">
        <div class="flex justify-center text-2xl">
            {$t('tasks.additionalTask.incentiveContent.title')}
        </div>
        <div class=" variant-glass-primary flex flex-col p-5">
            <div>
                <BaseButton on:click={addNew} size="sm">
                    <IconPlus/>
                </BaseButton>
            </div>

            <div class="flex flex-col gap-5 mt-5">
                {#if $CurrentEditableState.task_incentive_content && $CurrentEditableState.task_incentive_content.length > 0}
                    {#each $CurrentEditableState.task_incentive_content as item, i}
                        {@const contentValue = item.incentive_content.value ? 'value' : 'page_content'}
                        <TaskIncentiveContentItem
                                {incentiveContent}
                                index={i}
                                isLexical={!!item.incentive_content.value}
                                bind:incentiveContentId={item.incentiveContentId}
                                bind:selectValue={item.incentive_content.id}
                                bind:checkedValue={item.isActive}
                                bind:scoreThreshold={item.scoreThreshold}
                                bind:content={item.incentive_content[contentValue]}
                                {deleteItem}
                        />
                        {#if $CurrentEditableState.task_incentive_content.length - 1 !== i}
                            <hr/>
                        {/if}
                    {/each}
                {/if}
            </div>
        </div>
    </div>
{:else}
    <p>There's no incentive content, add one <a href="/incentive">here</a></p>
{/if}

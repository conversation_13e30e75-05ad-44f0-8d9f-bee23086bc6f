<script lang="ts">
    import {locale, t} from "$lib/i18n/config";
    import type {AboutContentsDto} from "$common/models/dtos/AboutContents.dto";
    import ContentEditor from "$components/content/ContentEditor.svelte";


    export let data;
    $:contents = data.aboutContents as AboutContentsDto[];


    const getContentByLang = () => {
        return contents && contents?.find((content: AboutContentsDto) => content?.lang === $locale)?.value;
    }
</script>

<div class="h-[calc(100vh-85px)] overflow-hidden flex flex-col px-6 ">
    <div class="mt-3">
        <h1 class="title mb-1 font-medium text-xl">
            {$t('about.title')}
        </h1>
    </div>

    <div dir="auto" class="mt-3 overflow-y-auto overflow-x-auto ">
        {#key $locale}
            <ContentEditor readMode={true} content={getContentByLang()}/>
        {/key}
    </div>
</div>

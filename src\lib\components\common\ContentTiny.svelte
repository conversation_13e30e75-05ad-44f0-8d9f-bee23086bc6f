<script lang="ts">
    import Editor from '@tinymce/tinymce-svelte';
    import LoadingSpinner2 from "$components/common/LoadingSpinner2.svelte";
    import {onMount} from "svelte";
    import {browser} from "$app/environment";
    import { ContentApiClient } from '$lib/core/api-clients/content-api.cient';

    export let content;

    export let isReadMode;

    let editor;

    let isLoading = true;

    const openLink = (url, target) => {
        if (target !== '_blank') {
            document.location.href = url;

            return;
        }

        const link = document.createElement('a');
        link.href = url;
        link.target = target;
        link.rel = 'noopener';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    onMount(() => {
        if (browser) {
            content = content.replace(/style="[^"]*padding-right: 80px;?"/g, ''); // TinyMCE removes padding-right:80px for content full width
        }
    })

    let conf = {
        height: 'auto',
        min_height: 100,
        menubar: !isReadMode,
        plugins: 'preview autoresize importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media template codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists charmap emoticons',
        toolbar: isReadMode ? '' : 'undo redo | ltr rtl| bold italic underline strikethrough | fontfamily fontsize blocks | alignleft aligncenter alignright | outdent indent |  numlist bullist | forecolor backcolor removeformat | pagebreak | charmap emoticons | fullscreen  preview save print | insertfile image media template link anchor codesample ',
        contextmenu: isReadMode ? '' : "link image imagetools table",
        directionality: 'rtl',
        editable_root: !isReadMode,
        statusbar: false,
        setup: function (editor) {
            editor.on('init', () => {
                if (isReadMode) {
                    Array.from(editor.getDoc().querySelectorAll('a')).map((el) => {
                      el.addEventListener('click', () => {
                        const href = el.getAttribute('href');
                        const target = el.getAttribute('target');
                        openLink(href, target);
                      });
                });
                }
            });
        },
        callbacks: (e) => {
            console.log('---', e)
        },
        content_style: `
            .mce-shim { display: none; }
            .mce-object-iframe { width: 100% !important; } /* !important to avoid resize, always 100% width */
            .mce-preview-object { margin: 0 !important; border: 0 none !important; }
            img, iframe {
                width: 100% !important; /* !important to avoid resize, always 100% width */
                height: auto;
                display: block;
            }
            iframe {
                aspect-ratio: 2 !important;
                width: 100% !important;
                height: auto !important;
            }
        `,
        file_picker_callback: (cb, value, meta) => {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            if (meta.filetype === 'image') {
                input.setAttribute('accept', 'image/*');
            } else if (meta.filetype === 'media') {
                input.setAttribute('accept', 'video/*,audio/*');
            } else if (meta.filetype === 'file') {
                input.setAttribute('accept', '*');
            }

            input.addEventListener('change', (e) => {
                const file = e.target.files[0];

                // Validate file type for images
                if (meta.filetype === 'image' && !file.type.startsWith('image/')) {
                    alert('Please select a valid image file.');
                    return;
                }
                
                const reader = new FileReader();
                reader.addEventListener('load', async () => {
                    let formData = fileToFormData(file);
                    const data = await new ContentApiClient().uploadFile(formData);

                    cb(data.file.url, { title: data.file.name });
                });
                reader.readAsDataURL(file);
            });

            input.click();
        }
    }

    const fileToFormData = (file: File | Blob) => {
        const formData = new FormData();

        formData.append('file', file);

        return formData;
    };
</script>


<div class="w-full h-full"
     style="--isRead-background:{isReadMode ? 'transparent':'#fff'};--isRead-border:{isReadMode ? 'none':'2px solid white'}">
    <div class="{isLoading?'visible':'invisible'}">
        <LoadingSpinner2/>
    </div>
    <div class="{isLoading?'invisible':'visible'}">
        {#if browser}
            <Editor
                    on:loadcontent={()=>{isLoading=false}}
                    bind:this={editor}
                    licenseKey='gpl'
                    scriptSrc='/tinymce/tinymce.min.js'
                    bind:value={content}
                    {conf}
            />
        {/if}
    </div>

</div>


<style lang="scss">
  div {
    :global(.tox) {
      border: var(--isRead-border)
    }


    :global(.tox-editor-header) {
      background: var(--isRead-background) !important;
      padding: 0 !important;
    }

    :global(.tox-edit-area__iframe) {
      background: var(--isRead-background);
    }
  }

  :global(.tox-promotion) {
    display: none;
  }

  :global(.tinymce-wrapper) {
    width: 100%;
  }
</style>
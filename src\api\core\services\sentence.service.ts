import type { GenerateTaskSentenceDto, <PERSON>tenceDto, SentenceInTaskDto } from '$common/models/dtos/sentence.dto';
import type { SentenceFilterAfterFulltextDto, SentenceFilterDto } from '$common/models/filters/sentence-filter.dto';
import type { TableDataDto } from '$common/models/dtos/table-data.dto';
import { db } from '../service-clients/db';
import { uploadAudio } from './audio.service';
import { config } from '../config';
import { mapper } from '$common/core/mapper';

import _ from 'lodash';
import { subDays } from 'date-fns';
import { getSentenceRepository } from '$api/core/service-clients/redis';
import { appToRedisSearchPrefix } from '$api/core/constants';

function removeVowels(text: string) {
	return text.replace(/[\u0591-\u05C7]/g, '');
}

export const getSentences = async (
	filter: SentenceFilterDto
): Promise<TableDataDto<SentenceDto>> => {

	const checkKeysArray = Object.keys(appToRedisSearchPrefix); // Get the keys from the object
	const searchWithRedis = checkKeysArray.includes(config.public.header); // Check if config.public.header is one of the keys
	const sentenceRedisSchema = searchWithRedis ? appToRedisSearchPrefix[config.public.header as keyof typeof appToRedisSearchPrefix] : undefined;console.log('filter', filter);

	try {
		const filterToUse = filter as SentenceFilterAfterFulltextDto;
		if (searchWithRedis && sentenceRedisSchema && filter.search && filter.search.length > 0) {
			const beforeStart = Date.now();
			const sentenceRepository = await getSentenceRepository(sentenceRedisSchema);
			const start = Date.now();
			console.log('clientCreatedTime', start - beforeStart);
			await sentenceRepository.createIndex();
			const indexCreatedTime = Date.now();

			console.log('indexCreatedTime', indexCreatedTime - start);
			console.log('createIndex', Date.now() - start);

			const cleanedSearch = removeVowels(filter.search);

			const redisSearch = filter.isStrict
				? await sentenceRepository.search().where('value').matchExact(cleanedSearch).return.all()
				: await sentenceRepository.search().where('value').match(cleanedSearch).return.all();

			const searchTime = Date.now();
			console.log('searchTime', searchTime - indexCreatedTime);
			console.log('overallSearch', searchTime - start);

			console.log('==='.repeat(50));
			console.log(redisSearch);
			console.log('==='.repeat(50));

			const foundIds = redisSearch.map(x => x.id?.toString()).filter(x => x);
			if (foundIds.length > 0) {
				filterToUse.ids = foundIds;
			}

			console.log('==============');
			console.log(filterToUse);

			return await simpleSentenceSearch(filterToUse);
		}

		return await simpleSentenceSearch(filter);
	} catch (error) {
		return await simpleSentenceSearch(filter);
	}


	// return filter.search
	// 	? await advancedSentenceSearch(filter)
	// 	: await simpleSentenceSearch(filter);
};

const getSentencesByIds = async (ids: string[]): Promise<TableDataDto<SentenceDto>> => {
	const where = {
		id: {
			in: ids
		}
	};

	const count = await db.sentences.count({ where });
	const data = await db.sentences.findMany({
		where,
		include: {
			translations: {
				select: {
					id: true,
					sentenceId: true,
					value: true,
					lang: true
				}
			},
			createdByUser: {
				select: {
					id: true,
					firstname: true,
					lastname: true
				}
			},
			updatedByUser: {
				select: {
					id: true,
					firstname: true,
					lastname: true
				}
			}
		},
		orderBy: {
			createdAt: 'desc'
		}
	});

	const dtos = data.map((x) => mapper<SentenceDto, unknown>(x));

	return { data: dtos, count };
};

const simpleSentenceSearch = async (filter: SentenceFilterAfterFulltextDto | SentenceFilterDto): Promise<TableDataDto<SentenceDto>> => {
	const where = composeSearchWhereClause(filter as any);
	const count = await db.sentences.count({ where });
	const data = await db.sentences.findMany({
		take: filter.take,
		skip: filter.skip,
		where,
		include: {
			translations: {
				select: {
					id: true,
					sentenceId: true,
					value: true,
					lang: true
				}
			},
			createdByUser: {
				select: {
					id: true,
					firstname: true,
					lastname: true
				}
			},
			updatedByUser: {
				select: {
					id: true,
					firstname: true,
					lastname: true
				}
			}
		},
		orderBy: {
			createdAt: 'desc'
		}
	});

	const dtos = data.map((x) => mapper<SentenceDto, unknown>(x));

	return { data: dtos, count };
};

export const getShuffledSentencesForGenerate = async (dto: GenerateTaskSentenceDto) => {

	const dataTaskSentence = await db.task_sentences.findMany({
		select: {
			sentenceId: true
		},
		where: {
			createdAt: {
				lte: new Date(dto.to),
				gte: subDays(new Date(dto.from), 1)
			},
			task: {
				groupId: dto.groupId
			},
			sentence: {
				audioUrl: dto.onlyAudio ? {
					not: null
				} : undefined,
				isFavorite: dto.onlyFav ? true : undefined
			},
			NOT: {
				sentence: dto?.onlyAudio ? {
					audioUrl: null
				} : undefined,
				sentenceId: { in: dto.selectedSentences }
			}
		},
		distinct: ['sentenceId']
	});

	const uniqueSentenceIds = dataTaskSentence.length >= dto.count * 2
		? dataTaskSentence.slice(0, dto.count * 2)
		: dataTaskSentence;

	const dataSentences = await db.sentences.findMany({
		where: {
			id: {
				in: uniqueSentenceIds.map(x => x.sentenceId)
			},
			audioUrl: dto.onlyAudio ? {
				not: null
			} : undefined,
			isFavorite: dto.onlyFav ? true : undefined,
			translations: {
				some: {
					lang: dto.lang,
					NOT: {
						value: ' '
					}
				}
			},
			NOT: dto.onlyAudio ? {
				audioUrl: null
			} : undefined
		},
		include: {
			translations: true
		}
	});

	const shuffledSentences = _.shuffle(dataSentences).slice(0, dto.count);

	return shuffledSentences.map((s, index) => mapper<SentenceInTaskDto, unknown>({
		...s,
		displayAsText: 'text',
		index: index + 1
	})).sort((a, b) => a.index - b.index);
};


export const createUpdateSentence = async (sentence: SentenceDto, userId: string) => {
	const translations = sentence.translations.map((element) => _.omit(element, ['sentenceId']));
	const data = await db.sentences.upsert({
		where: {
			id: sentence.id
		},
		create: {
			id: sentence.id,
			value: sentence.value,
			level: sentence.level,
			sex: sentence.sex,
			audioUrl: sentence.audioUrl,
			isFavorite: sentence.isFavorite,
			translations: {
				create: translations
			},
			createdBy: userId,
			updatedBy: userId
		},
		update: {
			value: sentence.value,
			level: sentence.level,
			sex: sentence.sex,
			audioUrl: sentence.audioUrl,
			isFavorite: sentence.isFavorite,
			translations: {
				upsert: composeUpsertSentencesQuery(sentence)
			},
			updatedBy: userId,
			updatedAt: new Date()
		},
		include: {
			translations: true
		}
	});

	const checkKeysArray = Object.keys(appToRedisSearchPrefix); // Get the keys from the object
	const searchWithRedis = checkKeysArray.includes(config.public.header); // Check if config.public.header is one of the keys
	const sentenceRedisSchema = searchWithRedis ? appToRedisSearchPrefix[config.public.header as keyof typeof appToRedisSearchPrefix] : undefined;
	if (searchWithRedis && sentenceRedisSchema) {
		try {
			console.log('=======going to update sentence IN REDIS');

			const sentenceRepository = await getSentenceRepository(sentenceRedisSchema);
			await sentenceRepository.save(sentence.id, { id: sentence.id, value: removeVowels(sentence.value) });
		} catch (error) {
			console.log('error while updating sentence in redis', error);
		}
	}
	return mapper<SentenceDto, unknown>(data);
};

export const deleteSentence = async (id: string) => {
	const translationsDelete = db.translations.deleteMany({
		where: {
			sentenceId: id
		}
	});

	const taskSentenceDelete = db.task_sentences.deleteMany({
		where: {
			sentenceId: id
		}
	});

	const deleteSentence = db.sentences.delete({
		where: {
			id
		},
		include: {
			translations: true
		}
	});

	const deleteComplaints = db.complaints.deleteMany({
		where: {
			sentenceId: id
		}
	});

	await db.$transaction([translationsDelete, taskSentenceDelete, deleteSentence, deleteComplaints]);

	return { success: true };
};


const composeSearchWhereClause = (filter: SentenceFilterAfterFulltextDto): Record<string, any> => {
		const whereClause: Record<string, any> = {};

		whereClause.translations =
			filter.lang != -1
				? {
					some: {
						lang: filter.lang,
						NOT: {
							value: ' '
						}
					}
				}
				: undefined;

		if (filter.audio > -1) {
			whereClause.audioUrl =
				filter.audio == 1
					? {
						not: null
					}
					: null;
		}
		if (filter.level > -1) {
			whereClause.level = filter.level;
		}

		if (filter.updatedBy) {
			whereClause.updatedBy = filter.updatedBy;
		}


		if (filter.isFavorite) {
			whereClause.isFavorite = true;
		}

		if (filter.ids && filter.ids.length > 0) {
			whereClause.id = {
				in: filter.ids
			};
		} else if (filter.search) {
			const searchString = `${filter.search.replace('+', ' ')}`.trim();
			whereClause.OR = [{
				value:
					{ search: `"${searchString}"` }
			},
				{
					value:
						{ search: `+${searchString.split(' ').join(' +')}` }
				}];
		}

		console.log('whereClause.value', whereClause.value);

		return whereClause;
	}
;

const composeUpsertSentencesQuery = (
	sentence: SentenceDto
): {
	create: { id: string; value: string; lang: string };
	update: { id: string; value: string; lang: string };
	where: { id: string };
}[] => {
	return sentence.translations.map((x) => ({
		create: {
			id: x.id,
			value: x.value,
			lang: x.lang
		},
		update: {
			id: x.id,
			value: x.value,
			lang: x.lang
		},
		where: {
			id: x.id
		}
	}));
};

export const updateAudio = async (file: File) => {
	if (file.size >= config.s3.AUDIOFILE_SIZE_LIMIT) throw new Error('File is too large...');

	const uploadResult = await uploadAudio(file);
	if (uploadResult) {
		return file.name;
	} else {
		throw new Error('Something went wrong with uploading audio...');
	}
};

import type {RequestEvent} from "@sveltejs/kit";
import {wrapFunc} from "$api/core/misc/response-wrapper";
import {createStudentSurvey, showTheSurvey} from "$api/core/services/group.service";


export const GET = async ({ locals}: RequestEvent) => wrapFunc(async () => {
    const currentGroup = locals?.user?.currentGroup;
    const id = locals?.user?.id;
    return await showTheSurvey(id, currentGroup);
});


export const POST = async (e: RequestEvent) =>
    wrapFunc(async () => {
        const data = await e.request.json();
        const dto = {studentId: data?.studentId}
        return await createStudentSurvey(dto);
    })

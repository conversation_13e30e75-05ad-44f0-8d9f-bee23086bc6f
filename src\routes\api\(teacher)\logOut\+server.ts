import type { RequestEvent } from '@sveltejs/kit';
import { logout } from '$api/core/services/auth.service';
import { redirect } from '@sveltejs/kit';
import { Constants } from '$api/core/constants';

export const POST = async (event: RequestEvent) => {
	logout(event);

	if (
		event.locals.user.role === Constants.AdminRole ||
		event.locals.user.role === Constants.TeacherRole
	) {
		throw redirect(302, '/login');
	} else {
		throw redirect(302, '/signin');
	}
};

import {Constants} from '../constants';
import type {<PERSON>le} from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import {checkUser, logout, redirectToLogin, refreshStudentToken, refreshTeacherToken} from '../services/auth.service';
import {config} from '../config';
import {UnprotectedRoutes} from "$common/core/routes";

export const authenticationMiddleware: Handle = async ({ event, resolve }) => {
	// First, determine if the current path is an unprotected route
	const path = event.url.pathname;
	const isUnprotectedRoute = UnprotectedRoutes.some(route => path === route || path.startsWith(`${route}`));
	
	// Try to get user data from auth cookie for ALL routes
	let hasValidUser = false;
	
	try {
		const authCookie = event.cookies?.get(Constants.AuthCookieName);
		
		if (authCookie) {
			// Extract token from cookie - handle malformed cookies gracefully
			let token;
			if (authCookie.startsWith('Bearer ')) {
				token = authCookie.substring(7);
			} else {
				token = authCookie;
			}
			
			if (token && token.trim() !== '') {
				try {
					// Try to verify the token
					const userData = jwt.verify(token, config.auth.JWT_SECRET);
					event.locals.user = userData;
					console.log('auth middleware user data:', userData);
					const decodedToken = jwt.decode(token) as any;
					if (decodedToken?.id && (decodedToken.role === Constants.AdminRole || decodedToken.role === Constants.AdminRole)) {
						const userStatus = await checkUser(decodedToken.id);
						if (userStatus && userStatus === 'disabled') {
							console.log('User is disabled, logging out');
							logout(event);
						}
					}
					hasValidUser = true;
				} catch (tokenError) {
					// If token is expired, try to refresh it
					if ((tokenError as Error).name === Constants.TokenExpiredError) {
						try {
							const decodedToken = jwt.decode(token) as any;
							
							if (decodedToken?.id && decodedToken?.role) {
								try {
									// Try to refresh the token
									if (decodedToken.role === Constants.StudentRole) {
										await refreshStudentToken(decodedToken.id, event);
									} else {
										await refreshTeacherToken(decodedToken.id, event);
									}
									
									if (event.locals.user) {
										console.log('Token refreshed successfully');
										hasValidUser = true;
									}
								} catch (refreshError) {
									console.log('Token refresh failed:', refreshError);
									// Don't delete cookie here yet, only handle after the route check
								}
							}
						} catch (decodeError) {
							console.log('Failed to decode token:', decodeError);
							// Invalid token structure, will be handled below
						}
					} else {
						console.log('Token validation failed:', tokenError);
						// Other token validation issues, will be handled below
					}
				}
			}
		}
	} catch (e) {
		console.error('Unexpected error in auth process:', e);
		// Don't delete cookie immediately, handle after route check
	}
	
	// Now make routing decision based on authentication status and route protection
	if (hasValidUser) {
		// User is authenticated, proceed to the next handler
		return resolve(event);
	} else {
		// No valid user data
		if (isUnprotectedRoute) {
			// Allow access to unprotected routes even without authentication
			return resolve(event);
		} else {
			// Protected route with no valid authentication - clean up and redirect
			console.log('No valid authentication for protected route:', event.url.href);
			event.cookies.delete(Constants.AuthCookieName, { path: '/' });
			return redirectToLogin(event);
		}
	}
};
